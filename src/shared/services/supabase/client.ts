import { createBrowserClient } from "@supabase/ssr";
import { Database } from "@/shared/types/supabase"; // We'll generate this file next

// Define a more flexible type for the Supabase client that allows any RPC function name
type SupabaseClient = ReturnType<typeof createBrowserClient<Database>> & {
  rpc<T = unknown>(
    fn: string,
    params?: Record<string, unknown>
  ): Promise<{ data: T | null; error: Error | null }>;
};

export function createClient(): SupabaseClient {
  // Create a supabase client on the browser with project's credentials
  const client = createBrowserClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );

  // Return the client with the more flexible RPC type
  return client as SupabaseClient;
}
