import { createClient } from '@/shared/services/supabase/client'

export interface AvatarUploadResult {
  success: boolean
  url?: string
  error?: string
}

export class AvatarService {
  private supabase = createClient()

  async uploadAvatar(file: File, userId: string): Promise<AvatarUploadResult> {
    try {
      console.log('🔄 Starting avatar upload for user:', userId)
      
      // Validate file type
      if (!file.type.startsWith('image/')) {
        return { success: false, error: 'Please select an image file' }
      }

      // Validate file size (5MB max)
      if (file.size > 5 * 1024 * 1024) {
        return { success: false, error: 'File size must be less than 5MB' }
      }

      // Skip bucket check since we know it exists and proceed directly to upload
      console.log('📁 Attempting upload to avatars bucket (skipping bucket check due to permissions)')

      // Generate unique filename
      const fileExt = file.name.split('.').pop()
      const fileName = `${userId}/avatar.${fileExt}`

      console.log('📁 Uploading to path:', fileName)

      // Delete existing avatar if it exists
      await this.deleteAvatar(userId)

      // Upload new avatar
      const { error: uploadError, data: uploadData } = await this.supabase.storage
        .from('avatars')
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: true
        })

      if (uploadError) {
        console.error('❌ Upload error:', uploadError)
        return { success: false, error: `Upload failed: ${uploadError.message}` }
      }

      console.log('✅ Upload successful:', uploadData)

      // Get public URL
      const { data: { publicUrl } } = this.supabase.storage
        .from('avatars')
        .getPublicUrl(fileName)

      console.log('🔗 Public URL:', publicUrl)

      return { success: true, url: publicUrl }
    } catch (error) {
      console.error('❌ Unexpected error:', error)
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Upload failed' 
      }
    }
  }

  async deleteAvatar(userId: string): Promise<void> {
    try {
      const { data: files } = await this.supabase.storage
        .from('avatars')
        .list(userId)

      if (files && files.length > 0) {
        const filePaths = files.map(file => `${userId}/${file.name}`)
        await this.supabase.storage.from('avatars').remove(filePaths)
      }
    } catch {
      // Ignore errors when deleting - file might not exist
    }
  }

  getAvatarUrl(userId: string, fileName: string): string {
    const { data: { publicUrl } } = this.supabase.storage
      .from('avatars')
      .getPublicUrl(`${userId}/${fileName}`)
    
    return publicUrl
  }
}