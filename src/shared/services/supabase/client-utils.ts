// This file contains utilities for client-side Supabase operations
// It should only import from client-safe modules

import { createBrowserClient } from '@supabase/ssr';
import type { Database } from '@/shared/types/supabase';

/**
 * Creates a Supabase client for use in Client Components
 * This is safe to use on the client side
 */
export function createClientComponentClient() {
  return createBrowserClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );
}

/**
 * Client-side transaction interface
 * This mirrors the server-side Transaction interface but is safe to import in client components
 */
export interface ClientTransaction {
  id?: string;
  user_id?: string;
  amount: number;
  type: 'income' | 'expense';
  date: string;
  description: string;
  category: string | null; // Using category name directly
  category_id?: string | null; // Optional ID reference to categories table
  account_id?: string;
  created_at?: string;
  updated_at?: string;
  categories?: { // Join data from categories table
    id: string;
    name: string;
    type: string;
  };
}

/**
 * Client-side transaction filters interface
 * This mirrors the server-side TransactionFilters interface but is safe to import in client components
 */
export interface ClientTransactionFilters {
  startDate?: string;
  endDate?: string;
  type?: 'income' | 'expense' | 'all';
  category?: string;
  account_id?: string;
  search?: string;
}

/**
 * Client-side category interface
 */
export interface Category {
  id: string;
  name: string;
  type: string;
}

/**
 * Static list of transaction categories
 * This can be used client-side without making a server request
 */
export function getClientCategories(): Category[] {
  return [
    // Income categories
    { id: 'salary', name: 'Salary', type: 'income' },
    { id: 'freelance', name: 'Freelance', type: 'income' },
    { id: 'investments', name: 'Investments', type: 'income' },
    { id: 'gifts', name: 'Gifts', type: 'income' },
    { id: 'other_income', name: 'Other Income', type: 'income' },
    
    // Expense categories
    { id: 'housing', name: 'Housing', type: 'expense' },
    { id: 'utilities', name: 'Utilities', type: 'expense' },
    { id: 'groceries', name: 'Groceries', type: 'expense' },
    { id: 'dining', name: 'Dining', type: 'expense' },
    { id: 'transportation', name: 'Transportation', type: 'expense' },
    { id: 'healthcare', name: 'Healthcare', type: 'expense' },
    { id: 'entertainment', name: 'Entertainment', type: 'expense' },
    { id: 'shopping', name: 'Shopping', type: 'expense' },
    { id: 'personal', name: 'Personal', type: 'expense' },
    { id: 'education', name: 'Education', type: 'expense' },
    { id: 'subscriptions', name: 'Subscriptions', type: 'expense' },
    { id: 'travel', name: 'Travel', type: 'expense' },
    { id: 'debt', name: 'Debt Payments', type: 'expense' },
    { id: 'other_expense', name: 'Other Expense', type: 'expense' },
  ];
}
