'use server';

import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { createAdminClient } from './admin';

/**
 * Creates a Supabase client for use in Server Actions
 * This should only be used in server-side code
 */
export async function createServerActionClient() {
  const cookieStore = await cookies();
  
  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name) {
          return cookieStore.get(name)?.value;
        },
        set() {
          // Server Actions can't set cookies
        },
        remove() {
          // Server Actions can't remove cookies
        },
      },
    }
  );
}

export interface InviteToFamilyGroupResponse {
  success: boolean;
  data?: {
    invitation_id: string;
    token: string;
    invitation_link: string;
    expires_at: string;
    email_sent: boolean;
  };
  error?: string;
}

/**
 * Server action to invite a user to a family group using Supabase native email
 * @param groupId - The ID of the family group
 * @param inviteeEmail - Email of the user to invite
 * @returns Promise with invitation details and email send status
 */
export async function inviteToFamilyGroupWithEmail(
  groupId: string,
  inviteeEmail: string
): Promise<InviteToFamilyGroupResponse> {
  try {
    const supabase = await createServerActionClient();
    const supabaseAdmin = createAdminClient();

    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return {
        success: false,
        error: 'User must be authenticated to send invitations',
      };
    }

    // Get group details and verify user has permission
    const { data: groupDetails, error: groupError } = await supabase.rpc(
      'get_family_group_details',
      { p_group_id: groupId }
    );

    if (groupError || !groupDetails) {
      return {
        success: false,
        error: 'Group not found or access denied',
      };
    }

    // Get user profile information
    const { data: userProfile } = await supabase
      .from('user_profiles')
      .select('username')
      .eq('id', user.id)
      .single();

    // Create invitation record first
    const { data: invitationData, error: inviteError } = await supabase.rpc(
      'invite_to_family_group',
      {
        p_group_id: groupId,
        p_invitee_email: inviteeEmail,
      }
    );

    if (inviteError) {
      return {
        success: false,
        error: inviteError.message || 'Failed to create invitation',
      };
    }

    if (!invitationData) {
      return {
        success: false,
        error: 'No invitation data returned from database',
      };
    }

    // Use Supabase native email invitation with family group context
    const { error: emailError } = await supabaseAdmin.auth.admin.inviteUserByEmail(
      inviteeEmail,
      {
        data: {
          invitationType: 'family_group',
          groupId: groupId,
          groupName: groupDetails.group_name,
          inviterName: userProfile?.username || 'A family member',
          inviterEmail: user.email,
          invitationToken: invitationData.token,
        },
        redirectTo: `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/auth/callback?next=/invite/${invitationData.token}`,
      }
    );

    if (emailError) {
      console.error('Error sending Supabase invitation email:', emailError);
      return {
        success: true, // Invitation was created, but email failed
        data: {
          invitation_id: invitationData.invitation_id,
          token: invitationData.token,
          invitation_link: invitationData.invitation_link,
          expires_at: invitationData.expires_at,
          email_sent: false,
        },
        error: 'Invitation created but email could not be sent',
      };
    }

    return {
      success: true,
      data: {
        invitation_id: invitationData.invitation_id,
        token: invitationData.token,
        invitation_link: invitationData.invitation_link,
        expires_at: invitationData.expires_at,
        email_sent: true,
      },
    };
  } catch (error) {
    console.error('Error in inviteToFamilyGroupWithEmail:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'An unexpected error occurred',
    };
  }
}
