// src/types/index.ts

/**
 * Represents a category for transactions and budgets.
 */
export interface Category {
  id: string;
  name: string;
  type: "income" | "expense"; // Type of category
  color?: string; // Optional color for UI representation
  user_id?: string; // If categories are user-specific or global
  // Add other fields from your 'categories' table as needed
}

/**
 * Budget period types
 */
export type BudgetPeriod =
  | "monthly"
  | "quarterly"
  | "yearly"
  | "one-time"
  | "weekly";

/**
 * Represents a budget created by a user.
 */
export interface Budget {
  id: string;
  name: string;
  amount: number;
  period: BudgetPeriod;
  category_id?: string;
  currency?: string;
  start_date: string;
  end_date?: string;
  created_at: string;
  updated_at?: string;
  spent?: number;
  remaining?: number;
  progress?: number;
  user_id?: string;
  category_name?: string;
  category_color?: string;
}

/**
 * Represents a financial transaction.
 */
export interface Transaction {
  id: string;
  description: string;
  amount: number;
  currency: string;
  type: "income" | "expense";
  category?: string;
  category_id?: string;
  category_name?: string;
  category_color?: string;
  date: string;
  created_at: string;
  updated_at?: string;
  budget_id?: string;
  user_id: string;
  account_id?: string;
}

/**
 * Account types enumeration
 */
export type AccountType =
  | "checking"
  | "savings"
  | "credit_card"
  | "cash"
  | "investment"
  | "other";

/**
 * Account data for form handling
 */
export interface AccountData {
  name: string;
  type: AccountType;
  balance: number;
  currency: string;
  user_id?: string;
  color?: string;
  icon?: string;
  is_active?: boolean;
}

/**
 * Represents a financial account.
 */
export interface Account {
  id: string;
  user_id: string;
  name: string;
  type: AccountType;
  balance: number;
  currency: string;
  color?: string; // Optional color for UI representation
  icon?: string; // Optional icon name/path for UI representation
  is_active?: boolean; // Whether the account is active
  created_at: string; // ISO date string
  updated_at?: string; // ISO date string
}

/**
 * Represents a financial goal.
 */
export interface FinancialGoal {
  id: string;
  user_id: string;
  name: string;
  target_amount: number;
  current_amount: number;
  currency: string;
  deadline?: string | null; // ISO date string (YYYY-MM-DD)
  icon?: string | null;
  color?: string | null;
  is_active: boolean;
  created_at: string; // ISO datetime string
  updated_at: string; // ISO datetime string
}

/**
 * Goal interface for dashboard usage with computed fields
 */
export interface Goal {
  id: string;
  user_id: string;
  name: string;
  target_amount: number;
  current_amount: number;
  currency: string;
  target_date: string;
  icon?: string | null;
  color?: string | null;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  progress: number;
  daysRemaining: number;
  monthlyRequired: number;
}

// Enum for TransactionType
export enum TransactionType {
  income = "income",
  expense = "expense",
  transfer = "transfer",
}

export * from "./types";
export * from "./supabase";
export * from "./family";
export * from "./subscriptions";
