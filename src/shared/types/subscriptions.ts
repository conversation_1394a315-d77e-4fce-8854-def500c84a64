// Subscription management types
export interface Subscription {
  id: string;
  user_id: string;
  family_group_id?: string;
  name: string;
  description?: string;
  category_id?: string;
  amount: number;
  currency: string;
  billing_cycle: "weekly" | "monthly" | "quarterly" | "yearly";
  start_date: string;
  end_date?: string;
  next_billing_date: string;
  is_active: boolean;
  auto_renew: boolean;
  provider?: string;
  account_id?: string;
  payment_method?: string;
  reminder_days: number;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface SubscriptionWithDetails extends Subscription {
  category_name?: string;
  category_icon?: string;
  account_name?: string;
  created_by_username?: string;
  days_until_payment: number;
  monthly_cost: number; // Normalized to monthly for comparison
}

export interface SubscriptionSummary {
  total_monthly_cost: number;
  active_subscriptions: number;
  upcoming_payments_7_days: number;
  expiring_soon: number;
}

export interface CreateSubscriptionData {
  name: string;
  description?: string;
  amount: number;
  currency?: string;
  billing_cycle: "weekly" | "monthly" | "quarterly" | "yearly";
  start_date: string;
  end_date?: string;
  category_id?: string;
  account_id?: string;
  provider?: string;
  payment_method?: string;
  reminder_days?: number;
  notes?: string;
  family_group_id?: string;
}

export interface UpdateSubscriptionData
  extends Partial<CreateSubscriptionData> {
  is_active?: boolean;
  auto_renew?: boolean;
}

export interface UpcomingPayment {
  id: string;
  name: string;
  amount: number;
  currency: string;
  next_billing_date: string;
  days_until_payment: number;
  provider?: string;
  category_name?: string;
  is_family: boolean;
}

export interface SubscriptionNotification {
  id: string;
  subscription_id: string;
  notification_type:
    | "upcoming_payment"
    | "payment_failed"
    | "renewal_reminder"
    | "cancellation_reminder";
  scheduled_for: string;
  sent_at?: string;
  is_sent: boolean;
  created_at: string;
}

// Predefined subscription providers with their details
export const SUBSCRIPTION_PROVIDERS = [
  { name: "Netflix", category: "entertainment", icon: "clapperboard" },
  { name: "Spotify", category: "entertainment", icon: "music" },
  { name: "Apple Music", category: "entertainment", icon: "music" },
  { name: "Amazon Prime", category: "shopping", icon: "package" },
  { name: "YouTube Premium", category: "entertainment", icon: "clapperboard" },
  { name: "Disney+", category: "entertainment", icon: "clapperboard" },
  { name: "Hulu", category: "entertainment", icon: "clapperboard" },
  { name: "HBO Max", category: "entertainment", icon: "clapperboard" },
  { name: "Paramount+", category: "entertainment", icon: "clapperboard" },
  { name: "Apple iCloud", category: "technology", icon: "cloud" },
  { name: "Google Drive", category: "technology", icon: "cloud" },
  { name: "Microsoft 365", category: "technology", icon: "laptop" },
  { name: "Adobe Creative Cloud", category: "technology", icon: "palette" },
  { name: "Canva Pro", category: "technology", icon: "palette" },
  { name: "Dropbox", category: "technology", icon: "cloud" },
  { name: "GitHub", category: "technology", icon: "laptop" },
  { name: "Slack", category: "technology", icon: "message-circle" },
  { name: "Zoom", category: "technology", icon: "video" },
  { name: "Notion", category: "productivity", icon: "file-text" },
  { name: "Evernote", category: "productivity", icon: "file-text" },
  { name: "Gym Membership", category: "health", icon: "dumbbell" },
  { name: "Planet Fitness", category: "health", icon: "dumbbell" },
  { name: "LA Fitness", category: "health", icon: "dumbbell" },
  { name: "Peloton", category: "health", icon: "bike" },
  { name: "Headspace", category: "health", icon: "brain" },
  { name: "Calm", category: "health", icon: "brain" },
  { name: "MyFitnessPal", category: "health", icon: "apple" },
  { name: "Electricity", category: "utilities", icon: "zap" },
  { name: "Gas", category: "utilities", icon: "flame" },
  { name: "Water", category: "utilities", icon: "droplets" },
  { name: "Internet", category: "utilities", icon: "globe" },
  { name: "Phone", category: "utilities", icon: "smartphone" },
  { name: "Insurance", category: "insurance", icon: "shield" },
  { name: "Car Insurance", category: "insurance", icon: "car" },
  { name: "Health Insurance", category: "insurance", icon: "heart-pulse" },
  { name: "Home Insurance", category: "insurance", icon: "home" },
  { name: "Life Insurance", category: "insurance", icon: "shield" },
  { name: "Rent", category: "housing", icon: "home" },
  { name: "Mortgage", category: "housing", icon: "home" },
  { name: "HOA Fees", category: "housing", icon: "building-2" },
] as const;

export type BillingCycle = "weekly" | "monthly" | "quarterly" | "yearly";

export const BILLING_CYCLE_LABELS: Record<BillingCycle, string> = {
  weekly: "Weekly",
  monthly: "Monthly",
  quarterly: "Quarterly",
  yearly: "Yearly",
};

export const BILLING_CYCLE_MULTIPLIERS: Record<BillingCycle, number> = {
  weekly: 4.33, // Convert to monthly
  monthly: 1,
  quarterly: 1 / 3,
  yearly: 1 / 12,
};
