// Family group types
export interface FamilyGroup {
  id: string;
  name: string;
  description?: string;
  created_at: string;
  updated_at: string;
  created_by: string;
}

export interface FamilyGroupMember {
  id: string;
  group_id: string;
  user_id: string;
  role: 'admin' | 'member';
  joined_at: string;
  email?: string;
}

export interface FamilyGroupInvitation {
  id: string;
  group_id: string;
  email: string;
  token: string;
  status: 'pending' | 'accepted' | 'expired';
  invited_by: string;
  created_at: string;
  expires_at: string;
  invited_by_user_id?: string;
  invitee_email?: string;
}

export interface FamilyGroupDetails {
  group_id: string;
  group_name: string;
  group_created_at: string;
  members: FamilyGroupMember[];
}

export interface UserFamilyGroupRpcResponseItem {
  group_id: string;
  group_name: string;
  user_role: string;
  created_at: string;
}

export interface PendingInvitation {
  invitation_id: string;
  group_id: string;
  group_name: string;
  invited_by_user_id: string;
  invited_by_user_email: string;
  invited_at: string;
  invitation_status: string;
}

export interface RespondToInvitationResponse {
  success: boolean;
  message: string;
}