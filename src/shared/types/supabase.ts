export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export type Database = {
  public: {
    Tables: {
      accounts: {
        Row: {
          balance: number;
          color: string | null;
          created_at: string;
          created_by_user_id: string | null;
          currency: string;
          family_group_id: string | null;
          icon: string | null;
          id: string;
          is_active: boolean | null;
          name: string;
          type: string;
          updated_at: string;
          user_id: string;
        };
        Insert: {
          balance?: number;
          color?: string | null;
          created_at?: string;
          created_by_user_id?: string | null;
          currency: string;
          family_group_id?: string | null;
          icon?: string | null;
          id?: string;
          is_active?: boolean | null;
          name: string;
          type: string;
          updated_at?: string;
          user_id: string;
        };
        Update: {
          balance?: number;
          color?: string | null;
          created_at?: string;
          created_by_user_id?: string | null;
          currency?: string;
          family_group_id?: string | null;
          icon?: string | null;
          id?: string;
          is_active?: boolean | null;
          name?: string;
          type?: string;
          updated_at?: string;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "accounts_user_id_fkey";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "user_profiles";
            referencedColumns: ["id"];
          }
        ];
      };
      analytics_events: {
        Row: {
          created_at: string | null;
          event_data: Json | null;
          event_type: string;
          id: string;
          user_id: string;
        };
        Insert: {
          created_at?: string | null;
          event_data?: Json | null;
          event_type: string;
          id?: string;
          user_id: string;
        };
        Update: {
          created_at?: string | null;
          event_data?: Json | null;
          event_type?: string;
          id?: string;
          user_id?: string;
        };
        Relationships: [];
      };
      budgets: {
        Row: {
          amount: number;
          category_id: string | null;
          color: string | null;
          created_at: string;
          created_by_user_id: string | null;
          currency: string | null;
          end_date: string | null;
          family_group_id: string | null;
          icon: string | null;
          id: string;
          name: string;
          period: string;
          start_date: string;
          updated_at: string;
          user_id: string;
        };
        Insert: {
          amount: number;
          category_id?: string | null;
          color?: string | null;
          created_at?: string;
          created_by_user_id?: string | null;
          currency?: string | null;
          end_date?: string | null;
          family_group_id?: string | null;
          icon?: string | null;
          id?: string;
          name: string;
          period: string;
          start_date: string;
          updated_at?: string;
          user_id: string;
        };
        Update: {
          amount?: number;
          category_id?: string | null;
          color?: string | null;
          created_at?: string;
          created_by_user_id?: string | null;
          currency?: string | null;
          end_date?: string | null;
          family_group_id?: string | null;
          icon?: string | null;
          id?: string;
          name?: string;
          period?: string;
          start_date?: string;
          updated_at?: string;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "budgets_category_id_fkey";
            columns: ["category_id"];
            isOneToOne: false;
            referencedRelation: "categories";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "budgets_user_id_fkey";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "user_profiles";
            referencedColumns: ["id"];
          }
        ];
      };
      categories: {
        Row: {
          color: string | null;
          created_at: string;
          created_by_user_id: string | null;
          family_group_id: string | null;
          icon: string | null;
          id: string;
          name: string;
          parent_id: string | null;
          type: string;
          updated_at: string;
          user_id: string;
        };
        Insert: {
          color?: string | null;
          created_at?: string;
          created_by_user_id?: string | null;
          family_group_id?: string | null;
          icon?: string | null;
          id?: string;
          name: string;
          parent_id?: string | null;
          type: string;
          updated_at?: string;
          user_id: string;
        };
        Update: {
          color?: string | null;
          created_at?: string;
          created_by_user_id?: string | null;
          family_group_id?: string | null;
          icon?: string | null;
          id?: string;
          name?: string;
          parent_id?: string | null;
          type?: string;
          updated_at?: string;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "categories_parent_id_fkey";
            columns: ["parent_id"];
            isOneToOne: false;
            referencedRelation: "categories";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "categories_user_id_fkey";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "user_profiles";
            referencedColumns: ["id"];
          }
        ];
      };
      financial_goals: {
        Row: {
          color: string | null;
          created_at: string;
          created_by_user_id: string | null;
          currency: string | null;
          current_amount: number;
          deadline: string | null;
          family_group_id: string | null;
          icon: string | null;
          id: string;
          is_active: boolean | null;
          name: string;
          status: string | null;
          target_amount: number;
          target_date: string | null;
          updated_at: string;
          user_id: string;
        };
        Insert: {
          color?: string | null;
          created_at?: string;
          created_by_user_id?: string | null;
          currency?: string | null;
          current_amount?: number;
          deadline?: string | null;
          family_group_id?: string | null;
          icon?: string | null;
          id?: string;
          is_active?: boolean | null;
          name: string;
          status?: string | null;
          target_amount: number;
          target_date?: string | null;
          updated_at?: string;
          user_id: string;
        };
        Update: {
          color?: string | null;
          created_at?: string;
          created_by_user_id?: string | null;
          currency?: string | null;
          current_amount?: number;
          deadline?: string | null;
          family_group_id?: string | null;
          icon?: string | null;
          id?: string;
          is_active?: boolean | null;
          name?: string;
          status?: string | null;
          target_amount?: number;
          target_date?: string | null;
          updated_at?: string;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "financial_goals_user_id_fkey";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "user_profiles";
            referencedColumns: ["id"];
          }
        ];
      };
      transactions: {
        Row: {
          account_id: string;
          amount: number;
          category: string | null;
          category_id: string | null;
          color: string | null;
          created_at: string;
          created_by_user_id: string | null;
          currency: string | null;
          date: string;
          description: string | null;
          family_group_id: string | null;
          icon: string | null;
          id: string;
          notes: string | null;
          type: string;
          updated_at: string;
          user_id: string;
        };
        Insert: {
          account_id: string;
          amount: number;
          category?: string | null;
          category_id?: string | null;
          color?: string | null;
          created_at?: string;
          created_by_user_id?: string | null;
          currency?: string | null;
          date: string;
          description?: string | null;
          family_group_id?: string | null;
          icon?: string | null;
          id?: string;
          notes?: string | null;
          type: string;
          updated_at?: string;
          user_id: string;
        };
        Update: {
          account_id?: string;
          amount?: number;
          category?: string | null;
          category_id?: string | null;
          color?: string | null;
          created_at?: string;
          created_by_user_id?: string | null;
          currency?: string | null;
          date?: string;
          description?: string | null;
          family_group_id?: string | null;
          icon?: string | null;
          id?: string;
          notes?: string | null;
          type?: string;
          updated_at?: string;
          user_id?: string;
        };
        Relationships: [
          {
            foreignKeyName: "transactions_account_id_fkey";
            columns: ["account_id"];
            isOneToOne: false;
            referencedRelation: "accounts";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "transactions_category_id_fkey";
            columns: ["category_id"];
            isOneToOne: false;
            referencedRelation: "categories";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "transactions_user_id_fkey";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "user_profiles";
            referencedColumns: ["id"];
          }
        ];
      };
      user_profiles: {
        Row: {
          avatar_url: string | null;
          created_at: string;
          currency: string;
          dark_mode: boolean | null;
          email: string | null;
          full_name: string | null;
          id: string;
          language: string;
          notifications_enabled: boolean | null;
          updated_at: string;
          username: string | null;
        };
        Insert: {
          avatar_url?: string | null;
          created_at?: string;
          currency?: string;
          dark_mode?: boolean | null;
          email?: string | null;
          full_name?: string | null;
          id: string;
          language?: string;
          notifications_enabled?: boolean | null;
          updated_at?: string;
          username?: string | null;
        };
        Update: {
          avatar_url?: string | null;
          created_at?: string;
          currency?: string;
          dark_mode?: boolean | null;
          email?: string | null;
          full_name?: string | null;
          id?: string;
          language?: string;
          notifications_enabled?: boolean | null;
          updated_at?: string;
          username?: string | null;
        };
        Relationships: [];
      };
      family_groups: {
        Row: {
          id: string;
          name: string;
          owner_user_id: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          owner_user_id: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          owner_user_id?: string;
          created_at?: string;
          updated_at?: string;
        };
        Relationships: [];
      };
      family_group_members: {
        Row: {
          group_id: string;
          user_id: string;
          role: string;
          joined_at: string;
        };
        Insert: {
          group_id: string;
          user_id: string;
          role?: string;
          joined_at?: string;
        };
        Update: {
          group_id?: string;
          user_id?: string;
          role?: string;
          joined_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "family_group_members_group_id_fkey";
            columns: ["group_id"];
            isOneToOne: false;
            referencedRelation: "family_groups";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "family_group_members_user_id_fkey";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "user_profiles";
            referencedColumns: ["id"];
          }
        ];
      };
      subscriptions: {
        Row: {
          id: string;
          user_id: string;
          family_group_id: string | null;
          name: string;
          description: string | null;
          category_id: string | null;
          amount: number;
          currency: string;
          billing_cycle: string;
          start_date: string;
          end_date: string | null;
          next_billing_date: string;
          is_active: boolean;
          auto_renew: boolean;
          provider: string | null;
          account_id: string | null;
          payment_method: string | null;
          reminder_days: number;
          notes: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          family_group_id?: string | null;
          name: string;
          description?: string | null;
          category_id?: string | null;
          amount: number;
          currency?: string;
          billing_cycle: string;
          start_date: string;
          end_date?: string | null;
          next_billing_date: string;
          is_active?: boolean;
          auto_renew?: boolean;
          provider?: string | null;
          account_id?: string | null;
          payment_method?: string | null;
          reminder_days?: number;
          notes?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          family_group_id?: string | null;
          name?: string;
          description?: string | null;
          category_id?: string | null;
          amount?: number;
          currency?: string;
          billing_cycle?: string;
          start_date?: string;
          end_date?: string | null;
          next_billing_date?: string;
          is_active?: boolean;
          auto_renew?: boolean;
          provider?: string | null;
          account_id?: string | null;
          payment_method?: string | null;
          reminder_days?: number;
          notes?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "subscriptions_user_id_fkey";
            columns: ["user_id"];
            isOneToOne: false;
            referencedRelation: "user_profiles";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "subscriptions_family_group_id_fkey";
            columns: ["family_group_id"];
            isOneToOne: false;
            referencedRelation: "family_groups";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "subscriptions_category_id_fkey";
            columns: ["category_id"];
            isOneToOne: false;
            referencedRelation: "categories";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "subscriptions_account_id_fkey";
            columns: ["account_id"];
            isOneToOne: false;
            referencedRelation: "accounts";
            referencedColumns: ["id"];
          }
        ];
      };
      subscription_notifications: {
        Row: {
          id: string;
          subscription_id: string;
          notification_type: string;
          scheduled_for: string;
          sent_at: string | null;
          is_sent: boolean;
          created_at: string;
        };
        Insert: {
          id?: string;
          subscription_id: string;
          notification_type: string;
          scheduled_for: string;
          sent_at?: string | null;
          is_sent?: boolean;
          created_at?: string;
        };
        Update: {
          id?: string;
          subscription_id?: string;
          notification_type?: string;
          scheduled_for?: string;
          sent_at?: string | null;
          is_sent?: boolean;
          created_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "subscription_notifications_subscription_id_fkey";
            columns: ["subscription_id"];
            isOneToOne: false;
            referencedRelation: "subscriptions";
            referencedColumns: ["id"];
          }
        ];
      };
      family_group_invitations: {
        Row: {
          id: string;
          group_id: string;
          invited_by_user_id: string;
          invitee_email: string;
          status: string;
          token: string;
          expires_at: string;
          created_at: string;
        };
        Insert: {
          id?: string;
          group_id: string;
          invited_by_user_id: string;
          invitee_email: string;
          status?: string;
          token: string;
          expires_at: string;
          created_at?: string;
        };
        Update: {
          id?: string;
          group_id?: string;
          invited_by_user_id?: string;
          invitee_email?: string;
          status?: string;
          token?: string;
          expires_at?: string;
          created_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "family_group_invitations_group_id_fkey";
            columns: ["group_id"];
            isOneToOne: false;
            referencedRelation: "family_groups";
            referencedColumns: ["id"];
          }
        ];
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      delete_account: {
        Args: { p_id: string };
        Returns: undefined;
      };
      get_accounts: {
        Args: Record<PropertyKey, never>;
        Returns: {
          id: string;
          user_id: string;
          name: string;
          type: string;
          balance: number;
          currency: string;
          icon: string;
          color: string;
          created_at: string;
          updated_at: string;
        }[];
      };
      insert_account: {
        Args: { p_data: Json };
        Returns: undefined;
      };
      update_account: {
        Args: { p_id: string; p_data: Json };
        Returns: undefined;
      };
      get_user_family_groups: {
        Args: Record<PropertyKey, never>;
        Returns: {
          group_id: string;
          group_name: string;
          user_role: string;
          created_at: string;
        }[];
      };
      get_family_group_members: {
        Args: { p_group_id: string };
        Returns: {
          user_id: string;
          email: string;
          role: string;
          joined_at: string;
        }[];
      };
      get_pending_invitations: {
        Args: { p_group_id: string };
        Returns: {
          invitation_id: string;
          group_id: string;
          group_name: string;
          invited_by_user_id: string;
          invited_by_user_email: string;
          invited_at: string;
          invitation_status: string;
        }[];
      };
      respond_to_invitation: {
        Args: { p_token: string; p_response: string };
        Returns: {
          success: boolean;
          message: string;
        };
      };
      is_family_group_admin: {
        Args: { p_group_id: string; p_user_id: string };
        Returns: boolean;
      };
      expire_old_invitations: {
        Args: Record<PropertyKey, never>;
        Returns: number;
      };
      get_user_subscriptions: {
        Args: Record<PropertyKey, never>;
        Returns: Json;
      };
      get_family_group_subscriptions: {
        Args: { group_uuid: string };
        Returns: Json;
      };
      get_subscription_summary: {
        Args: Record<PropertyKey, never>;
        Returns: Json;
      };
      get_family_subscription_summary: {
        Args: { group_uuid: string };
        Returns: Json;
      };
      create_subscription: {
        Args: {
          subscription_name: string;
          subscription_description?: string | null;
          subscription_amount: number;
          subscription_currency?: string;
          subscription_billing_cycle: string;
          subscription_start_date: string;
          subscription_end_date?: string | null;
          subscription_category_id?: string | null;
          subscription_account_id?: string | null;
          subscription_provider?: string | null;
          subscription_payment_method?: string | null;
          subscription_reminder_days?: number;
          subscription_notes?: string | null;
          subscription_family_group_id?: string | null;
        };
        Returns: string;
      };
      update_subscription: {
        Args: {
          subscription_id: string;
          subscription_name?: string | null;
          subscription_description?: string | null;
          subscription_amount?: number | null;
          subscription_currency?: string | null;
          subscription_billing_cycle?: string | null;
          subscription_start_date?: string | null;
          subscription_end_date?: string | null;
          subscription_category_id?: string | null;
          subscription_account_id?: string | null;
          subscription_provider?: string | null;
          subscription_payment_method?: string | null;
          subscription_reminder_days?: number | null;
          subscription_notes?: string | null;
          subscription_is_active?: boolean | null;
          subscription_auto_renew?: boolean | null;
        };
        Returns: boolean;
      };
      delete_subscription: {
        Args: { subscription_id: string };
        Returns: boolean;
      };
      get_upcoming_payments: {
        Args: { days_ahead: number };
        Returns: Json;
      };
      get_invitation_by_token: {
        Args: { p_token: string };
        Returns: {
          id: string;
          group_id: string;
          invitee_email: string;
          status: string;
          expires_at: string;
        }[];
      };
      create_family_group: {
        Args: { p_group_name: string };
        Returns: string;
      };
    };
    Enums: {
      [_ in never]: never;
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
};

type DefaultSchema = Database[Extract<keyof Database, "public">];

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R;
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
      DefaultSchema["Views"])
  ? (DefaultSchema["Tables"] &
      DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
      Row: infer R;
    }
    ? R
    : never
  : never;

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I;
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
  ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
      Insert: infer I;
    }
    ? I
    : never
  : never;

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U;
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
  ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
      Update: infer U;
    }
    ? U
    : never
  : never;

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
  ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
  : never;

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
  ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
  : never;

export const Constants = {
  public: {
    Enums: {},
  },
} as const;
