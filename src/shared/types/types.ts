// Supabase Database Types

// Account types
export type AccountType = 'checking' | 'savings' | 'credit_card' | 'cash' | 'investment' | 'other';

export interface Account {
  id: string;
  name: string;
  type: AccountType;
  balance: number;
  currency: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  user_id: string;
}

export interface AccountData {
  name: string;
  type: AccountType;
  balance: number;
  currency: string;
  is_active: boolean;
  user_id: string;
}

// Category types
export type CategoryType = 'income' | 'expense';

export interface Category {
  id: string;
  name: string;
  type: CategoryType;
  color: string;
  icon?: string;
  parent_id?: string;
  user_id: string;
  created_at: string;
  updated_at: string;
}

export interface CategoryData {
  name: string;
  type: CategoryType;
  color: string;
  icon?: string;
  parent_id?: string;
  user_id: string;
}

// Budget types
export type BudgetPeriod = 'monthly' | 'quarterly' | 'yearly' | 'one-time' | 'weekly';

export interface Budget {
  id: string;
  name: string;
  amount: number;
  period: BudgetPeriod;
  category_id?: string;
  currency: string;
  start_date: string;
  end_date?: string;
  user_id: string;
  created_at: string;
  updated_at: string;
  spent?: number;
  remaining?: number;
  progress?: number;
  category_name?: string;
  category_color?: string;
}

export interface BudgetData {
  name: string;
  amount: number;
  period: BudgetPeriod;
  category_id?: string;
  currency: string;
  start_date: string;
  end_date?: string;
  user_id: string;
}

// Goal types
export type GoalStatus = 'active' | 'completed' | 'paused';

export interface Goal {
  id: string;
  name: string;
  target_amount: number;
  current_amount: number;
  target_date: string;
  status: GoalStatus;
  description?: string;
  user_id: string;
  created_at: string;
  updated_at: string;
  progress: number;
  daysRemaining: number;
  monthlyRequired: number;
  icon?: string;
}

export interface GoalData {
  name: string;
  target_amount: number;
  current_amount: number;
  target_date: string;
  status: GoalStatus;
  description?: string;
  user_id: string;
}

// Transaction types
export type TransactionType = 'income' | 'expense' | 'transfer'; // transfer might be future use

export interface Transaction {
  id: string;
  user_id: string;
  account_id: string;
  category_id?: string; // Foreign key to categories table
  budget_id?: string;   // Foreign key to budgets table
  description: string;
  amount: number;        // Stored as numeric in DB
  currency: string;
  type: TransactionType;
  date: string;          // ISO date string (YYYY-MM-DD)
  created_at: string;    // ISO datetime string
  updated_at: string;    // ISO datetime string

  // Optional fields populated by joins or client-side logic for display
  category_name?: string;
  category_color?: string;
  account_name?: string;
}

export interface TransactionData { // For inserts/updates
  user_id: string;
  account_id: string;
  category_id?: string;
  budget_id?: string;
  description: string;
  amount: number;
  currency: string;
  type: TransactionType;
  date: string; // ISO date string (YYYY-MM-DD)
}

// User profile types
export interface UserProfile {
  id: string;
  email: string;
  full_name: string;
  avatar_url?: string;
  currency: string;
  created_at: string;
  updated_at: string;
}

// Supabase RPC function types
export interface SupabaseRpcFunctions {
  get_accounts: () => Promise<Account[]>;
  insert_account: (params: { p_data: AccountData }) => Promise<{ id: string }>;
  update_account: (params: { p_id: string; p_data: Partial<AccountData> }) => Promise<void>;
  delete_account: (params: { p_id: string }) => Promise<void>;
  
  get_categories: () => Promise<Category[]>;
  insert_category: (params: { p_data: CategoryData }) => Promise<{ id: string }>;
  update_category: (params: { p_id: string; p_data: Partial<CategoryData> }) => Promise<void>;
  delete_category: (params: { p_id: string }) => Promise<void>;
  
  get_budgets: () => Promise<Budget[]>;
  insert_budget: (params: { p_data: BudgetData }) => Promise<{ id: string }>;
  update_budget: (params: { p_id: string; p_data: Partial<BudgetData> }) => Promise<void>;
  delete_budget: (params: { p_id: string }) => Promise<void>;
  
  get_goals: () => Promise<Goal[]>;
  insert_goal: (params: { p_data: GoalData }) => Promise<{ id: string }>;
  update_goal: (params: { p_id: string; p_data: Partial<GoalData> }) => Promise<void>;
  delete_goal: (params: { p_id: string }) => Promise<void>;
}
