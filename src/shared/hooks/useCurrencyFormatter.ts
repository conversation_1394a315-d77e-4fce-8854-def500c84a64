import { useUserSettings } from '@/shared/contexts/UserSettingsContext';

/**
 * Custom hook that provides currency formatting using the user's preferred currency setting
 * This ensures consistent currency display across the entire application
 */
export function useCurrencyFormatter() {
  const { settings } = useUserSettings();
  
  const formatCurrency = (amount: number, options?: {
    minimumFractionDigits?: number;
    maximumFractionDigits?: number;
    locale?: string;
  }) => {
    const {
      minimumFractionDigits = 2,
      maximumFractionDigits = 2,
      locale = 'en-US'
    } = options || {};
    
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: settings.currency,
      minimumFractionDigits,
      maximumFractionDigits,
    }).format(amount);
  };

  return {
    formatCurrency,
    currency: settings.currency,
  };
}