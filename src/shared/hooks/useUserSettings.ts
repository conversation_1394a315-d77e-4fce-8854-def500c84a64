import { useState, useEffect, useCallback } from "react";
import { createClient } from "@/shared/services/supabase/client";

// Define the shape of your user settings
export interface UserSettings {
  id?: string;
  user_id?: string;
  currency: string;
  // Add other settings fields as needed
}

// Default settings to use when no user_settings table exists or no settings are found
const DEFAULT_SETTINGS: UserSettings = {
  currency: "USD",
};

// Flag to track if we've already detected the table doesn't exist
// This prevents making repeated API calls to a non-existent table
const USER_SETTINGS_TABLE_EXISTS = true;

const supabase = createClient();

export function useUserSettings() {
  const [userSettings, setUserSettings] =
    useState<UserSettings>(DEFAULT_SETTINGS);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchUserSettings = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    // If we already know the table doesn't exist, don't make the API call
    if (!USER_SETTINGS_TABLE_EXISTS) {
      setUserSettings(DEFAULT_SETTINGS);
      setIsLoading(false);
      return;
    }

    try {
      const {
        data: { user },
        error: userError,
      } = await supabase.auth.getUser();
      if (userError) {
        console.warn("User auth error:", userError.message);
        setUserSettings(DEFAULT_SETTINGS);
        setIsLoading(false);
        return;
      }

      if (!user) {
        console.warn("No authenticated user found");
        setUserSettings(DEFAULT_SETTINGS);
        setIsLoading(false);
        return;
      }

      // Fetch user settings from the 'user_profiles' table (assuming settings are there)
      const { data: userProfile, error: profileError } = await supabase
        .from("user_profiles") // Changed from 'user_settings'
        .select("currency") // Changed from default_currency
        .eq("id", user.id)
        .single();

      if (profileError) {
        // Check if the error is because the table doesn't exist (PostgREST code P0002)
        // Or if the user_profiles table doesn't have a currency column (42703)
        if (profileError.code === "PGRST116" || profileError.code === "42703") {
          console.warn(
            `User profile for ${user.id} not found or currency column missing. Using default settings. Error: ${profileError.message}`
          );
          setUserSettings(DEFAULT_SETTINGS);
          // USER_SETTINGS_TABLE_EXISTS = false; // Consider if this logic is still needed or how to adapt it
        } else {
          console.error("Error fetching user settings:", profileError);
          setError(profileError as Error);
          setUserSettings(DEFAULT_SETTINGS); // Fallback to default on other errors
        }
      } else if (userProfile && userProfile.currency) {
        setUserSettings({
          ...DEFAULT_SETTINGS,
          ...userProfile,
          currency: userProfile.currency,
        });
      } else {
        // User profile exists but no currency setting, or currency is null/empty
        console.warn(
          `User profile for ${user.id} does not have currency set or it's invalid. Using default settings.`
        );
        setUserSettings(DEFAULT_SETTINGS);
      }
    } catch (e: unknown) {
      // This catch is for unexpected errors in the try block
      console.warn("Error in settings fetch, using defaults:", e);
      setUserSettings(DEFAULT_SETTINGS);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchUserSettings();
  }, [fetchUserSettings]);

  const updateUserSettings = useCallback(
    async (newSettings: Partial<UserSettings>) => {
      setIsLoading(true);
      try {
        const {
          data: { user },
        } = await supabase.auth.getUser();
        if (!user) throw new Error("User not authenticated");

        // Prepare data for update, ensuring only 'currency' is updated if present
        const updateData: { currency?: string } = {};
        if (newSettings.currency) {
          updateData.currency = newSettings.currency;
        }

        if (Object.keys(updateData).length === 0) {
          console.warn("No valid settings to update.");
          setIsLoading(false);
          return;
        }

        const { data, error } = await supabase
          .from("user_profiles") // Changed from 'user_settings'
          .update(updateData)
          .eq("id", user.id)
          .select("currency") // Ensure we select currency
          .single();

        if (error) throw error;
        if (data) {
          setUserSettings({
            ...DEFAULT_SETTINGS,
            ...data,
            currency: data.currency,
          });
          // toast.success('Settings updated successfully!');
        } else {
          // This case should ideally not happen if the update was successful and data was selected
          // toast.warn('Settings updated, but could not retrieve the latest values. Please refresh.');
          fetchUserSettings(); // Re-fetch to be sure
        }
      } catch (e: unknown) {
        console.error("Error updating settings:", e);
        setError(e as Error);
      } finally {
        setIsLoading(false);
      }
    },
    [fetchUserSettings]
  );

  return {
    userSettings,
    isLoading,
    error,
    refetchUserSettings: fetchUserSettings,
    updateUserSettings,
  };
}
