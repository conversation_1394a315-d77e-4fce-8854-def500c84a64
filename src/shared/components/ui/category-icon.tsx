import React from "react";
import {
  ShoppingCart,
  Home,
  Car,
  Utensils,
  Gamepad2,
  Heart,
  GraduationCap,
  Briefcase,
  Plane,
  Tag,
  Zap,
  HeartPulse,
  Clapperboard,
  ShoppingBag,
  Smartphone,
  Sparkles,
  Shield,
  Calculator,
  CreditCard,
  Gift,
  Package,
  Laptop,
  Building2,
  TrendingUp,
  Landmark,
  Coins,
  Target,
  Undo2,
  Banknote,
  PiggyBank,
  Trees,
  Music,
  Cloud,
  Palette,
  MessageCircle,
  Video,
  FileText,
  Dumbbell,
  Bike,
  Brain,
  Apple,
  Flame,
  Droplets,
  Globe,
  type LucideIcon,
} from "lucide-react";
import { cn } from "@/shared/utils";

// Map of icon strings to Lucide React components
const iconMap: Record<string, LucideIcon> = {
  "shopping-cart": ShoppingCart,
  home: Home,
  car: Car,
  utensils: Utensils,
  "gamepad-2": Gamepad2,
  heart: Heart,
  "graduation-cap": GraduationCap,
  briefcase: Briefcase,
  plane: Plane,
  tag: Tag,
  zap: Zap,
  "heart-pulse": HeartPulse,
  clapperboard: Clapperboard,
  "shopping-bag": ShoppingBag,
  smartphone: Smartphone,
  sparkles: Sparkles,
  shield: Shield,
  calculator: Calculator,
  "credit-card": CreditCard,
  gift: Gift,
  package: Package,
  laptop: Laptop,
  "building-2": Building2,
  "trending-up": TrendingUp,
  landmark: Landmark,
  coins: Coins,
  target: Target,
  "undo-2": Undo2,
  banknote: Banknote,
  "piggy-bank": PiggyBank,
  "palm-tree": Trees,
  music: Music,
  cloud: Cloud,
  palette: Palette,
  "message-circle": MessageCircle,
  video: Video,
  "file-text": FileText,
  dumbbell: Dumbbell,
  bike: Bike,
  brain: Brain,
  apple: Apple,
  flame: Flame,
  droplets: Droplets,
  globe: Globe,
};

interface CategoryIconProps {
  icon: string | null | undefined;
  className?: string;
  fallback?: LucideIcon;
}

export function CategoryIcon({
  icon,
  className = "h-4 w-4",
  fallback = Tag,
}: CategoryIconProps) {
  if (!icon) {
    const FallbackIcon = fallback;
    return <FallbackIcon className={cn(className)} />;
  }

  // If it's a Lucide icon string, use the mapped component
  const IconComponent = iconMap[icon];
  if (IconComponent) {
    return <IconComponent className={cn(className)} />;
  }

  // If it's an emoji or unknown string, fallback to Tag icon
  const FallbackIcon = fallback;
  return <FallbackIcon className={cn(className)} />;
}
