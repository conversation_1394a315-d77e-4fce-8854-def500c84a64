"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/shared/utils";
import {
  LayoutDashboard,
  Receipt,
  CreditCard,
  PieChart,
  Target,
  Settings,
  Wallet,
  TrendingUp,
  ChevronLeft,
  ChevronRight,
  User,
  Mail,
  Home,
  Building2,
} from "lucide-react";
import { Button } from "@/shared/components/ui/button";
import { useMobileMenu } from "@/shared/contexts/mobile-menu-context";
import { useEffect, useState } from "react";
import { ScrollArea } from "@/shared/components/ui/scroll-area";

const SIDEBAR_WIDTH = 16; // rem
const SIDEBAR_WIDTH_COLLAPSED = 4; // rem

const personalNavigation = [
  { name: "Dashboard", href: "/dashboard/personal", icon: LayoutDashboard },
  { name: "Transactions", href: "/dashboard/transactions", icon: Receipt },
  { name: "Subscriptions", href: "/dashboard/subscriptions", icon: CreditCard },
  { name: "Budgets", href: "/dashboard/budgets", icon: Wallet },
  { name: "Categories", href: "/dashboard/categories", icon: <PERSON><PERSON><PERSON> },
  { name: "Goals", href: "/dashboard/goals", icon: Target },
  { name: "Analytics", href: "/dashboard/analytics", icon: TrendingUp },
  { name: "Accounts", href: "/dashboard/accounts", icon: Building2 },
  { name: "Settings", href: "/dashboard/settings", icon: Settings },
];

const familyNavigation = [
  { name: "Dashboard", href: "/dashboard/family", icon: LayoutDashboard },
  { name: "Invitations", href: "/dashboard/invitations", icon: Mail },
  { name: "Settings", href: "/dashboard/settings", icon: Settings },
];

export function Sidebar() {
  const pathname = usePathname();
  const { isOpen, close } = useMobileMenu();
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Determine current mode based on pathname
  const isPersonalMode =
    pathname.startsWith("/dashboard/personal") ||
    (pathname.startsWith("/dashboard/") &&
      !pathname.startsWith("/dashboard/family") &&
      pathname !== "/dashboard" &&
      !pathname.startsWith("/dashboard/invitations"));

  // Extract family group ID from pathname if viewing a specific family group
  const familyGroupMatch = pathname.match(/\/dashboard\/family\/([^\/]+)/);
  const currentFamilyGroupId = familyGroupMatch ? familyGroupMatch[1] : null;
  const isInFamilyGroup = !!currentFamilyGroupId;

  // Get navigation items based on current mode
  const currentNavigation = isPersonalMode
    ? personalNavigation
    : familyNavigation;
  const currentModeTitle = isPersonalMode
    ? "Personal Finance"
    : "Family Finance";

  // Handle mobile view
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 1024);
    };

    checkIfMobile();
    window.addEventListener("resize", checkIfMobile);
    return () => window.removeEventListener("resize", checkIfMobile);
  }, []);

  // Close mobile menu when a link is clicked
  const handleLinkClick = () => {
    if (isMobile) {
      close();
    }
  };

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  const sidebarWidth = isCollapsed ? SIDEBAR_WIDTH_COLLAPSED : SIDEBAR_WIDTH;
  const sidebarClasses = cn(
    "fixed inset-y-0 left-0 z-30 h-screen overflow-y-auto border-r bg-background transition-all duration-300 ease-in-out lg:relative lg:block",
    isMobile
      ? isOpen
        ? "w-64 translate-x-0"
        : "-translate-x-full w-64"
      : `w-${sidebarWidth}`, // This won't work with Tailwind, but we'll handle it with style
    isCollapsed && "lg:w-16"
  );

  const overlayClasses = cn(
    "fixed inset-0 z-20 bg-background/80 backdrop-blur-sm transition-opacity lg:hidden",
    isOpen ? "opacity-100" : "pointer-events-none opacity-0"
  );

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && isMobile && (
        <div className={overlayClasses} onClick={close} aria-hidden="true" />
      )}

      <aside
        className={sidebarClasses}
        style={{
          width: isMobile ? "16rem" : isCollapsed ? "4rem" : "16rem",
          minWidth: isMobile ? "16rem" : isCollapsed ? "4rem" : "16rem",
        }}
      >
        <div className="flex h-full flex-col">
          <div className="flex h-16 items-center justify-between border-b px-4">
            {!isCollapsed && <h2 className="text-lg font-semibold">Budget</h2>}
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={toggleCollapse}
                title={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
              >
                {isCollapsed ? (
                  <ChevronRight className="h-4 w-4" />
                ) : (
                  <ChevronLeft className="h-4 w-4" />
                )}
                <span className="sr-only">
                  {isCollapsed ? "Expand" : "Collapse"} sidebar
                </span>
              </Button>
            </div>
          </div>

          <ScrollArea className="flex-1">
            <nav className="space-y-4 p-2">
              {/* Current Mode Navigation */}
              <div>
                {!isCollapsed && (
                  <h3 className="mb-2 px-3 text-xs font-semibold uppercase tracking-wider text-primary">
                    {currentModeTitle}
                    <span className="ml-2 text-xs bg-primary text-primary-foreground px-1.5 py-0.5 rounded">
                      Active
                    </span>
                  </h3>
                )}
                <div className="space-y-1">
                  {currentNavigation.map((item) => {
                    const isActive =
                      pathname === item.href ||
                      (item.href !== "/dashboard/personal" &&
                        item.href !== "/dashboard/family" &&
                        pathname.startsWith(item.href));
                    return (
                      <Link
                        key={item.name}
                        href={item.href}
                        onClick={handleLinkClick}
                        className={cn(
                          "group flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium transition-colors",
                          isActive
                            ? "bg-primary text-primary-foreground"
                            : "hover:bg-muted"
                        )}
                      >
                        <item.icon className="h-4 w-4 flex-shrink-0" />
                        {!isCollapsed && (
                          <span className="whitespace-nowrap">{item.name}</span>
                        )}
                      </Link>
                    );
                  })}
                </div>
              </div>

              {/* Family Group Specific Navigation */}
              {isInFamilyGroup && (
                <div className="transition-opacity duration-200">
                  {!isCollapsed && (
                    <h3 className="mb-2 px-3 text-xs font-semibold text-primary uppercase tracking-wider">
                      Group Navigation
                      <span className="ml-2 text-xs bg-primary text-primary-foreground px-1.5 py-0.5 rounded">
                        Active
                      </span>
                    </h3>
                  )}
                  <div className="space-y-1">
                    {[
                      {
                        name: "Overview",
                        href: `/dashboard/family/${currentFamilyGroupId}`,
                        icon: Home,
                      },
                      {
                        name: "Accounts",
                        href: `/dashboard/family/${currentFamilyGroupId}/accounts`,
                        icon: Building2,
                      },
                      {
                        name: "Transactions",
                        href: `/dashboard/family/${currentFamilyGroupId}/transactions`,
                        icon: Receipt,
                      },
                      {
                        name: "Subscriptions",
                        href: `/dashboard/family/${currentFamilyGroupId}/subscriptions`,
                        icon: CreditCard,
                      },
                      {
                        name: "Budgets",
                        href: `/dashboard/family/${currentFamilyGroupId}/budgets`,
                        icon: Wallet,
                      },
                      {
                        name: "Goals",
                        href: `/dashboard/family/${currentFamilyGroupId}/goals`,
                        icon: Target,
                      },
                      {
                        name: "Categories",
                        href: `/dashboard/family/${currentFamilyGroupId}/categories`,
                        icon: PieChart,
                      },
                      {
                        name: "Settings",
                        href: `/dashboard/family/${currentFamilyGroupId}/settings`,
                        icon: Settings,
                      },
                    ].map((item) => {
                      const isActive = pathname === item.href;
                      return (
                        <Link
                          key={item.name}
                          href={item.href}
                          onClick={handleLinkClick}
                          className={cn(
                            "group flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium transition-colors",
                            isActive
                              ? "bg-primary text-primary-foreground"
                              : "hover:bg-muted"
                          )}
                        >
                          <item.icon className="h-4 w-4 flex-shrink-0" />
                          {!isCollapsed && (
                            <span className="whitespace-nowrap">
                              {item.name}
                            </span>
                          )}
                        </Link>
                      );
                    })}
                  </div>
                </div>
              )}
            </nav>
          </ScrollArea>

          {!isCollapsed && (
            <div className="border-t p-4">
              <div className="flex items-center gap-3">
                <div className="h-8 w-8 rounded-full bg-muted flex items-center justify-center">
                  <User className="h-4 w-4 text-muted-foreground" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate">User Account</p>
                  <p className="text-xs text-muted-foreground truncate">
                    Free Plan
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </aside>
    </>
  );
}
