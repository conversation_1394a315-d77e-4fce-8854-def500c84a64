"use client";

import React, { createContext, useContext, useState, useEffect } from 'react';
import { usePathname } from 'next/navigation';

export type AppMode = 'personal' | 'family';

interface FamilyGroup {
  id: string;
  name: string;
}

interface AppModeContextType {
  mode: AppMode;
  setMode: (mode: AppMode) => void;
  selectedFamilyGroup: FamilyGroup | null;
  setSelectedFamilyGroup: (group: FamilyGroup | null) => void;
  isPersonalMode: boolean;
  isFamilyMode: boolean;
}

const AppModeContext = createContext<AppModeContextType | undefined>(undefined);

interface AppModeProviderProps {
  children: React.ReactNode;
}

export function AppModeProvider({ children }: AppModeProviderProps) {
  const [mode, setMode] = useState<AppMode>('personal');
  const [selectedFamilyGroup, setSelectedFamilyGroup] = useState<FamilyGroup | null>(null);
  const pathname = usePathname();

  // Auto-switch mode based on current route
  useEffect(() => {
    if (pathname.startsWith('/dashboard/family')) {
      setMode('family');
    } else if (pathname.startsWith('/dashboard') && !pathname.startsWith('/dashboard/family')) {
      setMode('personal');
    }
  }, [pathname]);

  // Auto-switch to family mode when a family group is selected
  useEffect(() => {
    if (selectedFamilyGroup && mode === 'personal') {
      setMode('family');
    }
    // Note: We don't auto-switch back to personal when no family group is selected
    // Users should be able to browse family groups even without selecting one
  }, [selectedFamilyGroup, mode]);

  const value: AppModeContextType = {
    mode,
    setMode,
    selectedFamilyGroup,
    setSelectedFamilyGroup,
    isPersonalMode: mode === 'personal',
    isFamilyMode: mode === 'family',
  };

  return (
    <AppModeContext.Provider value={value}>
      {children}
    </AppModeContext.Provider>
  );
}

export function useAppMode() {
  const context = useContext(AppModeContext);
  if (context === undefined) {
    throw new Error('useAppMode must be used within an AppModeProvider');
  }
  return context;
}