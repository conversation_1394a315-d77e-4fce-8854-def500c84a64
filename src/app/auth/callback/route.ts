import { createServerClient, type CookieOptions } from "@supabase/ssr";
import { NextResponse, type NextRequest } from "next/server";

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const code = searchParams.get("code");
  const next = searchParams.get("next") ?? "/";
  const redirectTo = request.nextUrl.clone();

  if (code) {
    // Create cookie store for managing auth cookies
    const cookiesToSet: Array<{
      name: string;
      value: string;
      options: CookieOptions;
    }> = [];

    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return request.cookies.get(name)?.value;
          },
          set(name: string, value: string, options: CookieOptions) {
            cookiesToSet.push({ name, value, options });
          },
          remove(name: string, options: CookieOptions) {
            cookiesToSet.push({
              name,
              value: "",
              options: { ...options, maxAge: 0 },
            });
          },
        },
      }
    );

    try {
      const { data, error } = await supabase.auth.exchangeCodeForSession(code);

      if (!error && data?.user) {
        // Check if user has family group invitation metadata
        const userMetadata = data.user.user_metadata;

        if (
          userMetadata?.invitationType === "family_group" &&
          userMetadata?.invitationToken
        ) {
          // Redirect to the specific invitation page
          redirectTo.pathname = `/invite/${userMetadata.invitationToken}`;
          redirectTo.searchParams.delete("code");
          redirectTo.searchParams.delete("next");
          const response = NextResponse.redirect(redirectTo);

          // Set cookies on the response
          for (const cookie of cookiesToSet) {
            response.cookies.set(cookie.name, cookie.value, cookie.options);
          }

          return response;
        }

        // Check if user needs to set password (invited users)
        if (data.user.email_confirmed_at && !data.user.last_sign_in_at) {
          // New invited user - redirect to set password
          redirectTo.pathname = "/auth/set-password";
          redirectTo.searchParams.delete("code");
          redirectTo.searchParams.set(
            "invitation_token",
            userMetadata?.invitationToken || ""
          );
          const response = NextResponse.redirect(redirectTo);

          // Set cookies on the response
          for (const cookie of cookiesToSet) {
            response.cookies.set(cookie.name, cookie.value, cookie.options);
          }

          return response;
        }

        // Regular authenticated user - redirect to dashboard or next URL
        redirectTo.pathname = next;
        redirectTo.searchParams.delete("code");
        redirectTo.searchParams.delete("next");
        const response = NextResponse.redirect(redirectTo);

        // Set cookies on the response
        for (const cookie of cookiesToSet) {
          response.cookies.set(cookie.name, cookie.value, cookie.options);
        }

        return response;
      }

      if (error) {
        console.error("Auth callback error:", error);
        // Redirect to login with error message
        redirectTo.pathname = "/auth/login";
        redirectTo.searchParams.delete("code");
        redirectTo.searchParams.delete("next");
        redirectTo.searchParams.set("error", "auth_callback_error");
        redirectTo.searchParams.set("error_description", error.message);
        return NextResponse.redirect(redirectTo);
      }
    } catch (err) {
      console.error("Unexpected auth callback error:", err);
      // Redirect to login with generic error
      redirectTo.pathname = "/auth/login";
      redirectTo.searchParams.delete("code");
      redirectTo.searchParams.delete("next");
      redirectTo.searchParams.set("error", "unexpected_error");
      return NextResponse.redirect(redirectTo);
    }
  }

  // No code provided or other error - redirect to login
  redirectTo.pathname = "/auth/login";
  redirectTo.searchParams.delete("code");
  redirectTo.searchParams.delete("next");
  redirectTo.searchParams.set("error", "missing_code");
  return NextResponse.redirect(redirectTo);
}
