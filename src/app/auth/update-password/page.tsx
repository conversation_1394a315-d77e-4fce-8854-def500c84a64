"use client";

import { useState, useEffect, Suspense } from "react";
import { use<PERSON><PERSON><PERSON>, useSearchParams } from "next/navigation";
import { createClient } from "@/shared/services/supabase/client";
import { Button } from "@/shared/components/ui/button";
import { Input } from "@/shared/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/shared/components/ui/card";
import { toast } from "sonner";
import {
  Loader2,
  AlertCircle,
  KeyRound, // Already used for button, can be aliased or use a different one for title
  CheckCircle,
  ArrowLeft,
  ShieldCheck, // Using ShieldCheck for Update Password title
} from "lucide-react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/shared/components/ui/form";

const updatePasswordSchema = z
  .object({
    password: z
      .string()
      .min(8, { message: "Password must be at least 8 characters long" })
      .regex(/[A-Z]/, {
        message: "Password must contain at least one uppercase letter",
      })
      .regex(/[a-z]/, {
        message: "Password must contain at least one lowercase letter",
      })
      .regex(/[0-9]/, { message: "Password must contain at least one number" }),
    confirmPassword: z.string(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"], // path of error
  });

type UpdatePasswordFormValues = z.infer<typeof updatePasswordSchema>;

function UpdatePasswordComponent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isTokenInvalid, setIsTokenInvalid] = useState(false);

  // Supabase client
  const supabase = createClient();

  useEffect(() => {
    // Supabase onAuthStateChange handles the session restoration from the URL fragment
    // containing access_token and refresh_token after a password reset redirect.
    // We just need to check if a session is active.
    const { data: authListener } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === "PASSWORD_RECOVERY") {
          // This event confirms the user is in the password recovery flow.
          // The session should now be active with the recovery token.
          setIsTokenInvalid(false);
          setError(null);
        } else if (
          event === "SIGNED_IN" &&
          !session?.user.aud.includes("authenticated")
        ) {
          // If signed in but not fully authenticated (e.g. after recovery link but before password update)
          // this is also a valid state to proceed.
        } else if (!session) {
          // If there's no session after the redirect, the token might be invalid or expired.
          // However, Supabase handles this internally. If updateUser fails, it's likely due to this.
        }
      }
    );

    // Check for error in searchParams (e.g., error_description=Invalid%20Refresh%20Token)
    const errorDescription = searchParams.get("error_description");
    if (errorDescription) {
      setError(decodeURIComponent(errorDescription));
      setIsTokenInvalid(true);
      toast.error("Password update failed", {
        description: decodeURIComponent(errorDescription),
        icon: <AlertCircle className="h-5 w-5 text-destructive" />,
      });
    }

    return () => {
      authListener?.subscription.unsubscribe();
    };
  }, [supabase, searchParams, router]);

  const form = useForm<UpdatePasswordFormValues>({
    resolver: zodResolver(updatePasswordSchema),
    defaultValues: {
      password: "",
      confirmPassword: "",
    },
  });

  const handleUpdatePassword = async (values: UpdatePasswordFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      const { error: updateError } = await supabase.auth.updateUser({
        password: values.password,
      });

      if (updateError) {
        setError(updateError.message);
        toast.error("Password update failed", {
          description: updateError.message,
          icon: <AlertCircle className="h-5 w-5 text-destructive" />,
        });
      } else {
        toast.success("Password updated successfully!", {
          description: "You can now log in with your new password.",
          icon: <CheckCircle className="h-5 w-5 text-green-500" />,
        });
        // Redirect to login after a short delay to allow toast to be seen
        setTimeout(() => router.push("/auth/login"), 2000);
      }
    } catch (err: unknown) {
      console.error("Unexpected error during password update:", err);
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError("An unexpected error occurred. Please try again.");
      }
      toast.error("An unexpected error occurred", {
        description: "Please try again later.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isTokenInvalid) {
    return (
      <div className="flex min-h-screen items-center justify-center px-4 py-8 bg-background">
        <Card className="w-full max-w-md shadow-lg">
          <CardHeader>
            <CardTitle className="text-2xl font-bold tracking-tight text-destructive">
              <AlertCircle className="inline-block mr-2 h-6 w-6" />
              Invalid or Expired Link
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              The password reset link is invalid or has expired. This can happen
              if the link has already been used or if too much time has passed
              since it was requested.
            </p>
            <p className="text-muted-foreground mt-2">{error}</p>
          </CardContent>
          <CardFooter>
            <Button
              variant="outline"
              className="w-full"
              onClick={() => router.push("/auth/forgot-password")}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Request a New Reset Link
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen items-center justify-center px-4 py-8 bg-background">
      <Card className="w-full max-w-md shadow-lg">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold tracking-tight flex items-center">
            <ShieldCheck className="mr-2 h-6 w-6" /> Update Your Password
          </CardTitle>
          <CardDescription>
            Enter your new password below. Make sure it is strong and memorable.
          </CardDescription>
        </CardHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleUpdatePassword)}>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>New Password</FormLabel>
                    <FormControl>
                      <Input
                        type="password"
                        placeholder="••••••••••"
                        {...field}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="confirmPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Confirm New Password</FormLabel>
                    <FormControl>
                      <Input
                        type="password"
                        placeholder="••••••••••"
                        {...field}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
            <CardFooter className="flex flex-col space-y-4">
              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <KeyRound className="mr-2 h-4 w-4" />
                )}
                Update Password
              </Button>
              <Button
                variant="outline"
                className="w-full"
                onClick={() => router.push("/auth/login")}
                type="button"
                disabled={isLoading}
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Login
              </Button>
            </CardFooter>
          </form>
        </Form>
      </Card>
    </div>
  );
}

// Wrap with Suspense because useSearchParams() needs it
export default function UpdatePasswordPage() {
  return (
    <Suspense
      fallback={
        <div className="flex min-h-screen items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      }
    >
      <UpdatePasswordComponent />
    </Suspense>
  );
}
