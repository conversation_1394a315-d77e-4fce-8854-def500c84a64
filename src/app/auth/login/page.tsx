"use client";

import { useState, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import { createClient } from "@/shared/services/supabase/client";
import { <PERSON><PERSON> } from "@/shared/components/ui/button";
import { Input } from "@/shared/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/shared/components/ui/card";
import { toast } from "sonner";
import { Loader2, AlertCircle, LogIn, KeyRound as KeyIcon } from "lucide-react"; // Using KeyRound as KeyIcon for title
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/shared/components/ui/form";

// Define the login form schema
const loginSchema = z.object({
  emailOrUsername: z
    .string()
    .min(1, { message: "Email or username is required" }),
  password: z.string().min(1, { message: "Password is required" }),
});

type LoginFormValues = z.infer<typeof loginSchema>;

function LoginForm() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isLoading, setIsLoading] = useState(false);
  const redirectTo = searchParams.get("redirect");

  // Initialize form with react-hook-form and zod validation
  const form = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      emailOrUsername: "",
      password: "",
    },
  });

  const handleLogin = async (values: LoginFormValues) => {
    setIsLoading(true);

    try {
      const supabase = createClient();

      // Check if input is email or username
      const isEmail = values.emailOrUsername.includes("@");

      if (isEmail) {
        // Direct login with email
        const { error } = await supabase.auth.signInWithPassword({
          email: values.emailOrUsername,
          password: values.password,
        });

        if (error) {
          toast.error("Login failed", {
            description: error.message,
            icon: <AlertCircle className="h-5 w-5 text-destructive" />,
          });
          return;
        }
      } else {
        // Look up email by username using RPC
        const { data: rpcData, error: rpcError } = await supabase.rpc<
          { email: string }[]
        >("get_email_by_username", { p_username: values.emailOrUsername });

        if (
          rpcError ||
          !rpcData ||
          rpcData.length === 0 ||
          !rpcData[0]?.email
        ) {
          toast.error("Login failed", {
            description:
              rpcError?.message ||
              "Username not found or has no associated email.",
            icon: <AlertCircle className="h-5 w-5 text-destructive" />,
          });
          return;
        }

        const userEmail = rpcData[0].email;

        // Login with the found email
        const { error } = await supabase.auth.signInWithPassword({
          email: userEmail,
          password: values.password,
        });

        if (error) {
          toast.error("Login failed", {
            description: error.message,
            icon: <AlertCircle className="h-5 w-5 text-destructive" />,
          });
          return;
        }
      }

      // Successful login
      toast.success("Login successful", {
        description: "Welcome back to Budget Tracker!",
      });

      // Redirect to the specified URL or dashboard
      const destination = redirectTo || "/dashboard";
      router.push(destination);
      router.refresh();
    } catch (err) {
      console.error("Unexpected error during login:", err);
      toast.error("An unexpected error occurred", {
        description: "Please try again later.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center px-4 py-8 bg-background">
      <Card className="w-full max-w-md shadow-lg">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold tracking-tight flex items-center">
            <KeyIcon className="mr-2 h-6 w-6" /> Welcome back
          </CardTitle>
          <CardDescription>
            Sign in to your Budget Tracker account
          </CardDescription>
        </CardHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleLogin)}>
            <CardContent className="space-y-4">
              <FormField
                control={form.control}
                name="emailOrUsername"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email or Username</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="<EMAIL> or johndoe123"
                        {...field}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormDescription>
                      You can login with either your email address or username
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Password</FormLabel>
                    <FormControl>
                      <Input type="password" {...field} disabled={isLoading} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="text-sm text-right mt-2">
                <Link
                  href="/auth/forgot-password"
                  className="text-primary hover:underline font-medium"
                >
                  Forgot Password?
                </Link>
              </div>
            </CardContent>
            <CardFooter className="flex flex-col space-y-4">
              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <LogIn className="mr-2 h-4 w-4" />
                )}
                Sign In
              </Button>
              <div className="text-sm text-muted-foreground text-center">
                Don&apos;t have an account?{" "}
                <Link
                  href={`/auth/signup${
                    redirectTo
                      ? `?redirect=${encodeURIComponent(redirectTo)}`
                      : ""
                  }`}
                  className="text-primary hover:underline font-medium"
                >
                  Sign up
                </Link>
              </div>
            </CardFooter>
          </form>
        </Form>
      </Card>
    </div>
  );
}

export default function LoginPage() {
  return (
    <Suspense fallback={
      <div className="flex min-h-screen items-center justify-center px-4 py-8 bg-background">
        <Card className="w-full max-w-md shadow-lg">
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl font-bold tracking-tight flex items-center">
              <KeyIcon className="mr-2 h-6 w-6" /> Welcome back
            </CardTitle>
            <CardDescription>
              Loading...
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    }>
      <LoginForm />
    </Suspense>
  );
}
