"use client";

import { useState, useEffect, Suspense } from "react";
import { use<PERSON><PERSON><PERSON>, useSearchParams } from "next/navigation";
import { createClient } from "@/shared/services/supabase/client";
import { But<PERSON> } from "@/shared/components/ui/button";
import { Input } from "@/shared/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/shared/components/ui/card";
import { toast } from "sonner";
import { Loader2, AlertCircle, CheckCircle, Key, Users } from "lucide-react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { type User } from "@supabase/supabase-js";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/shared/components/ui/form";

// Define the password setup schema
const passwordSchema = z.object({
  password: z
    .string()
    .min(8, { message: "Password must be at least 8 characters long" })
    .regex(/[A-Z]/, {
      message: "Password must contain at least one uppercase letter",
    })
    .regex(/[a-z]/, {
      message: "Password must contain at least one lowercase letter",
    })
    .regex(/[0-9]/, { message: "Password must contain at least one number" }),
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

type PasswordFormValues = z.infer<typeof passwordSchema>;

function SetPasswordForm() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isLoading, setIsLoading] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const [loadingUser, setLoadingUser] = useState(true);
  const invitationToken = searchParams.get("invitation_token");

  // Initialize form with react-hook-form and zod validation
  const form = useForm<PasswordFormValues>({
    resolver: zodResolver(passwordSchema),
    defaultValues: {
      password: "",
      confirmPassword: "",
    },
  });

  useEffect(() => {
    const checkUser = async () => {
      try {
        const supabase = createClient();
        const { data: { user: currentUser }, error } = await supabase.auth.getUser();
        
        if (error) {
          console.error("Error getting user:", error);
          toast.error("Authentication error", {
            description: "Please try logging in again.",
          });
          router.push("/auth/login");
          return;
        }

        if (!currentUser) {
          toast.error("Authentication required", {
            description: "Please sign in to continue.",
          });
          router.push("/auth/login");
          return;
        }

        // Check if user already has a password set (has signed in before)
        if (currentUser.last_sign_in_at) {
          toast.success("Account already set up", {
            description: "Redirecting to your invitation...",
          });
          if (invitationToken) {
            router.push(`/invite/${invitationToken}`);
          } else {
            router.push("/dashboard");
          }
          return;
        }

        setUser(currentUser);
      } catch (err) {
        console.error("Unexpected error:", err);
        toast.error("An unexpected error occurred");
        router.push("/auth/login");
      } finally {
        setLoadingUser(false);
      }
    };

    checkUser();
  }, [router, invitationToken]);

  const handleSetPassword = async (values: PasswordFormValues) => {
    if (!user) return;

    setIsLoading(true);

    try {
      const supabase = createClient();

      // Update the user's password
      const { error } = await supabase.auth.updateUser({
        password: values.password,
      });

      if (error) {
        toast.error("Failed to set password", {
          description: error.message,
          icon: <AlertCircle className="h-5 w-5 text-destructive" />,
        });
        return;
      }

      toast.success("Password set successfully!", {
        description: "Your account is now ready. Redirecting...",
        icon: <CheckCircle className="h-5 w-5 text-green-500" />,
      });

      // Redirect to invitation page if token exists, otherwise dashboard
      if (invitationToken) {
        router.push(`/invite/${invitationToken}`);
      } else {
        router.push("/dashboard");
      }
    } catch (err) {
      console.error("Unexpected error during password setup:", err);
      toast.error("An unexpected error occurred", {
        description: "Please try again later.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (loadingUser) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-background">
        <div className="text-center">
          <Loader2 className="h-12 w-12 animate-spin text-primary mx-auto mb-4" />
          <p className="text-lg text-gray-600 dark:text-gray-300">
            Setting up your account...
          </p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect in useEffect
  }

  return (
    <div className="flex min-h-screen items-center justify-center px-4 py-8 bg-background">
      <Card className="w-full max-w-md shadow-lg">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold tracking-tight flex items-center">
            <Key className="mr-2 h-6 w-6" /> Set Your Password
          </CardTitle>
          <CardDescription>
            {invitationToken ? (
              <div className="flex items-center text-sm text-muted-foreground">
                <Users className="mr-2 h-4 w-4" />
                Complete your account setup to join the family group
              </div>
            ) : (
              "Complete your account setup to get started"
            )}
          </CardDescription>
        </CardHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSetPassword)}>
            <CardContent className="space-y-4">
              <div className="text-sm text-muted-foreground bg-muted p-3 rounded-lg">
                <p>
                  <strong>Welcome, {user.email}!</strong>
                </p>
                <p>Please set a secure password for your account.</p>
              </div>

              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Password</FormLabel>
                    <FormControl>
                      <Input type="password" {...field} disabled={isLoading} />
                    </FormControl>
                    <FormDescription>
                      Must be at least 8 characters with uppercase, lowercase,
                      and numbers
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="confirmPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Confirm Password</FormLabel>
                    <FormControl>
                      <Input type="password" {...field} disabled={isLoading} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
            <CardFooter>
              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <CheckCircle className="mr-2 h-4 w-4" />
                )}
                Complete Setup
              </Button>
            </CardFooter>
          </form>
        </Form>
      </Card>
    </div>
  );
}

export default function SetPasswordPage() {
  return (
    <Suspense fallback={
      <div className="flex min-h-screen items-center justify-center bg-background">
        <div className="text-center">
          <Loader2 className="h-12 w-12 animate-spin text-primary mx-auto mb-4" />
          <p className="text-lg text-gray-600 dark:text-gray-300">
            Setting up your account...
          </p>
        </div>
      </div>
    }>
      <SetPasswordForm />
    </Suspense>
  );
}