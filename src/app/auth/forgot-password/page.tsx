"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
// import Link from "next/link"; // Link component is not used
import { createClient } from "@/shared/services/supabase/client";
import { Button } from "@/shared/components/ui/button";
import { Input } from "@/shared/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/shared/components/ui/card";
import { toast } from "sonner";
import {
  Loader2,
  AlertCircle,
  Mail,
  ArrowLeft,
  HelpCircle,
} from "lucide-react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormControl,
  // FormDescription, // FormDescription is not used
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/shared/components/ui/form";

const forgotPasswordSchema = z.object({
  email: z.string().email({ message: "Please enter a valid email address" }),
});

type ForgotPasswordFormValues = z.infer<typeof forgotPasswordSchema>;

export default function ForgotPasswordPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [messageSent, setMessageSent] = useState(false);

  const form = useForm<ForgotPasswordFormValues>({
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: {
      email: "",
    },
  });

  const handlePasswordResetRequest = async (
    values: ForgotPasswordFormValues
  ) => {
    setIsLoading(true);
    setMessageSent(false);

    try {
      const supabase = createClient();
      const { error } = await supabase.auth.resetPasswordForEmail(
        values.email,
        {
          redirectTo: `${window.location.origin}/auth/update-password`, // Or your configured redirect URL
        }
      );

      if (error) {
        toast.error("Password reset failed", {
          description: error.message,
          icon: <AlertCircle className="h-5 w-5 text-destructive" />,
        });
      } else {
        setMessageSent(true);
        toast.success("Password reset email sent", {
          description:
            "Please check your email for instructions to reset your password.",
          icon: <Mail className="h-5 w-5 text-green-500" />,
        });
        form.reset(); // Clear the form
      }
    } catch (err) {
      console.error("Unexpected error during password reset request:", err);
      toast.error("An unexpected error occurred", {
        description: "Please try again later.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center px-4 py-8 bg-background">
      <Card className="w-full max-w-md shadow-lg">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold tracking-tight flex items-center">
            <HelpCircle className="mr-2 h-6 w-6" /> Forgot Your Password?
          </CardTitle>
          <CardDescription>
            Enter your email address and we will send you a link to reset your
            password.
          </CardDescription>
        </CardHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handlePasswordResetRequest)}>
            <CardContent className="space-y-4">
              {!messageSent ? (
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input
                          type="email"
                          placeholder="<EMAIL>"
                          {...field}
                          disabled={isLoading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              ) : (
                <div className="text-center text-green-600 dark:text-green-400 p-4 border border-green-300 dark:border-green-700 rounded-md bg-green-50 dark:bg-green-900/30">
                  <Mail className="h-8 w-8 mx-auto mb-2" />
                  <p>
                    If an account exists for {form.getValues("email")}, you will
                    receive an email with password reset instructions shortly.
                  </p>
                </div>
              )}
            </CardContent>
            <CardFooter className="flex flex-col space-y-4">
              {!messageSent && (
                <Button type="submit" className="w-full" disabled={isLoading}>
                  {isLoading ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <Mail className="mr-2 h-4 w-4" />
                  )}
                  Send Reset Link
                </Button>
              )}
              <Button
                variant="outline"
                className="w-full"
                onClick={() => router.push("/auth/login")}
                type="button"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Login
              </Button>
            </CardFooter>
          </form>
        </Form>
      </Card>
    </div>
  );
}
