"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { createClient } from "@/shared/services/supabase/client";
import { Button } from "@/shared/components/ui/button";
import { Input } from "@/shared/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/shared/components/ui/card";
import {
  <PERSON>bs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/shared/components/ui/tabs";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/shared/components/ui/form";
import {
  PiggyBank,
  BarChart3,
  Shield,
  Smartphone,
  Loader2,
  AlertCircle,
  CheckCircle,
  LogIn,
  UserPlus,
  Sparkles,
  TrendingUp,
  Target,
  Users,
} from "lucide-react";
import { toast } from "sonner";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";

// Login form schema
const loginSchema = z.object({
  emailOrUsername: z
    .string()
    .min(1, { message: "Email or username is required" }),
  password: z.string().min(1, { message: "Password is required" }),
});

// Signup form schema
const signupSchema = z.object({
  username: z
    .string()
    .min(3, { message: "Username must be at least 3 characters long" })
    .max(20, { message: "Username must be at most 20 characters long" })
    .regex(/^[a-zA-Z0-9_]+$/, {
      message: "Username can only contain letters, numbers, and underscores",
    }),
  fullName: z
    .string()
    .min(2, { message: "Full name must be at least 2 characters long" })
    .max(50, { message: "Full name must be at most 50 characters long" }),
  email: z.string().email({ message: "Please enter a valid email address" }),
  password: z
    .string()
    .min(8, { message: "Password must be at least 8 characters long" })
    .regex(/[A-Z]/, {
      message: "Password must contain at least one uppercase letter",
    })
    .regex(/[a-z]/, {
      message: "Password must contain at least one lowercase letter",
    })
    .regex(/[0-9]/, { message: "Password must contain at least one number" }),
});

type LoginFormValues = z.infer<typeof loginSchema>;
type SignupFormValues = z.infer<typeof signupSchema>;

export default function HomePage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("signup");

  // Login form
  const loginForm = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      emailOrUsername: "",
      password: "",
    },
  });

  // Signup form
  const signupForm = useForm<SignupFormValues>({
    resolver: zodResolver(signupSchema),
    defaultValues: {
      username: "",
      fullName: "",
      email: "",
      password: "",
    },
  });

  const handleLogin = async (values: LoginFormValues) => {
    setIsLoading(true);

    try {
      const supabase = createClient();
      const isEmail = values.emailOrUsername.includes("@");

      if (isEmail) {
        const { error } = await supabase.auth.signInWithPassword({
          email: values.emailOrUsername,
          password: values.password,
        });

        if (error) {
          toast.error("Login failed", {
            description: error.message,
            icon: <AlertCircle className="h-5 w-5 text-destructive" />,
          });
          return;
        }
      } else {
        const { data: rpcData, error: rpcError } = await supabase.rpc<
          { email: string }[]
        >("get_email_by_username", { p_username: values.emailOrUsername });

        if (
          rpcError ||
          !rpcData ||
          rpcData.length === 0 ||
          !rpcData[0]?.email
        ) {
          toast.error("Login failed", {
            description:
              rpcError?.message ||
              "Username not found or has no associated email.",
            icon: <AlertCircle className="h-5 w-5 text-destructive" />,
          });
          return;
        }

        const userEmail = rpcData[0].email;
        const { error } = await supabase.auth.signInWithPassword({
          email: userEmail,
          password: values.password,
        });

        if (error) {
          toast.error("Login failed", {
            description: error.message,
            icon: <AlertCircle className="h-5 w-5 text-destructive" />,
          });
          return;
        }
      }

      router.push("/dashboard");
      router.refresh();
    } catch (err) {
      console.error("Unexpected error during login:", err);
      toast.error("An unexpected error occurred", {
        description: "Please try again later.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSignup = async (values: SignupFormValues) => {
    setIsLoading(true);

    try {
      const supabase = createClient();

      const { data, error } = await supabase.auth.signUp({
        email: values.email,
        password: values.password,
        options: {
          data: {
            full_name: values.fullName,
            username: values.username,
          },
        },
      });

      if (error) {
        toast.error("Signup failed", {
          description: error.message,
          icon: <AlertCircle className="h-5 w-5 text-destructive" />,
        });
        return;
      }

      if (data?.user) {
        toast.success("Account created successfully!", {
          description: "Please check your email to verify your account.",
          icon: <CheckCircle className="h-5 w-5 text-green-500" />,
          duration: 5000,
        });

        signupForm.reset();
        setActiveTab("login");
      }
    } catch (err) {
      console.error("Unexpected error during signup:", err);
      toast.error("An unexpected error occurred", {
        description: "Please try again later.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex min-h-screen flex-col">
      {/* Hero Section with Gradient Background */}
      <section className="relative flex-1 flex items-center justify-center px-4 py-16 overflow-hidden bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-gradient-to-br from-slate-100/50 to-blue-50/50 dark:from-slate-800/50 dark:to-slate-900/50"></div>

        <div className="relative max-w-7xl mx-auto grid gap-12 lg:grid-cols-2 lg:gap-20 items-center">
          {/* Left side - Hero content */}
          <div className="text-center lg:text-left space-y-8">
            <div className="flex justify-center lg:justify-start">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full blur-lg opacity-30 animate-pulse"></div>
                <PiggyBank className="relative h-20 w-20 text-blue-600 drop-shadow-lg" />
              </div>
            </div>

            <div className="space-y-4">
              <h1 className="text-5xl lg:text-6xl font-bold tracking-tight bg-gradient-to-r from-slate-900 via-blue-900 to-slate-900 dark:from-white dark:via-blue-100 dark:to-white bg-clip-text text-transparent">
                Take Control of Your{" "}
                <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  Finances
                </span>
              </h1>
              <p className="text-xl lg:text-2xl text-slate-600 dark:text-slate-300 leading-relaxed">
                Track expenses, set budgets, and achieve your financial goals
                with our{" "}
                <span className="font-semibold text-blue-600 dark:text-blue-400">
                  powerful
                </span>{" "}
                and{" "}
                <span className="font-semibold text-purple-600 dark:text-purple-400">
                  intuitive
                </span>{" "}
                budget tracking application.
              </p>
            </div>

            {/* Feature highlights */}
            <div className="flex flex-wrap gap-4 justify-center lg:justify-start">
              <div className="flex items-center gap-2 bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm rounded-full px-4 py-2 border border-slate-200 dark:border-slate-700">
                <Sparkles className="h-4 w-4 text-yellow-500" />
                <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                  Smart Analytics
                </span>
              </div>
              <div className="flex items-center gap-2 bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm rounded-full px-4 py-2 border border-slate-200 dark:border-slate-700">
                <Shield className="h-4 w-4 text-green-500" />
                <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                  Bank-level Security
                </span>
              </div>
              <div className="flex items-center gap-2 bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm rounded-full px-4 py-2 border border-slate-200 dark:border-slate-700">
                <Users className="h-4 w-4 text-blue-500" />
                <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                  Family Sharing
                </span>
              </div>
            </div>

            {/* Beta Program CTA */}
            <div className="mt-8 p-4 bg-gradient-to-r from-purple-100/80 to-blue-100/80 dark:from-purple-900/30 dark:to-blue-900/30 rounded-xl border border-purple-200 dark:border-purple-700">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg">
                    <Sparkles className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-slate-900 dark:text-white">Join Our Beta Program</h3>
                    <p className="text-sm text-slate-600 dark:text-slate-400">Get exclusive early access to revolutionary features</p>
                  </div>
                </div>
                <Link href="/beta">
                  <Button variant="outline" size="sm" className="border-purple-300 text-purple-700 hover:bg-purple-50 dark:border-purple-600 dark:text-purple-300 dark:hover:bg-purple-900/20">
                    Learn More
                  </Button>
                </Link>
              </div>
            </div>
          </div>

          {/* Right side - Enhanced Auth forms */}
          <div className="flex justify-center lg:justify-end">
            <Card className="w-full max-w-md bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl shadow-2xl border-0 ring-1 ring-slate-200 dark:ring-slate-700">
              <CardHeader className="space-y-2 text-center pb-6">
                <div className="flex justify-center">
                  <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl">
                    <Target className="h-6 w-6 text-white" />
                  </div>
                </div>
                <CardTitle className="text-2xl font-bold text-slate-900 dark:text-white">
                  Start Your Journey
                </CardTitle>
                <CardDescription className="text-slate-600 dark:text-slate-400">
                  Join thousands managing their finances better
                </CardDescription>
              </CardHeader>
              <CardContent className="pb-6">
                <Tabs
                  value={activeTab}
                  onValueChange={setActiveTab}
                  className="w-full"
                >
                  <TabsList className="grid w-full grid-cols-2 mb-6 bg-slate-100 dark:bg-slate-800">
                    <TabsTrigger
                      value="signup"
                      className="data-[state=active]:bg-white dark:data-[state=active]:bg-slate-700 data-[state=active]:text-slate-900 dark:data-[state=active]:text-white"
                    >
                      Sign Up
                    </TabsTrigger>
                    <TabsTrigger
                      value="login"
                      className="data-[state=active]:bg-white dark:data-[state=active]:bg-slate-700 data-[state=active]:text-slate-900 dark:data-[state=active]:text-white"
                    >
                      Login
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="signup" className="space-y-4">
                    <Form {...signupForm}>
                      <form
                        onSubmit={signupForm.handleSubmit(handleSignup)}
                        className="space-y-4"
                      >
                        <div className="grid grid-cols-2 gap-3">
                          <FormField
                            control={signupForm.control}
                            name="fullName"
                            render={({ field }) => (
                              <FormItem className="col-span-2">
                                <FormLabel className="text-slate-700 dark:text-slate-300">
                                  Full Name
                                </FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="John Doe"
                                    {...field}
                                    disabled={isLoading}
                                    className="bg-white/50 dark:bg-slate-800/50 border-slate-300 dark:border-slate-600 focus:border-blue-500 dark:focus:border-blue-400"
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={signupForm.control}
                            name="username"
                            render={({ field }) => (
                              <FormItem className="col-span-2">
                                <FormLabel className="text-slate-700 dark:text-slate-300">
                                  Username
                                </FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="johndoe123"
                                    {...field}
                                    disabled={isLoading}
                                    className="bg-white/50 dark:bg-slate-800/50 border-slate-300 dark:border-slate-600 focus:border-blue-500 dark:focus:border-blue-400"
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={signupForm.control}
                            name="email"
                            render={({ field }) => (
                              <FormItem className="col-span-2">
                                <FormLabel className="text-slate-700 dark:text-slate-300">
                                  Email
                                </FormLabel>
                                <FormControl>
                                  <Input
                                    type="email"
                                    placeholder="<EMAIL>"
                                    {...field}
                                    disabled={isLoading}
                                    className="bg-white/50 dark:bg-slate-800/50 border-slate-300 dark:border-slate-600 focus:border-blue-500 dark:focus:border-blue-400"
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={signupForm.control}
                            name="password"
                            render={({ field }) => (
                              <FormItem className="col-span-2">
                                <FormLabel className="text-slate-700 dark:text-slate-300">
                                  Password
                                </FormLabel>
                                <FormControl>
                                  <Input
                                    type="password"
                                    placeholder="••••••••"
                                    {...field}
                                    disabled={isLoading}
                                    className="bg-white/50 dark:bg-slate-800/50 border-slate-300 dark:border-slate-600 focus:border-blue-500 dark:focus:border-blue-400"
                                  />
                                </FormControl>
                                <FormDescription className="text-xs text-slate-500 dark:text-slate-400">
                                  8+ characters with uppercase, lowercase &
                                  number
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        <Button
                          type="submit"
                          className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200"
                          disabled={isLoading}
                          size="lg"
                        >
                          {isLoading ? (
                            <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                          ) : (
                            <UserPlus className="mr-2 h-5 w-5" />
                          )}
                          Create Account
                        </Button>
                      </form>
                    </Form>
                  </TabsContent>

                  <TabsContent value="login" className="space-y-4">
                    <Form {...loginForm}>
                      <form
                        onSubmit={loginForm.handleSubmit(handleLogin)}
                        className="space-y-4"
                      >
                        <FormField
                          control={loginForm.control}
                          name="emailOrUsername"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-slate-700 dark:text-slate-300">
                                Email or Username
                              </FormLabel>
                              <FormControl>
                                <Input
                                  placeholder="<EMAIL> or johndoe123"
                                  {...field}
                                  disabled={isLoading}
                                  className="bg-white/50 dark:bg-slate-800/50 border-slate-300 dark:border-slate-600 focus:border-blue-500 dark:focus:border-blue-400"
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={loginForm.control}
                          name="password"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-slate-700 dark:text-slate-300">
                                Password
                              </FormLabel>
                              <FormControl>
                                <Input
                                  type="password"
                                  placeholder="••••••••"
                                  {...field}
                                  disabled={isLoading}
                                  className="bg-white/50 dark:bg-slate-800/50 border-slate-300 dark:border-slate-600 focus:border-blue-500 dark:focus:border-blue-400"
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <div className="text-sm text-right">
                          <Link
                            href="/auth/forgot-password"
                            className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium transition-colors"
                          >
                            Forgot Password?
                          </Link>
                        </div>

                        <Button
                          type="submit"
                          className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200"
                          disabled={isLoading}
                          size="lg"
                        >
                          {isLoading ? (
                            <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                          ) : (
                            <LogIn className="mr-2 h-5 w-5" />
                          )}
                          Sign In
                        </Button>
                      </form>
                    </Form>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Enhanced Features Section */}
      <section className="py-24 px-4 bg-white dark:bg-slate-900">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-slate-900 dark:text-white mb-4">
              Everything you need to{" "}
              <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                manage your money
              </span>
            </h2>
            <p className="text-xl text-slate-600 dark:text-slate-400 max-w-2xl mx-auto">
              Powerful tools designed to make financial management simple and
              effective
            </p>
          </div>

          <div className="grid gap-8 md:grid-cols-3">
            <div className="group text-center space-y-6 p-8 rounded-2xl border border-slate-200 dark:border-slate-700 hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-300 hover:shadow-xl bg-gradient-to-br from-slate-50 to-white dark:from-slate-800 dark:to-slate-900">
              <div className="flex justify-center">
                <div className="p-4 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl group-hover:scale-110 transition-transform duration-300 shadow-lg">
                  <BarChart3 className="h-8 w-8 text-white" />
                </div>
              </div>
              <h3 className="text-2xl font-bold text-slate-900 dark:text-white">
                Detailed Analytics
              </h3>
              <p className="text-slate-600 dark:text-slate-400 leading-relaxed">
                Visualize your spending patterns with beautiful charts and
                actionable insights
              </p>
              <div className="flex justify-center">
                <TrendingUp className="h-5 w-5 text-green-500" />
              </div>
            </div>

            <div className="group text-center space-y-6 p-8 rounded-2xl border border-slate-200 dark:border-slate-700 hover:border-green-300 dark:hover:border-green-600 transition-all duration-300 hover:shadow-xl bg-gradient-to-br from-slate-50 to-white dark:from-slate-800 dark:to-slate-900">
              <div className="flex justify-center">
                <div className="p-4 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl group-hover:scale-110 transition-transform duration-300 shadow-lg">
                  <Shield className="h-8 w-8 text-white" />
                </div>
              </div>
              <h3 className="text-2xl font-bold text-slate-900 dark:text-white">
                Secure & Private
              </h3>
              <p className="text-slate-600 dark:text-slate-400 leading-relaxed">
                Your financial data is encrypted and never shared with third
                parties
              </p>
              <div className="flex justify-center">
                <CheckCircle className="h-5 w-5 text-green-500" />
              </div>
            </div>

            <div className="group text-center space-y-6 p-8 rounded-2xl border border-slate-200 dark:border-slate-700 hover:border-purple-300 dark:hover:border-purple-600 transition-all duration-300 hover:shadow-xl bg-gradient-to-br from-slate-50 to-white dark:from-slate-800 dark:to-slate-900">
              <div className="flex justify-center">
                <div className="p-4 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl group-hover:scale-110 transition-transform duration-300 shadow-lg">
                  <Smartphone className="h-8 w-8 text-white" />
                </div>
              </div>
              <h3 className="text-2xl font-bold text-slate-900 dark:text-white">
                Access Anywhere
              </h3>
              <p className="text-slate-600 dark:text-slate-400 leading-relaxed">
                Track your finances on any device with our responsive design
              </p>
              <div className="flex justify-center">
                <Sparkles className="h-5 w-5 text-purple-500" />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Enhanced Footer */}
      <footer className="py-12 px-4 bg-slate-900 dark:bg-black">
        <div className="max-w-6xl mx-auto text-center">
          <div className="flex justify-center mb-4">
            <PiggyBank className="h-8 w-8 text-blue-400" />
          </div>
          <p className="text-slate-400">
            &copy; 2024 Budget Tracker. All rights reserved.
          </p>
          <p className="text-slate-500 text-sm mt-2">
            Built with ❤️ for better financial wellness
          </p>
        </div>
      </footer>
    </div>
  );
}
