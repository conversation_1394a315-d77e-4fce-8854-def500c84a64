"use client";

import React, { useState, useEffect } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { createClient } from "@/shared/services/supabase/client";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/shared/components/ui/card";
import { Button } from "@/shared/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/shared/components/ui/alert";
import {
  Loader2,
  CheckCircle,
  XCircle,
  AlertCircle,
  Mail,
  Users,
} from "lucide-react";
import { format } from "date-fns";
import { toast } from "sonner";

interface InvitationDetails {
  id: string;
  group_id: string;
  invitee_email: string;
  status: string;
  expires_at: string;
  // Optional fields that might not be returned by the RPC
  group_name?: string;
  invited_by_email?: string;
  created_at?: string;
}

export default function InviteTokenPage() {
  const params = useParams();
  const router = useRouter();
  const token = params.token as string;
  const supabase = createClient();

  const [invitation, setInvitation] = useState<InvitationDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [user, setUser] = useState<{ email: string | undefined } | null>(null);

  useEffect(() => {
    const fetchInvitationAndUser = async () => {
      try {
        // Get current user
        const {
          data: { user: currentUser },
        } = await supabase.auth.getUser();
        setUser(currentUser ? { email: currentUser.email } : null);

        // Fetch invitation details by token first
        const { data, error } = await supabase.rpc("get_invitation_by_token", {
          p_token: token,
        });

        if (error) {
          setError("Failed to load invitation details.");
          console.error("Error fetching invitation:", error);
          return;
        }

        if (!data || data.length === 0) {
          setError("Invitation not found or has expired.");
          return;
        }

        setInvitation(data[0]);

        // If user is authenticated and this invitation matches their email,
        // we can attempt to auto-accept
        if (currentUser && data[0]?.invitee_email) {
          const inviteeEmail = data[0].invitee_email;
          
          // Check if the authenticated user's email matches the invitation
          if (currentUser.email === inviteeEmail) {
            // Auto-accept the invitation since they're the intended recipient
            setProcessing(true);
            try {
              const { data: acceptData, error: acceptError } = await supabase.rpc(
                "accept_family_group_invitation",
                {
                  p_invitation_token: token,
                }
              );

              if (acceptError) {
                console.error("Auto-accept failed:", acceptError);
                // Continue with normal flow - they can manually accept
              } else {
                const typedData = acceptData as { status?: string; message?: string };
                if (typedData?.status === "success") {
                  toast.success("Welcome to the family group!");
                  router.push(`/dashboard/family/${data[0].group_id}`);
                  return;
                }
              }
            } catch (err) {
              console.error("Auto-accept error:", err);
              // Continue with normal flow
            } finally {
              setProcessing(false);
            }
          } else {
            // User is authenticated but with different email
            console.log("Authenticated user email doesn't match invitation email");
          }
        }
      } catch (err) {
        setError("An unexpected error occurred.");
        console.error("Error:", err);
      } finally {
        setLoading(false);
      }
    };

    if (token) {
      fetchInvitationAndUser();
    }
  }, [token, supabase, router]);

  const handleAccept = async () => {
    if (!invitation || !user) return;

    setProcessing(true);
    try {
      const { data, error } = await supabase.rpc(
        "accept_family_group_invitation",
        {
          p_invitation_token: token,
        }
      );

      if (error) {
        toast.error(`Failed to accept invitation: ${error.message}`);
        return;
      }

      const typedData = data as { status?: string; message?: string };
      if (typedData?.status === "success") {
        toast.success(typedData.message || "Invitation accepted successfully");
        // Redirect to the group page
        router.push(`/dashboard/family/${invitation.group_id}`);
      } else {
        toast.error(typedData?.message || "Failed to accept invitation");
      }
    } catch (err) {
      toast.error("An unexpected error occurred");
      console.error("Error accepting invitation:", err);
    } finally {
      setProcessing(false);
    }
  };

  const handleDecline = async () => {
    if (!invitation) return;

    setProcessing(true);
    try {
      const { data, error } = await supabase.rpc("respond_to_invitation", {
        p_invitation_id: invitation.id,
        p_action: "decline",
      });

      if (error) {
        toast.error(`Failed to decline invitation: ${error.message}`);
        return;
      }

      const typedData = data as { success?: boolean; message?: string };
      if (typedData?.success) {
        toast.success(typedData.message || "Invitation declined successfully");
        router.push("/dashboard/invitations");
      } else {
        toast.error(typedData?.message || "Failed to decline invitation");
      }
    } catch (err) {
      toast.error("An unexpected error occurred");
      console.error("Error declining invitation:", err);
    } finally {
      setProcessing(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <Loader2 className="h-12 w-12 animate-spin text-primary mx-auto mb-4" />
          <p className="text-lg text-gray-600 dark:text-gray-300">
            Loading invitation...
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="flex items-center text-red-600 dark:text-red-400">
              <AlertCircle className="h-6 w-6 mr-2" />
              Invalid Invitation
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 dark:text-gray-300 mb-4">{error}</p>
            <Button
              onClick={() => router.push("/dashboard")}
              className="w-full"
            >
              Go to Dashboard
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Mail className="h-6 w-6 mr-2 text-primary" />
              Sign In Required
            </CardTitle>
            <CardDescription>
              You need to sign in to accept this invitation.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <p className="text-sm text-gray-600 dark:text-gray-300">
                You&apos;ve been invited to join{" "}
                <strong>{invitation?.group_name}</strong>.
              </p>
              <div className="space-y-2">
                <Button
                  onClick={() =>
                    router.push(`/auth/login?redirect=/invite/${token}`)
                  }
                  className="w-full"
                >
                  Sign In
                </Button>
                <Button
                  variant="outline"
                  onClick={() =>
                    router.push(`/auth/signup?redirect=/invite/${token}`)
                  }
                  className="w-full"
                >
                  Create Account
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!invitation) {
    return null;
  }

  // Check if invitation has expired
  const isExpired = new Date(invitation.expires_at) < new Date();
  const isNotPending = invitation.status !== "pending";

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 p-4">
      <Card className="w-full max-w-lg">
        <CardHeader>
          <CardTitle className="flex items-center text-2xl">
            <Users className="h-8 w-8 mr-3 text-primary" />
            Family Group Invitation
          </CardTitle>
          <CardDescription>
            You&apos;ve been invited to join a family group
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {(isExpired || isNotPending) && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Invitation Unavailable</AlertTitle>
              <AlertDescription>
                {isExpired
                  ? "This invitation has expired."
                  : `This invitation has already been ${invitation.status}.`}
              </AlertDescription>
            </Alert>
          )}

          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                {invitation.group_name}
              </h3>
              {invitation.invited_by_email && (
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Invited by: {invitation.invited_by_email}
                </p>
              )}
              {invitation.created_at && (
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Invited on:{" "}
                  {format(new Date(invitation.created_at), "MMMM d, yyyy")}
                </p>
              )}
              {!isExpired && (
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Expires:{" "}
                  {format(new Date(invitation.expires_at), "MMMM d, yyyy")}
                </p>
              )}
            </div>

            <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
              <p className="text-sm text-gray-700 dark:text-gray-300">
                <strong>Your email:</strong> {user.email}
              </p>
              <p className="text-sm text-gray-700 dark:text-gray-300">
                <strong>Invited email:</strong> {invitation.invitee_email}
              </p>
              {user.email !== invitation.invitee_email && (
                <Alert className="mt-3">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription className="text-sm">
                    This invitation was sent to a different email address. You
                    can still accept it if you have access to that email.
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </div>

          {!isExpired && isNotPending === false && (
            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={handleDecline}
                disabled={processing}
                className="flex-1"
              >
                {processing ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <XCircle className="mr-2 h-4 w-4" />
                )}
                Decline
              </Button>
              <Button
                onClick={handleAccept}
                disabled={processing}
                className="flex-1"
              >
                {processing ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <CheckCircle className="mr-2 h-4 w-4" />
                )}
                Accept Invitation
              </Button>
            </div>
          )}

          {(isExpired || isNotPending) && (
            <Button
              onClick={() => router.push("/dashboard")}
              className="w-full"
            >
              Go to Dashboard
            </Button>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
