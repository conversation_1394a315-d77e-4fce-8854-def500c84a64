'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { BetaOnboardingFlow } from '@/beta/components/BetaOnboardingFlow';
import { useBetaFinanceType } from '@/beta/contexts/BetaFinanceTypeContext';
import { betaService } from '@/beta/services/betaService';
import { useToast } from '@/shared/hooks/use-toast';

export default function BetaOnboardingPage() {
  const router = useRouter();
  const { user } = useBetaFinanceType();
  const { toast } = useToast();

  const handleOnboardingComplete = async () => {
    if (!user) return;

    try {
      await betaService.completeOnboarding(user.id);

      toast({
        title: 'Welcome to the Beta!',
        description: 'Your onboarding is complete. Let\'s start exploring!',
      });

      router.push('/beta/dashboard');
    } catch {
      toast({
        title: 'An error occurred',
        description: 'Please try again later.',
        variant: 'destructive'
      });
    }
  };

  // Redirect if already completed onboarding
  React.useEffect(() => {
    if (user?.onboarding_completed) {
      router.push('/beta/dashboard');
    }
  }, [user, router]);

  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading your beta account...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <BetaOnboardingFlow onComplete={handleOnboardingComplete} />
    </div>
  );
}