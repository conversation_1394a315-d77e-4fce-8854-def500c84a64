"use client";

import React, { useState, useEffect } from "react";
import <PERSON> from "next/link";
import { <PERSON><PERSON> } from "@/shared/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/shared/components/ui/card";
import { Badge } from "@/shared/components/ui/badge";
import { Input } from "@/shared/components/ui/input";
import {
  Sparkles,
  Users,
  TrendingUp,
  Shield,
  Zap,
  MessageCircle,
  ArrowRight,
  CheckCircle,
  Star,
  ArrowLeft,
  LogIn,
  UserPlus,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { createClient } from "@/shared/services/supabase/client";
import { useToast } from "@/shared/hooks/use-toast";

const betaFeatures = [
  {
    icon: TrendingUp,
    title: "AI-Powered Insights",
    description:
      "Get intelligent spending analysis and personalized financial recommendations",
    status: "New",
  },
  {
    icon: Users,
    title: "Enhanced Family Collaboration",
    description:
      "Advanced family financial management with real-time collaboration tools",
    status: "Beta",
  },
  {
    icon: Zap,
    title: "Smart Budgeting",
    description:
      "Automated budget adjustments based on your spending patterns and goals",
    status: "Coming Soon",
  },
  {
    icon: Shield,
    title: "Advanced Security",
    description:
      "Enterprise-grade security features and enhanced privacy controls",
    status: "New",
  },
];

const testimonials = [
  {
    name: "Sarah M.",
    role: "Family Finance User",
    content:
      "The family collaboration features have transformed how we manage our household budget. Everyone stays on the same page!",
    rating: 5,
  },
  {
    name: "David L.",
    role: "Personal Finance User",
    content:
      "The AI insights helped me identify spending patterns I never noticed. Saved me $200 last month!",
    rating: 5,
  },
  {
    name: "Jessica R.",
    role: "Combined User",
    content:
      "Having both personal and family finances in one place with smart switching is a game-changer.",
    rating: 5,
  },
];

export default function BetaLandingPage() {
  const [email, setEmail] = useState("");
  const [isJoining, setIsJoining] = useState(false);
  const [user, setUser] = useState<any>(null);
  const router = useRouter();
  const { toast } = useToast();

  // Check if user is already logged in
  useEffect(() => {
    const checkUser = async () => {
      const supabase = createClient();
      const {
        data: { user },
      } = await supabase.auth.getUser();
      setUser(user);
    };
    checkUser();
  }, []);

  const handleJoinBeta = async () => {
    if (!email) return;

    setIsJoining(true);
    // In a real implementation, this would handle the beta signup process
    setTimeout(() => {
      router.push("/beta/auth/signup");
    }, 1000);
  };

  const handleAccessBeta = () => {
    if (user) {
      router.push("/beta/dashboard");
    } else {
      router.push("/beta/auth/login");
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">BT</span>
              </div>
              <span className="text-xl font-bold">Budget Tracker</span>
              <Badge
                variant="secondary"
                className="bg-gradient-to-r from-blue-600 to-purple-600 text-white"
              >
                <Sparkles className="w-3 h-3 mr-1" />
                BETA
              </Badge>
            </div>
            <div className="flex items-center space-x-4">
              <Link
                href="/"
                className="text-sm text-muted-foreground hover:text-foreground flex items-center"
              >
                <ArrowLeft className="w-4 h-4 mr-1" />
                Main App
              </Link>
              {user ? (
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-muted-foreground">
                    Welcome back!
                  </span>
                  <Button
                    onClick={handleAccessBeta}
                    className="bg-gradient-to-r from-blue-600 to-purple-600 text-white"
                  >
                    <Sparkles className="w-4 h-4 mr-2" />
                    Access Beta
                  </Button>
                </div>
              ) : (
                <div className="flex items-center space-x-2">
                  <Link href="/beta/auth/login">
                    <Button variant="outline">
                      <LogIn className="w-4 h-4 mr-2" />
                      Beta Login
                    </Button>
                  </Link>
                  <Link href="/beta/auth/signup">
                    <Button className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
                      <UserPlus className="w-4 h-4 mr-2" />
                      Join Beta
                    </Button>
                  </Link>
                </div>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto text-center">
          <div className="max-w-4xl mx-auto space-y-6">
            <div className="flex items-center justify-center space-x-2 mb-4">
              <Sparkles className="w-6 h-6 text-purple-600" />
              <Badge
                variant="secondary"
                className="bg-purple-100 text-purple-700 px-4 py-1"
              >
                Exclusive Beta Access
              </Badge>
            </div>

            <h1 className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 bg-clip-text text-transparent">
              The Future of Financial Management
            </h1>

            <p className="text-xl text-muted-foreground max-w-2xl mx-auto leading-relaxed">
              Join our exclusive beta program and experience cutting-edge
              financial tools designed for modern families and individuals.
              Shape the future with your feedback.
            </p>

            {user ? (
              <div className="flex flex-col items-center space-y-4">
                <div className="bg-green-50 border border-green-200 rounded-lg p-4 max-w-md mx-auto">
                  <div className="flex items-center space-x-2 text-green-700">
                    <CheckCircle className="w-5 h-5" />
                    <span className="font-semibold">
                      You&apos;re already signed in!
                    </span>
                  </div>
                  <p className="text-sm text-green-600 mt-1">
                    Ready to explore the beta features? Access your dashboard
                    now.
                  </p>
                </div>
                <Button
                  onClick={handleAccessBeta}
                  size="lg"
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 h-12 px-8"
                >
                  <Sparkles className="w-4 h-4 mr-2" />
                  Access Beta Dashboard
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </div>
            ) : (
              <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-4 max-w-md mx-auto">
                <div className="flex-1 w-full">
                  <Input
                    type="email"
                    placeholder="Enter your email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="h-12"
                  />
                </div>
                <Button
                  onClick={handleJoinBeta}
                  disabled={!email || isJoining}
                  size="lg"
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 h-12 px-8"
                >
                  {isJoining ? "Joining..." : "Join Beta"}
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </div>
            )}

            <div className="flex items-center justify-center space-x-6 text-sm text-muted-foreground">
              <div className="flex items-center">
                <CheckCircle className="w-4 h-4 mr-1 text-green-500" />
                Free during beta
              </div>
              <div className="flex items-center">
                <CheckCircle className="w-4 h-4 mr-1 text-green-500" />
                Early access features
              </div>
              <div className="flex items-center">
                <CheckCircle className="w-4 h-4 mr-1 text-green-500" />
                Direct feedback channel
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-4 bg-white">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold mb-4">Exclusive Beta Features</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Get early access to revolutionary features that will transform
              your financial management experience.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {betaFeatures.map((feature, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-center justify-between mb-2">
                    <feature.icon className="w-8 h-8 text-blue-600" />
                    <Badge
                      variant={
                        feature.status === "New" ? "default" : "secondary"
                      }
                      className={
                        feature.status === "New"
                          ? "bg-green-100 text-green-700"
                          : feature.status === "Beta"
                          ? "bg-blue-100 text-blue-700"
                          : "bg-orange-100 text-orange-700"
                      }
                    >
                      {feature.status}
                    </Badge>
                  </div>
                  <CardTitle className="text-lg">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>{feature.description}</CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 px-4 bg-gradient-to-r from-blue-50 to-purple-50">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold mb-4">What Beta Users Say</h2>
            <p className="text-muted-foreground">
              Real feedback from our amazing beta community
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="bg-white">
                <CardHeader>
                  <div className="flex items-center space-x-1 mb-2">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star
                        key={i}
                        className="w-4 h-4 fill-yellow-400 text-yellow-400"
                      />
                    ))}
                  </div>
                  <CardTitle className="text-lg">{testimonial.name}</CardTitle>
                  <CardDescription>{testimonial.role}</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground italic">
                    "{testimonial.content}"
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="container mx-auto text-center">
          <div className="max-w-2xl mx-auto space-y-6">
            <h2 className="text-3xl font-bold">Ready to Shape the Future?</h2>
            <p className="text-blue-100 text-lg">
              Join hundreds of users who are already experiencing the next
              generation of financial management. Your feedback directly
              influences our product development.
            </p>

            {user ? (
              <div className="space-y-4">
                <p className="text-blue-100">
                  You&apos;re all set! Access your beta dashboard to get
                  started.
                </p>
                <Button
                  onClick={handleAccessBeta}
                  size="lg"
                  variant="secondary"
                  className="h-12 px-8 bg-white text-blue-600 hover:bg-gray-100"
                >
                  <Sparkles className="w-4 h-4 mr-2" />
                  Go to Beta Dashboard
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </div>
            ) : (
              <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-4 max-w-md mx-auto">
                <div className="flex-1 w-full">
                  <Input
                    type="email"
                    placeholder="Enter your email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="h-12 bg-white text-gray-900"
                  />
                </div>
                <Button
                  onClick={handleJoinBeta}
                  disabled={!email || isJoining}
                  size="lg"
                  variant="secondary"
                  className="h-12 px-8 bg-white text-blue-600 hover:bg-gray-100"
                >
                  {isJoining ? "Joining..." : "Join Beta Now"}
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </div>
            )}

            <div className="flex items-center justify-center space-x-2 text-sm text-blue-100">
              <MessageCircle className="w-4 h-4" />
              <span>Questions? Email <NAME_EMAIL></span>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-8 px-4 bg-white border-t">
        <div className="container mx-auto">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="flex items-center space-x-2 mb-4 md:mb-0">
              <div className="w-6 h-6 bg-gradient-to-r from-blue-600 to-purple-600 rounded flex items-center justify-center">
                <span className="text-white font-bold text-xs">BT</span>
              </div>
              <span className="font-semibold">Budget Tracker Beta</span>
            </div>
            <div className="flex items-center space-x-6 text-sm text-muted-foreground">
              <Link href="/privacy" className="hover:text-foreground">
                Privacy
              </Link>
              <Link href="/terms" className="hover:text-foreground">
                Terms
              </Link>
              <Link href="/contact" className="hover:text-foreground">
                Contact
              </Link>
              <Link
                href="/"
                className="hover:text-foreground flex items-center"
              >
                <ArrowLeft className="w-3 h-3 mr-1" />
                Main App
              </Link>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
