"use client";

import React, { useState } from "react";
import { Card, CardContent } from "@/shared/components/ui/card";
import { <PERSON><PERSON> } from "@/shared/components/ui/button";
import { Badge } from "@/shared/components/ui/badge";
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger,
} from "@/shared/components/ui/tabs";
import {
  Target,
  Sparkles,
  BarChart3,
  Pie<PERSON>hart,
  CreditCard,
  MessageCircle,
  User,
} from "lucide-react";
import { useBetaFinanceType } from "@/beta/contexts/BetaFinanceTypeContext";
import { BetaFeedbackWidget } from "@/beta/components/BetaFeedbackWidget";
import {
  PersonalOverview,
  PersonalBudgets,
  PersonalGoals,
  PersonalTransactions,
} from "@/beta/dashboards/personal/components";

export default function PersonalDashboardPage() {
  const { user } = useBetaFinanceType();
  const [activeTab, setActiveTab] = useState("overview");

  const handleAddTransaction = () => setActiveTab("transactions");
  const handleAddAccount = () => setActiveTab("accounts");
  const handleViewBudgets = () => setActiveTab("budgets");
  const handleViewGoals = () => setActiveTab("goals");

  const getWelcomeMessage = () => {
    const hour = new Date().getHours();
    const greeting =
      hour < 12
        ? "Good morning"
        : hour < 18
        ? "Good afternoon"
        : "Good evening";
    return `${greeting}, ${user?.full_name?.split(" ")[0] || "Beta User"}!`;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <User className="h-8 w-8 text-blue-600" />
            {getWelcomeMessage()}
          </h1>
          <p className="text-muted-foreground">
            Your personal financial dashboard - clean, focused, and designed
            just for you
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge
            variant="secondary"
            className="bg-gradient-to-r from-blue-600 to-purple-600 text-white"
          >
            <Sparkles className="w-3 h-3 mr-1" />
            Personal Finance Beta
          </Badge>
          <BetaFeedbackWidget
            trigger={
              <Button variant="outline" size="sm">
                <MessageCircle className="w-4 h-4 mr-2" />
                Feedback
              </Button>
            }
          />
        </div>
      </div>

      {/* Beta Welcome Message */}
      <Card className="border-l-4 border-blue-500 bg-blue-50/50">
        <CardContent className="pt-6">
          <div className="flex items-start gap-3">
            <div className="p-2 rounded-full bg-blue-100">
              <Sparkles className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <h3 className="font-semibold text-blue-900">
                Welcome to Personal Finance Beta!
              </h3>
              <p className="text-sm text-blue-700 mt-1">
                You're experiencing a simplified, individual-focused financial
                management interface. This dashboard is optimized for personal
                use with 50% fewer distractions.
              </p>
              <div className="flex items-center gap-2 mt-3">
                <Badge variant="outline" className="text-xs">
                  Clean Interface
                </Badge>
                <Badge variant="outline" className="text-xs">
                  Fast Actions
                </Badge>
                <Badge variant="outline" className="text-xs">
                  Personal Focus
                </Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Dashboard Tabs */}
      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="space-y-6"
      >
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="w-4 h-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="budgets" className="flex items-center gap-2">
            <PieChart className="w-4 h-4" />
            Budgets
          </TabsTrigger>
          <TabsTrigger value="goals" className="flex items-center gap-2">
            <Target className="w-4 h-4" />
            Goals
          </TabsTrigger>
          <TabsTrigger value="transactions" className="flex items-center gap-2">
            <CreditCard className="w-4 h-4" />
            Transactions
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <PersonalOverview
            onAddTransaction={handleAddTransaction}
            onAddAccount={handleAddAccount}
            onViewBudgets={handleViewBudgets}
            onViewGoals={handleViewGoals}
          />
        </TabsContent>

        <TabsContent value="budgets" className="space-y-6">
          <PersonalBudgets />
        </TabsContent>

        <TabsContent value="goals" className="space-y-6">
          <PersonalGoals />
        </TabsContent>

        <TabsContent value="transactions" className="space-y-6">
          <PersonalTransactions />
        </TabsContent>
      </Tabs>
    </div>
  );
}
