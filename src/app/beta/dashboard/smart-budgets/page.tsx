"use client";

import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/shared/components/ui/card";
import { <PERSON><PERSON> } from "@/shared/components/ui/button";
import { Badge } from "@/shared/components/ui/badge";
import { Progress } from "@/shared/components/ui/progress";
import {
  PieChart,
  Sparkles,
  Brain,
  TrendingUp,
  AlertTriangle,
  Target,
  Zap,
  Settings,
  RefreshCw,
} from "lucide-react";
import { useBetaFinanceType } from "@/beta/contexts/BetaFinanceTypeContext";

export default function SmartBudgetsPage() {
  const { user } = useBetaFinanceType();

  const [smartBudgets] = useState([
    {
      id: "1",
      category: "Groceries",
      currentBudget: 600,
      smartRecommendation: 650,
      spent: 520,
      reasoning: "Based on seasonal price increases and family size changes",
      confidence: 92,
      adjustment: "+8.3%",
      impact: "Better accuracy",
    },
    {
      id: "2",
      category: "Transportation",
      currentBudget: 400,
      smartRecommendation: 350,
      spent: 280,
      reasoning: "Remote work patterns suggest lower fuel costs",
      confidence: 87,
      adjustment: "-12.5%",
      impact: "$600 annual savings",
    },
    {
      id: "3",
      category: "Entertainment",
      currentBudget: 200,
      smartRecommendation: 180,
      spent: 165,
      reasoning: "Consistent under-spending suggests budget optimization",
      confidence: 78,
      adjustment: "-10%",
      impact: "$240 annual savings",
    },
  ]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: user?.currency || "USD",
    }).format(amount);
  };

  const getProgressPercentage = (spent: number, budget: number) => {
    return Math.min((spent / budget) * 100, 100);
  };

  const totalCurrentBudget = smartBudgets.reduce(
    (sum, budget) => sum + budget.currentBudget,
    0
  );
  const totalSmartBudget = smartBudgets.reduce(
    (sum, budget) => sum + budget.smartRecommendation,
    0
  );
  const potentialSavings = totalCurrentBudget - totalSmartBudget;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center gap-2 mb-2">
            <h1 className="text-3xl font-bold tracking-tight">Smart Budgets</h1>
            <Badge className="bg-gradient-to-r from-blue-600 to-green-600 text-white">
              <Sparkles className="w-3 h-3 mr-1" />
              Beta Feature
            </Badge>
          </div>
          <p className="text-muted-foreground">
            AI-powered budget optimization based on your spending patterns
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline">
            <Settings className="w-4 h-4 mr-2" />
            Configure
          </Button>
          <Button>
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh Analysis
          </Button>
        </div>
      </div>

      {/* Smart Budget Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="border-l-4 border-blue-500">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <PieChart className="h-5 w-5 text-blue-500" />
              Current Budget
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">
              {formatCurrency(totalCurrentBudget)}
            </p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-green-500">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Brain className="h-5 w-5 text-green-500" />
              Smart Budget
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">
              {formatCurrency(totalSmartBudget)}
            </p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-purple-500">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Target className="h-5 w-5 text-purple-500" />
              Potential Savings
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold text-green-600">
              {formatCurrency(potentialSavings)}
            </p>
            <p className="text-sm text-muted-foreground mt-1">Per month</p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-orange-500">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Zap className="h-5 w-5 text-orange-500" />
              Recommendations
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">{smartBudgets.length}</p>
            <p className="text-sm text-muted-foreground mt-1">
              Active suggestions
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Smart Budget Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            AI Budget Recommendations
          </CardTitle>
          <CardDescription>
            Personalized budget adjustments based on your spending patterns
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {smartBudgets.map((budget) => (
              <Card key={budget.id} className="border border-border">
                <CardContent className="pt-6">
                  <div className="space-y-4">
                    {/* Header */}
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-semibold">
                        {budget.category}
                      </h3>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-xs">
                          <Brain className="w-3 h-3 mr-1" />
                          {budget.confidence}% confidence
                        </Badge>
                        <Badge
                          variant="outline"
                          className={`text-xs ${
                            budget.smartRecommendation < budget.currentBudget
                              ? "text-green-700 bg-green-50"
                              : "text-blue-700 bg-blue-50"
                          }`}
                        >
                          {budget.adjustment}
                        </Badge>
                      </div>
                    </div>

                    {/* Budget Comparison */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <p className="text-sm text-muted-foreground mb-2">
                          Current Budget
                        </p>
                        <p className="text-xl font-bold">
                          {formatCurrency(budget.currentBudget)}
                        </p>
                        <div className="mt-2">
                          <Progress
                            value={getProgressPercentage(
                              budget.spent,
                              budget.currentBudget
                            )}
                            className="h-2"
                          />
                          <p className="text-xs text-muted-foreground mt-1">
                            {formatCurrency(budget.spent)} spent (
                            {getProgressPercentage(
                              budget.spent,
                              budget.currentBudget
                            ).toFixed(0)}
                            %)
                          </p>
                        </div>
                      </div>

                      <div>
                        <p className="text-sm text-muted-foreground mb-2">
                          Smart Recommendation
                        </p>
                        <p className="text-xl font-bold text-green-600">
                          {formatCurrency(budget.smartRecommendation)}
                        </p>
                        <div className="mt-2">
                          <Progress
                            value={getProgressPercentage(
                              budget.spent,
                              budget.smartRecommendation
                            )}
                            className="h-2"
                          />
                          <p className="text-xs text-muted-foreground mt-1">
                            {formatCurrency(budget.spent)} spent (
                            {getProgressPercentage(
                              budget.spent,
                              budget.smartRecommendation
                            ).toFixed(0)}
                            %)
                          </p>
                        </div>
                      </div>

                      <div>
                        <p className="text-sm text-muted-foreground mb-2">
                          Impact
                        </p>
                        <p className="text-xl font-bold text-blue-600">
                          {budget.impact}
                        </p>
                        <p className="text-xs text-muted-foreground mt-1">
                          Expected outcome
                        </p>
                      </div>
                    </div>

                    {/* AI Reasoning */}
                    <div className="bg-gray-50 rounded-lg p-4">
                      <div className="flex items-start gap-3">
                        <div className="p-1 rounded-full bg-blue-100">
                          <Brain className="h-4 w-4 text-blue-600" />
                        </div>
                        <div>
                          <h4 className="font-medium text-sm mb-1">
                            AI Analysis
                          </h4>
                          <p className="text-sm text-gray-700">
                            {budget.reasoning}
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex gap-2">
                      <Button size="sm">Apply Recommendation</Button>
                      <Button size="sm" variant="outline">
                        Learn More
                      </Button>
                      <Button size="sm" variant="ghost">
                        Dismiss
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Smart Features */}
      <Card className="border-l-4 border-blue-500 bg-blue-50/50">
        <CardContent className="pt-6">
          <div className="flex items-start gap-3">
            <div className="p-2 rounded-full bg-blue-100">
              <Sparkles className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <h3 className="font-semibold text-blue-900">
                Smart Budget Technology
              </h3>
              <p className="text-sm text-blue-700 mt-1">
                Our AI analyzes your spending history, seasonal patterns, and
                lifestyle changes to suggest optimal budget allocations that
                align with your actual needs and financial goals.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                <div className="bg-white rounded-lg p-3 border">
                  <h4 className="font-medium text-blue-900 text-sm">
                    Predictive Modeling
                  </h4>
                  <p className="text-xs text-blue-700 mt-1">
                    Forecasts future spending based on historical patterns and
                    trends
                  </p>
                </div>
                <div className="bg-white rounded-lg p-3 border">
                  <h4 className="font-medium text-blue-900 text-sm">
                    Adaptive Learning
                  </h4>
                  <p className="text-xs text-blue-700 mt-1">
                    Continuously improves recommendations as it learns your
                    habits
                  </p>
                </div>
                <div className="bg-white rounded-lg p-3 border">
                  <h4 className="font-medium text-blue-900 text-sm">
                    Seasonal Adjustments
                  </h4>
                  <p className="text-xs text-blue-700 mt-1">
                    Accounts for seasonal variations and life events
                    automatically
                  </p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
