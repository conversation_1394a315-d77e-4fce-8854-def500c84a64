"use client";

import React, { useState } from "react";
import { Card, CardContent } from "@/shared/components/ui/card";
import { <PERSON><PERSON> } from "@/shared/components/ui/button";
import { Badge } from "@/shared/components/ui/badge";
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger,
} from "@/shared/components/ui/tabs";
import {
  Users,
  Sparkles,
  BarChart3,
  MessageCircle,
  Home,
  Target,
  Star,
  Wallet,
  ArrowRight,
} from "lucide-react";
import { useBetaFinanceType } from "@/beta/contexts/BetaFinanceTypeContext";
import { BetaFeedbackWidget } from "@/beta/components/BetaFeedbackWidget";
import {
  PersonalOverview,
  PersonalBudgets,
  PersonalGoals,
  PersonalTransactions,
} from "@/beta/dashboards/personal/components";

export default function BetaDashboardPage() {
  const { user, financeType } = useBetaFinanceType();
  const [activeTab, setActiveTab] = useState("overview");

  // Handlers for navigation between sections
  const handleAddTransaction = () => setActiveTab("transactions");
  const handleAddAccount = () => setActiveTab("accounts");
  const handleViewBudgets = () => setActiveTab("budgets");
  const handleViewGoals = () => setActiveTab("goals");

  const getWelcomeMessage = () => {
    const hour = new Date().getHours();
    const greeting =
      hour < 12
        ? "Good morning"
        : hour < 18
        ? "Good afternoon"
        : "Good evening";
    return `${greeting}, ${user?.full_name?.split(" ")[0] || "Beta User"}!`;
  };

  // Route to finance type specific dashboard
  if (financeType === "personal") {
    return (
      <div className="space-y-6">
        {/* Welcome Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              {getWelcomeMessage()}
            </h1>
            <p className="text-muted-foreground">
              Your Personal Finance Dashboard - Real data, powerful insights
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Badge
              variant="secondary"
              className="bg-gradient-to-r from-blue-600 to-purple-600 text-white"
            >
              <Sparkles className="w-3 h-3 mr-1" />
              Personal Finance Beta
            </Badge>
          </div>
        </div>

        {/* Tabbed Dashboard */}
        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          className="space-y-6"
        >
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview" className="flex items-center gap-2">
              <Home className="w-4 h-4" />
              Overview
            </TabsTrigger>
            <TabsTrigger value="budgets" className="flex items-center gap-2">
              <Target className="w-4 h-4" />
              Budgets
            </TabsTrigger>
            <TabsTrigger value="goals" className="flex items-center gap-2">
              <Star className="w-4 h-4" />
              Goals
            </TabsTrigger>
            <TabsTrigger
              value="transactions"
              className="flex items-center gap-2"
            >
              <Wallet className="w-4 h-4" />
              Transactions
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <PersonalOverview
              onAddTransaction={handleAddTransaction}
              onAddAccount={handleAddAccount}
              onViewBudgets={handleViewBudgets}
              onViewGoals={handleViewGoals}
            />
          </TabsContent>

          <TabsContent value="budgets" className="space-y-6">
            <PersonalBudgets />
          </TabsContent>

          <TabsContent value="goals" className="space-y-6">
            <PersonalGoals />
          </TabsContent>

          <TabsContent value="transactions" className="space-y-6">
            <PersonalTransactions />
          </TabsContent>
        </Tabs>
      </div>
    );
  }

  // Fallback for family or combined finance types - will be implemented later
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {getWelcomeMessage()}
          </h1>
          <p className="text-muted-foreground">
            {financeType === "family"
              ? "Family Finance Dashboard"
              : "Combined Finance Dashboard"}{" "}
            - Coming Soon
          </p>
        </div>
        <Badge
          variant="secondary"
          className="bg-gradient-to-r from-blue-600 to-purple-600 text-white"
        >
          <Sparkles className="w-3 h-3 mr-1" />
          Beta Preview
        </Badge>
      </div>

      <Card className="border-2 border-dashed border-purple-200">
        <CardContent className="text-center py-12">
          <div className="mx-auto w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mb-4">
            {financeType === "family" ? (
              <Users className="w-8 h-8 text-purple-600" />
            ) : (
              <BarChart3 className="w-8 h-8 text-purple-600" />
            )}
          </div>
          <h3 className="text-xl font-semibold mb-2">
            {financeType === "family"
              ? "Family Finance Dashboard"
              : "Combined Finance Dashboard"}
          </h3>
          <p className="text-muted-foreground mb-4">
            This dashboard is currently in development. Switch to Personal
            Finance to explore the completed features.
          </p>
          <div className="flex items-center justify-center gap-2">
            <Button variant="outline" onClick={() => setActiveTab("overview")}>
              <ArrowRight className="w-4 h-4 mr-2" />
              Try Personal Finance
            </Button>
            <BetaFeedbackWidget
              trigger={
                <Button variant="outline">
                  <MessageCircle className="w-4 h-4 mr-2" />
                  Share Feedback
                </Button>
              }
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
