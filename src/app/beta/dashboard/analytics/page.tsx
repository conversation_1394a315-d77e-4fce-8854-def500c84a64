"use client";

import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/shared/components/ui/card";
import { <PERSON><PERSON> } from "@/shared/components/ui/button";
import { Badge } from "@/shared/components/ui/badge";
import { Progress } from "@/shared/components/ui/progress";
import {
  TrendingUp,
  BarChart3,
  PieChart,
  Brain,
  Target,
  AlertTriangle,
  CheckCircle,
  DollarSign,
  Users,
  Download,
  Filter,
  Calendar,
  Lightbulb,
} from "lucide-react";
import { useBetaFinanceType } from "@/beta/contexts/BetaFinanceTypeContext";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/shared/components/ui/select";

export default function AdvancedAnalyticsPage() {
  const { user } = useBetaFinanceType();
  const [selectedPeriod, setSelectedPeriod] = useState("last-3-months");
  const [selectedCategory, setSelectedCategory] = useState("all");

  const [analyticsData] = useState({
    spendingTrends: [
      { category: "Housing", personal: 1200, family: 1800, trend: "stable" },
      { category: "Food", personal: 450, family: 320, trend: "increasing" },
      {
        category: "Transportation",
        personal: 280,
        family: 150,
        trend: "decreasing",
      },
      {
        category: "Entertainment",
        personal: 200,
        family: 180,
        trend: "stable",
      },
      { category: "Healthcare", personal: 150, family: 120, trend: "stable" },
    ],
    insights: [
      {
        type: "optimization",
        title: "Budget Reallocation Opportunity",
        description:
          "Consider moving $200 from entertainment to emergency fund to meet your 6-month target faster",
        impact: "medium",
        savings: 2400,
      },
      {
        type: "alert",
        title: "Family Food Budget Overspend",
        description:
          "Family grocery spending increased 15% this quarter. Review shared shopping habits",
        impact: "high",
        amount: 96,
      },
      {
        type: "achievement",
        title: "Personal Savings Goal Exceeded",
        description:
          "You're saving 25% above your target rate. Consider increasing goal amounts",
        impact: "positive",
        excess: 340,
      },
      {
        type: "trend",
        title: "Transportation Costs Declining",
        description:
          "Vehicle expenses down 12% due to improved maintenance scheduling",
        impact: "positive",
        savings: 168,
      },
    ],
    financialHealth: {
      score: 78,
      factors: [
        { name: "Emergency Fund", score: 85, status: "good" },
        { name: "Debt-to-Income", score: 72, status: "fair" },
        { name: "Savings Rate", score: 88, status: "excellent" },
        { name: "Budget Adherence", score: 65, status: "needs-improvement" },
      ],
    },
    predictions: [
      {
        metric: "Monthly Surplus",
        currentValue: 1650,
        predictedValue: 1820,
        confidence: 85,
        timeframe: "3 months",
      },
      {
        metric: "Emergency Fund",
        currentValue: 32000,
        predictedValue: 50000,
        confidence: 92,
        timeframe: "8 months",
      },
      {
        metric: "Vacation Goal",
        currentValue: 8400,
        predictedValue: 12000,
        confidence: 78,
        timeframe: "5 months",
      },
    ],
  });

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: user?.currency || "USD",
    }).format(amount);
  };

  const getInsightIcon = (type: string) => {
    switch (type) {
      case "optimization":
        return <Lightbulb className="h-5 w-5 text-yellow-500" />;
      case "alert":
        return <AlertTriangle className="h-5 w-5 text-red-500" />;
      case "achievement":
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case "trend":
        return <TrendingUp className="h-5 w-5 text-blue-500" />;
      default:
        return <Brain className="h-5 w-5 text-gray-500" />;
    }
  };

  const getInsightColor = (type: string) => {
    switch (type) {
      case "optimization":
        return "border-yellow-200 bg-yellow-50";
      case "alert":
        return "border-red-200 bg-red-50";
      case "achievement":
        return "border-green-200 bg-green-50";
      case "trend":
        return "border-blue-200 bg-blue-50";
      default:
        return "border-gray-200 bg-gray-50";
    }
  };

  const getHealthColor = (status: string) => {
    switch (status) {
      case "excellent":
        return "text-green-600 bg-green-100";
      case "good":
        return "text-blue-600 bg-blue-100";
      case "fair":
        return "text-yellow-600 bg-yellow-100";
      case "needs-improvement":
        return "text-red-600 bg-red-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Advanced Analytics
          </h1>
          <p className="text-muted-foreground">
            AI-powered insights across your complete financial picture
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline">
            <Filter className="w-4 h-4 mr-2" />
            Customize
          </Button>
          <Button>
            <Download className="w-4 h-4 mr-2" />
            Export Report
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block">
                Analysis Period
              </label>
              <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                <SelectTrigger>
                  <SelectValue placeholder="Select period" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="last-month">Last Month</SelectItem>
                  <SelectItem value="last-3-months">Last 3 Months</SelectItem>
                  <SelectItem value="last-6-months">Last 6 Months</SelectItem>
                  <SelectItem value="last-year">Last Year</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">
                Category Focus
              </label>
              <Select
                value={selectedCategory}
                onValueChange={setSelectedCategory}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem value="housing">Housing</SelectItem>
                  <SelectItem value="food">Food & Dining</SelectItem>
                  <SelectItem value="transportation">Transportation</SelectItem>
                  <SelectItem value="entertainment">Entertainment</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <Button className="w-full">
                <Brain className="w-4 h-4 mr-2" />
                Refresh Analysis
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Financial Health Score */}
      <Card className="border-l-4 border-blue-500">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Financial Health Score
          </CardTitle>
          <CardDescription>
            Overall assessment of your financial wellbeing
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="text-center">
                <div className="text-4xl font-bold text-blue-600 mb-2">
                  {analyticsData.financialHealth.score}/100
                </div>
                <div className="text-lg font-medium text-gray-700">
                  Good Financial Health
                </div>
                <p className="text-sm text-muted-foreground">
                  Above average compared to similar households
                </p>
              </div>
              <Progress
                value={analyticsData.financialHealth.score}
                className="h-3"
              />
            </div>
            <div className="space-y-3">
              {analyticsData.financialHealth.factors.map((factor, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-sm font-medium">{factor.name}</span>
                  <div className="flex items-center gap-2">
                    <div className="w-16 text-right">
                      <span className="text-sm font-semibold">
                        {factor.score}
                      </span>
                    </div>
                    <Badge
                      variant="outline"
                      className={`text-xs ${getHealthColor(factor.status)}`}
                    >
                      {factor.status.replace("-", " ")}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* AI-Powered Insights */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            AI-Powered Financial Insights
          </CardTitle>
          <CardDescription>
            Personalized recommendations based on your spending patterns
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {analyticsData.insights.map((insight, index) => (
              <Card
                key={index}
                className={`border ${getInsightColor(insight.type)}`}
              >
                <CardContent className="pt-4">
                  <div className="flex items-start gap-3">
                    <div className="p-2 rounded-full bg-white/80">
                      {getInsightIcon(insight.type)}
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold text-sm mb-1">
                        {insight.title}
                      </h4>
                      <p className="text-xs text-gray-700 mb-3">
                        {insight.description}
                      </p>
                      <div className="flex items-center justify-between">
                        <Badge variant="outline" className="text-xs">
                          {insight.impact} impact
                        </Badge>
                        {insight.savings && (
                          <span className="text-xs font-medium text-green-600">
                            Save {formatCurrency(insight.savings)}
                          </span>
                        )}
                        {insight.amount && (
                          <span className="text-xs font-medium text-red-600">
                            +{formatCurrency(insight.amount)}
                          </span>
                        )}
                        {insight.excess && (
                          <span className="text-xs font-medium text-green-600">
                            +{formatCurrency(insight.excess)}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Spending Analysis */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <PieChart className="h-5 w-5" />
            Cross-Account Spending Analysis
          </CardTitle>
          <CardDescription>
            Personal vs family spending breakdown by category
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {analyticsData.spendingTrends.map((trend, index) => (
              <div key={index} className="p-4 border rounded-lg">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-medium">{trend.category}</h4>
                  <div className="flex items-center gap-2">
                    <Badge
                      variant="outline"
                      className={`text-xs ${
                        trend.trend === "increasing"
                          ? "border-red-200 text-red-700"
                          : trend.trend === "decreasing"
                          ? "border-green-200 text-green-700"
                          : "border-gray-200 text-gray-700"
                      }`}
                    >
                      {trend.trend}
                    </Badge>
                    <span className="text-sm font-semibold">
                      {formatCurrency(trend.personal + trend.family)}
                    </span>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm text-blue-600">Personal</span>
                      <span className="text-sm font-medium">
                        {formatCurrency(trend.personal)}
                      </span>
                    </div>
                    <Progress
                      value={
                        (trend.personal / (trend.personal + trend.family)) * 100
                      }
                      className="h-2"
                    />
                  </div>
                  <div>
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm text-green-600">Family</span>
                      <span className="text-sm font-medium">
                        {formatCurrency(trend.family)}
                      </span>
                    </div>
                    <Progress
                      value={
                        (trend.family / (trend.personal + trend.family)) * 100
                      }
                      className="h-2"
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Predictive Analytics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Financial Predictions
          </CardTitle>
          <CardDescription>
            AI-powered forecasts based on current trends
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {analyticsData.predictions.map((prediction, index) => (
              <Card key={index} className="border-l-4 border-purple-500">
                <CardContent className="pt-4">
                  <div className="space-y-3">
                    <div>
                      <h4 className="font-semibold text-sm">
                        {prediction.metric}
                      </h4>
                      <p className="text-xs text-muted-foreground">
                        Forecast in {prediction.timeframe}
                      </p>
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-xs text-muted-foreground">Current</p>
                        <p className="font-semibold">
                          {formatCurrency(prediction.currentValue)}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="text-xs text-muted-foreground">
                          Predicted
                        </p>
                        <p className="font-semibold text-green-600">
                          {formatCurrency(prediction.predictedValue)}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-muted-foreground">
                        Confidence
                      </span>
                      <div className="flex items-center gap-2">
                        <Progress
                          value={prediction.confidence}
                          className="w-16 h-2"
                        />
                        <span className="text-xs font-medium">
                          {prediction.confidence}%
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Advanced Features */}
      <Card className="border-l-4 border-purple-500 bg-purple-50/50">
        <CardContent className="pt-6">
          <div className="flex items-start gap-3">
            <div className="p-2 rounded-full bg-purple-100">
              <Brain className="h-5 w-5 text-purple-600" />
            </div>
            <div>
              <h3 className="font-semibold text-purple-900">
                Advanced Analytics Features
              </h3>
              <p className="text-sm text-purple-700 mt-1">
                Unlock deeper insights with machine learning-powered analysis of
                your financial patterns. Get personalized recommendations,
                predictive modeling, and optimization suggestions.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-4">
                <div className="bg-white rounded-lg p-3 border">
                  <h4 className="font-medium text-purple-900 text-sm flex items-center gap-1">
                    <BarChart3 className="h-3 w-3" />
                    Trend Analysis
                  </h4>
                  <p className="text-xs text-purple-700 mt-1">
                    Multi-dimensional trend detection across all financial data
                  </p>
                </div>
                <div className="bg-white rounded-lg p-3 border">
                  <h4 className="font-medium text-purple-900 text-sm flex items-center gap-1">
                    <Target className="h-3 w-3" />
                    Goal Optimization
                  </h4>
                  <p className="text-xs text-purple-700 mt-1">
                    AI-suggested goal adjustments for optimal achievement rates
                  </p>
                </div>
                <div className="bg-white rounded-lg p-3 border">
                  <h4 className="font-medium text-purple-900 text-sm flex items-center gap-1">
                    <Lightbulb className="h-3 w-3" />
                    Smart Alerts
                  </h4>
                  <p className="text-xs text-purple-700 mt-1">
                    Proactive notifications for financial opportunities and
                    risks
                  </p>
                </div>
                <div className="bg-white rounded-lg p-3 border">
                  <h4 className="font-medium text-purple-900 text-sm flex items-center gap-1">
                    <Users className="h-3 w-3" />
                    Family Insights
                  </h4>
                  <p className="text-xs text-purple-700 mt-1">
                    Collaborative analytics for shared financial decision-making
                  </p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
