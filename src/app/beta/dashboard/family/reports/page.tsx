"use client";

import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/shared/components/ui/card";
import { <PERSON><PERSON> } from "@/shared/components/ui/button";
import { Badge } from "@/shared/components/ui/badge";
import {
  BarChart3,
  Download,
  Calendar,
  Users,
  TrendingUp,
  TrendingDown,
  DollarSign,
  PieChart,
  FileText,
  Filter,
  Share2,
} from "lucide-react";
import { useBetaFinanceType } from "@/beta/contexts/BetaFinanceTypeContext";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/shared/components/ui/select";

export default function FamilyReportsPage() {
  const { user } = useBetaFinanceType();
  const [selectedPeriod, setSelectedPeriod] = useState("last-month");
  const [selectedMember, setSelectedMember] = useState("all");

  const [mockReports] = useState([
    {
      id: "1",
      name: "Monthly Family Budget Report",
      type: "budget",
      period: "monthly",
      lastGenerated: "2024-01-15",
      description: "Comprehensive budget analysis across all family categories",
      insights: [
        "Over budget in groceries by 15%",
        "Utilities spending reduced by 8%",
        "Entertainment budget well managed",
      ],
    },
    {
      id: "2",
      name: "Quarterly Family Goals Progress",
      type: "goals",
      period: "quarterly",
      lastGenerated: "2024-01-01",
      description: "Progress tracking for all family financial goals",
      insights: [
        "Vacation fund 70% complete",
        "Emergency fund behind by 2 months",
        "College fund ahead of schedule",
      ],
    },
    {
      id: "3",
      name: "Family Cash Flow Analysis",
      type: "cashflow",
      period: "monthly",
      lastGenerated: "2024-01-14",
      description: "Income vs expenses analysis by family member",
      insights: [
        "Net positive cash flow $1,200",
        "Sarah contributed 40% of income",
        "Largest expense category: Housing",
      ],
    },
    {
      id: "4",
      name: "Annual Tax Preparation Report",
      type: "tax",
      period: "yearly",
      lastGenerated: "2024-01-01",
      description: "Tax-related transactions and deductions summary",
      insights: [
        "$5,400 in charitable deductions",
        "$2,800 in business expenses",
        "Home office deduction eligible",
      ],
    },
  ]);

  const [mockMetrics] = useState({
    totalIncome: 8500,
    totalExpenses: 6800,
    netCashFlow: 1700,
    budgetVariance: -150,
    goalsProgress: 68,
    savingsRate: 20,
  });

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: user?.currency || "USD",
    }).format(amount);
  };

  const getReportIcon = (type: string) => {
    switch (type) {
      case "budget":
        return <PieChart className="h-5 w-5 text-blue-500" />;
      case "goals":
        return <TrendingUp className="h-5 w-5 text-green-500" />;
      case "cashflow":
        return <BarChart3 className="h-5 w-5 text-purple-500" />;
      case "tax":
        return <FileText className="h-5 w-5 text-orange-500" />;
      default:
        return <FileText className="h-5 w-5 text-gray-500" />;
    }
  };

  const getReportTypeLabel = (type: string) => {
    switch (type) {
      case "budget":
        return "Budget Analysis";
      case "goals":
        return "Goals Progress";
      case "cashflow":
        return "Cash Flow";
      case "tax":
        return "Tax Report";
      default:
        return "Report";
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Family Reports</h1>
          <p className="text-muted-foreground">
            Comprehensive financial insights for your family
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline">
            <Share2 className="w-4 h-4 mr-2" />
            Share Reports
          </Button>
          <Button>
            <Download className="w-4 h-4 mr-2" />
            Export All
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Report Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block">
                Time Period
              </label>
              <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                <SelectTrigger>
                  <SelectValue placeholder="Select period" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="last-week">Last Week</SelectItem>
                  <SelectItem value="last-month">Last Month</SelectItem>
                  <SelectItem value="last-quarter">Last Quarter</SelectItem>
                  <SelectItem value="last-year">Last Year</SelectItem>
                  <SelectItem value="custom">Custom Range</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">
                Family Member
              </label>
              <Select value={selectedMember} onValueChange={setSelectedMember}>
                <SelectTrigger>
                  <SelectValue placeholder="Select member" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Members</SelectItem>
                  <SelectItem value="john">John</SelectItem>
                  <SelectItem value="sarah">Sarah</SelectItem>
                  <SelectItem value="emma">Emma</SelectItem>
                  <SelectItem value="mike">Mike</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <Button className="w-full">Generate Report</Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Total Income
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4 text-green-500" />
              <span className="text-2xl font-bold">
                {formatCurrency(mockMetrics.totalIncome)}
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Total Expenses
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <TrendingDown className="h-4 w-4 text-red-500" />
              <span className="text-2xl font-bold">
                {formatCurrency(mockMetrics.totalExpenses)}
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Net Cash Flow
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <DollarSign className="h-4 w-4 text-blue-500" />
              <span className="text-2xl font-bold">
                {formatCurrency(mockMetrics.netCashFlow)}
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Budget Variance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <PieChart className="h-4 w-4 text-orange-500" />
              <span
                className={`text-2xl font-bold ${
                  mockMetrics.budgetVariance >= 0
                    ? "text-green-600"
                    : "text-red-600"
                }`}
              >
                {formatCurrency(mockMetrics.budgetVariance)}
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Goals Progress
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4 text-purple-500" />
              <span className="text-2xl font-bold">
                {mockMetrics.goalsProgress}%
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Savings Rate
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4 text-cyan-500" />
              <span className="text-2xl font-bold">
                {mockMetrics.savingsRate}%
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Available Reports */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Available Family Reports
          </CardTitle>
          <CardDescription>
            Pre-built reports tailored for family financial management
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {mockReports.map((report) => (
              <Card
                key={report.id}
                className="border border-border hover:shadow-md transition-shadow"
              >
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {getReportIcon(report.type)}
                      <div>
                        <CardTitle className="text-lg">{report.name}</CardTitle>
                        <CardDescription className="flex items-center gap-2">
                          <Badge variant="outline" className="text-xs">
                            {getReportTypeLabel(report.type)}
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            <Calendar className="w-3 h-3 mr-1" />
                            {report.period}
                          </Badge>
                        </CardDescription>
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <p className="text-sm text-muted-foreground">
                      {report.description}
                    </p>

                    {/* Key Insights */}
                    <div>
                      <h4 className="text-sm font-medium mb-2">Key Insights</h4>
                      <ul className="space-y-1">
                        {report.insights.slice(0, 2).map((insight, index) => (
                          <li
                            key={index}
                            className="text-xs text-muted-foreground flex items-center gap-2"
                          >
                            <div className="w-1 h-1 rounded-full bg-blue-500"></div>
                            {insight}
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* Actions */}
                    <div className="flex gap-2 pt-2">
                      <Button variant="outline" size="sm" className="flex-1">
                        View Report
                      </Button>
                      <Button variant="outline" size="sm" className="flex-1">
                        <Download className="w-3 h-3 mr-1" />
                        Export
                      </Button>
                    </div>

                    <div className="text-xs text-muted-foreground">
                      Last generated:{" "}
                      {new Date(report.lastGenerated).toLocaleDateString()}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Custom Report Builder */}
      <Card className="border-l-4 border-blue-500 bg-blue-50/50">
        <CardContent className="pt-6">
          <div className="flex items-start gap-3">
            <div className="p-2 rounded-full bg-blue-100">
              <BarChart3 className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <h3 className="font-semibold text-blue-900">
                Custom Report Builder
              </h3>
              <p className="text-sm text-blue-700 mt-1">
                Create custom reports tailored to your family&apos;s specific
                needs. Choose from various data sources, time periods, and
                visualization options to get the insights that matter most.
              </p>
              <div className="flex items-center gap-2 mt-3">
                <Button size="sm" variant="outline" className="bg-white">
                  Build Custom Report
                </Button>
                <Badge variant="outline" className="text-xs">
                  Charts & Graphs
                </Badge>
                <Badge variant="outline" className="text-xs">
                  Export Options
                </Badge>
                <Badge variant="outline" className="text-xs">
                  Scheduled Reports
                </Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
