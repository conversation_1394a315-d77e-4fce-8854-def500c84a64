"use client";

import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/shared/components/ui/card";
import { <PERSON><PERSON> } from "@/shared/components/ui/button";
import { Badge } from "@/shared/components/ui/badge";
import { Progress } from "@/shared/components/ui/progress";
import {
  Plus,
  Target,
  Users,
  Calendar,
  TrendingUp,
  Home,
  Plane,
  GraduationCap,
  Car,
  Heart,
  Gift,
} from "lucide-react";
import { useBetaFinanceType } from "@/beta/contexts/BetaFinanceTypeContext";

export default function FamilyGoalsPage() {
  const { user } = useBetaFinanceType();

  const [mockGoals] = useState([
    {
      id: "1",
      name: "Family Vacation to Europe",
      targetAmount: 12000,
      currentAmount: 8400,
      deadline: "2024-07-15",
      category: "Travel",
      icon: "Plane",
      contributors: ["<PERSON>", "<PERSON>", "<PERSON>"],
      monthlyTarget: 800,
      status: "on-track",
      description:
        "Two-week family trip across Europe visiting Italy, France, and Spain",
    },
    {
      id: "2",
      name: "Emergency Fund",
      targetAmount: 50000,
      currentAmount: 32000,
      deadline: "2024-12-31",
      category: "Emergency",
      icon: "Home",
      contributors: ["<PERSON>", "<PERSON>"],
      monthlyTarget: 2000,
      status: "behind",
      description: "6-month emergency fund for family security",
    },
    {
      id: "3",
      name: "Emma's College Fund",
      targetAmount: 80000,
      currentAmount: 45000,
      deadline: "2026-09-01",
      category: "Education",
      icon: "GraduationCap",
      contributors: ["John", "Sarah", "Grandparents"],
      monthlyTarget: 1200,
      status: "ahead",
      description: "College tuition and expenses for Emma's education",
    },
    {
      id: "4",
      name: "New Family Car",
      targetAmount: 35000,
      currentAmount: 15000,
      deadline: "2024-09-01",
      category: "Transportation",
      icon: "Car",
      contributors: ["John", "Sarah"],
      monthlyTarget: 2500,
      status: "on-track",
      description: "Reliable family vehicle for daily commuting and trips",
    },
  ]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: user?.currency || "USD",
    }).format(amount);
  };

  const getProgressPercentage = (current: number, target: number) => {
    return Math.min((current / target) * 100, 100);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "ahead":
        return "text-green-600 bg-green-100";
      case "on-track":
        return "text-blue-600 bg-blue-100";
      case "behind":
        return "text-yellow-600 bg-yellow-100";
      case "at-risk":
        return "text-red-600 bg-red-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  const getIcon = (iconName: string) => {
    const iconMap = {
      Plane: Plane,
      Home: Home,
      GraduationCap: GraduationCap,
      Car: Car,
      Heart: Heart,
      Gift: Gift,
    };
    const Icon = iconMap[iconName as keyof typeof iconMap] || Target;
    return <Icon className="h-6 w-6" />;
  };

  const getDaysRemaining = (deadline: string) => {
    const today = new Date();
    const targetDate = new Date(deadline);
    const diffTime = targetDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const totalTargetAmount = mockGoals.reduce(
    (sum, goal) => sum + goal.targetAmount,
    0
  );
  const totalCurrentAmount = mockGoals.reduce(
    (sum, goal) => sum + goal.currentAmount,
    0
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Family Goals</h1>
          <p className="text-muted-foreground">
            Achieve your family&apos;s financial dreams together
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline">
            <Users className="w-4 h-4 mr-2" />
            Manage Contributors
          </Button>
          <Button>
            <Plus className="w-4 h-4 mr-2" />
            Create Family Goal
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="border-l-4 border-blue-500">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Target className="h-5 w-5 text-blue-500" />
              Total Goal Value
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">
              {formatCurrency(totalTargetAmount)}
            </p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-green-500">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-green-500" />
              Amount Saved
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">
              {formatCurrency(totalCurrentAmount)}
            </p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-purple-500">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Calendar className="h-5 w-5 text-purple-500" />
              Active Goals
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">{mockGoals.length}</p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-orange-500">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Users className="h-5 w-5 text-orange-500" />
              Contributors
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">5</p>
          </CardContent>
        </Card>
      </div>

      {/* Family Goals Progress */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Family Goals Progress
          </CardTitle>
          <CardDescription>
            Combined progress across all family financial goals
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">Overall Progress</span>
                <span className="text-sm text-muted-foreground">
                  {formatCurrency(totalCurrentAmount)} /{" "}
                  {formatCurrency(totalTargetAmount)}
                </span>
              </div>
              <Progress
                value={getProgressPercentage(
                  totalCurrentAmount,
                  totalTargetAmount
                )}
                className="h-3"
              />
              <p className="text-xs text-muted-foreground mt-1">
                {getProgressPercentage(
                  totalCurrentAmount,
                  totalTargetAmount
                ).toFixed(1)}
                % of total family goals achieved
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Individual Goals */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {mockGoals.map((goal) => (
          <Card key={goal.id} className="hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-3 rounded-full bg-gradient-to-br from-blue-500 to-purple-500 text-white">
                    {getIcon(goal.icon)}
                  </div>
                  <div>
                    <CardTitle className="text-lg">{goal.name}</CardTitle>
                    <CardDescription className="flex items-center gap-2">
                      <Badge variant="outline" className="text-xs">
                        {goal.category}
                      </Badge>
                      <Badge
                        variant="outline"
                        className={`text-xs ${getStatusColor(goal.status)}`}
                      >
                        {goal.status.replace("-", " ")}
                      </Badge>
                    </CardDescription>
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Progress */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">Progress</span>
                    <span className="text-sm text-muted-foreground">
                      {formatCurrency(goal.currentAmount)} /{" "}
                      {formatCurrency(goal.targetAmount)}
                    </span>
                  </div>
                  <Progress
                    value={getProgressPercentage(
                      goal.currentAmount,
                      goal.targetAmount
                    )}
                    className="h-3"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    {getProgressPercentage(
                      goal.currentAmount,
                      goal.targetAmount
                    ).toFixed(1)}
                    % complete
                  </p>
                </div>

                {/* Timeline */}
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium">Target Date</p>
                    <p className="text-sm text-muted-foreground">
                      {new Date(goal.deadline).toLocaleDateString()}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium">
                      {getDaysRemaining(goal.deadline)} days
                    </p>
                    <p className="text-sm text-muted-foreground">remaining</p>
                  </div>
                </div>

                {/* Monthly Target */}
                <div className="bg-gray-50 rounded-lg p-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Monthly Target</span>
                    <span className="text-sm font-bold">
                      {formatCurrency(goal.monthlyTarget)}
                    </span>
                  </div>
                </div>

                {/* Contributors */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">Contributors</span>
                    <span className="text-sm text-muted-foreground">
                      {goal.contributors.length} members
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    {goal.contributors.slice(0, 4).map((contributor, index) => (
                      <div
                        key={index}
                        className="w-8 h-8 rounded-full bg-gradient-to-br from-green-500 to-blue-500 flex items-center justify-center text-white text-xs font-medium"
                      >
                        {contributor.charAt(0)}
                      </div>
                    ))}
                    {goal.contributors.length > 4 && (
                      <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 text-xs font-medium">
                        +{goal.contributors.length - 4}
                      </div>
                    )}
                  </div>
                </div>

                {/* Description */}
                <p className="text-xs text-muted-foreground italic">
                  {goal.description}
                </p>

                {/* Actions */}
                <div className="flex gap-2 pt-2">
                  <Button variant="outline" size="sm" className="flex-1">
                    Contribute
                  </Button>
                  <Button variant="outline" size="sm" className="flex-1">
                    View Details
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Family Goal Benefits */}
      <Card className="border-l-4 border-purple-500 bg-purple-50/50">
        <CardContent className="pt-6">
          <div className="flex items-start gap-3">
            <div className="p-2 rounded-full bg-purple-100">
              <Heart className="h-5 w-5 text-purple-600" />
            </div>
            <div>
              <h3 className="font-semibold text-purple-900">
                Family Goal Success
              </h3>
              <p className="text-sm text-purple-700 mt-1">
                Achieve your dreams faster by working together as a family. Set
                shared goals, track progress in real-time, and celebrate
                milestones together. Every contribution brings your family
                closer to financial success.
              </p>
              <div className="flex items-center gap-2 mt-3">
                <Badge variant="outline" className="text-xs">
                  Milestone Tracking
                </Badge>
                <Badge variant="outline" className="text-xs">
                  Progress Notifications
                </Badge>
                <Badge variant="outline" className="text-xs">
                  Shared Contributions
                </Badge>
                <Badge variant="outline" className="text-xs">
                  Goal Insights
                </Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
