"use client";

import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/shared/components/ui/card";
import { <PERSON><PERSON> } from "@/shared/components/ui/button";
import { Badge } from "@/shared/components/ui/badge";
import { Input } from "@/shared/components/ui/input";
import {
  Plus,
  CreditCard,
  Building2,
  Banknote,
  Wallet,
  Users,
  Eye,
  EyeOff,
  Search,
  MoreVertical,
  Lock,
  Unlock,
} from "lucide-react";
import { useBetaFinanceType } from "@/beta/contexts/BetaFinanceTypeContext";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/shared/components/ui/dropdown-menu";

export default function FamilyAccountsPage() {
  const { user } = useBetaFinanceType();
  const [showBalances, setShowBalances] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");

  const [mockAccounts] = useState([
    {
      id: "1",
      name: "Family Joint Checking",
      type: "checking",
      balance: 12450.75,
      institution: "Chase Bank",
      permission: "full",
      isShared: true,
      memberCount: 4,
      lastActivity: "2024-01-15",
    },
    {
      id: "2",
      name: "Emergency Fund",
      type: "savings",
      balance: 25000.0,
      institution: "Ally Bank",
      permission: "view",
      isShared: true,
      memberCount: 2,
      lastActivity: "2024-01-10",
    },
    {
      id: "3",
      name: "Family Credit Card",
      type: "credit card",
      balance: -2340.5,
      institution: "Capital One",
      permission: "limited",
      isShared: true,
      memberCount: 3,
      lastActivity: "2024-01-14",
    },
  ]);

  const formatCurrency = (amount: number) => {
    if (!showBalances) return "••••••";
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: user?.currency || "USD",
    }).format(amount);
  };

  const getAccountIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case "checking":
      case "savings":
        return <Building2 className="h-6 w-6 text-blue-600" />;
      case "credit card":
        return <CreditCard className="h-6 w-6 text-purple-600" />;
      case "cash":
        return <Banknote className="h-6 w-6 text-green-600" />;
      default:
        return <Wallet className="h-6 w-6 text-gray-600" />;
    }
  };

  const getPermissionIcon = (permission: string) => {
    switch (permission) {
      case "full":
        return <Unlock className="h-4 w-4 text-green-500" />;
      case "view":
        return <Eye className="h-4 w-4 text-blue-500" />;
      case "limited":
        return <Lock className="h-4 w-4 text-yellow-500" />;
      default:
        return <Lock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getPermissionLabel = (permission: string) => {
    switch (permission) {
      case "full":
        return "Full Access";
      case "view":
        return "View Only";
      case "limited":
        return "Limited";
      default:
        return "No Access";
    }
  };

  const filteredAccounts = mockAccounts.filter(
    (account) =>
      account.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      account.type.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const totalBalance = mockAccounts.reduce(
    (sum, account) => sum + account.balance,
    0
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Shared Family Accounts
          </h1>
          <p className="text-muted-foreground">
            Manage accounts shared across your family groups
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowBalances(!showBalances)}
          >
            {showBalances ? (
              <Eye className="w-4 h-4 mr-2" />
            ) : (
              <EyeOff className="w-4 h-4 mr-2" />
            )}
            {showBalances ? "Hide" : "Show"} Balances
          </Button>
          <Button>
            <Plus className="w-4 h-4 mr-2" />
            Add Shared Account
          </Button>
        </div>
      </div>

      {/* Summary Card */}
      <Card className="border-l-4 border-green-500">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Family Account Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <p className="text-sm text-muted-foreground">
                Total Family Balance
              </p>
              <p className="text-2xl font-bold">
                {formatCurrency(totalBalance)}
              </p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Shared Accounts</p>
              <p className="text-2xl font-bold">{mockAccounts.length}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Family Members</p>
              <p className="text-2xl font-bold">4</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Search */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search shared accounts..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Accounts Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredAccounts.map((account) => (
          <Card key={account.id} className="hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="flex items-center gap-3">
                {getAccountIcon(account.type)}
                <div>
                  <CardTitle className="text-lg">{account.name}</CardTitle>
                  <CardDescription className="flex items-center gap-1">
                    {account.type}
                    <Users className="h-3 w-3 ml-1" />
                    {account.memberCount}
                  </CardDescription>
                </div>
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem>View Details</DropdownMenuItem>
                  <DropdownMenuItem>View Transactions</DropdownMenuItem>
                  <DropdownMenuItem>Manage Permissions</DropdownMenuItem>
                  <DropdownMenuItem>Edit Account</DropdownMenuItem>
                  <DropdownMenuItem className="text-destructive">
                    Remove from Family
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div>
                  <p className="text-sm text-muted-foreground">
                    Current Balance
                  </p>
                  <p
                    className={`text-2xl font-bold ${
                      account.balance >= 0 ? "" : "text-red-600"
                    }`}
                  >
                    {formatCurrency(account.balance)}
                  </p>
                </div>

                {account.institution && (
                  <div>
                    <p className="text-sm text-muted-foreground">Institution</p>
                    <p className="text-sm font-medium">{account.institution}</p>
                  </div>
                )}

                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">
                    Your Permission
                  </span>
                  <div className="flex items-center gap-1">
                    {getPermissionIcon(account.permission)}
                    <Badge variant="outline" className="text-xs">
                      {getPermissionLabel(account.permission)}
                    </Badge>
                  </div>
                </div>

                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Family Members</span>
                  <div className="flex items-center gap-1">
                    <Users className="h-3 w-3" />
                    <span>{account.memberCount} members</span>
                  </div>
                </div>

                <div className="text-xs text-muted-foreground">
                  Last activity:{" "}
                  {new Date(account.lastActivity).toLocaleDateString()}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Permission Help */}
      <Card className="border-l-4 border-blue-500 bg-blue-50/50">
        <CardContent className="pt-6">
          <div className="flex items-start gap-3">
            <div className="p-2 rounded-full bg-blue-100">
              <Lock className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <h3 className="font-semibold text-blue-900">
                Account Permissions
              </h3>
              <p className="text-sm text-blue-700 mt-1">
                Family accounts have different permission levels to ensure
                financial security and appropriate access.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-3">
                <div className="flex items-center gap-2">
                  <Unlock className="h-4 w-4 text-green-500" />
                  <div>
                    <p className="text-xs font-medium text-green-700">
                      Full Access
                    </p>
                    <p className="text-xs text-green-600">
                      View, add, edit transactions
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Eye className="h-4 w-4 text-blue-500" />
                  <div>
                    <p className="text-xs font-medium text-blue-700">
                      View Only
                    </p>
                    <p className="text-xs text-blue-600">
                      View balance and transactions
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Lock className="h-4 w-4 text-yellow-500" />
                  <div>
                    <p className="text-xs font-medium text-yellow-700">
                      Limited
                    </p>
                    <p className="text-xs text-yellow-600">View balance only</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
