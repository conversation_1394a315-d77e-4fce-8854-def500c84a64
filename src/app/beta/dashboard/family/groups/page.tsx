"use client";

import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/shared/components/ui/card";
import { Button } from "@/shared/components/ui/button";
import { Badge } from "@/shared/components/ui/badge";
import {
  Users,
  Plus,
  UserPlus,
  Settings,
  Crown,
  Shield,
  Mail,
  MoreVertical,
} from "lucide-react";
import { useBetaFinanceType } from "@/beta/contexts/BetaFinanceTypeContext";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/shared/components/ui/dropdown-menu";

export default function FamilyGroupsPage() {
  const { user } = useBetaFinanceType();
  const [mockGroups] = useState([
    {
      id: "1",
      name: "The Smith Family",
      memberCount: 4,
      role: "admin",
      joinedAt: "2024-01-15",
      totalBalance: 45600,
      isActive: true,
    },
    {
      id: "2",
      name: "Extended Family Pool",
      memberCount: 8,
      role: "member",
      joinedAt: "2024-02-20",
      totalBalance: 78900,
      isActive: true,
    },
  ]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: user?.currency || "USD",
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Family Groups</h1>
          <p className="text-muted-foreground">
            Manage your family financial collaborations
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline">
            <Mail className="w-4 h-4 mr-2" />
            Invitations
          </Button>
          <Button>
            <Plus className="w-4 h-4 mr-2" />
            Create Group
          </Button>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="hover:shadow-lg transition-shadow cursor-pointer">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Plus className="h-5 w-5 text-green-500" />
              Create New Group
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              Start a new family financial group and invite members to
              collaborate.
            </p>
            <Button className="w-full">Get Started</Button>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow cursor-pointer">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <UserPlus className="h-5 w-5 text-blue-500" />
              Join Existing Group
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              Join a family group using an invitation code or link.
            </p>
            <Button variant="outline" className="w-full">
              Enter Code
            </Button>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow cursor-pointer">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Mail className="h-5 w-5 text-purple-500" />
              Pending Invitations
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              View and manage your pending group invitations.
            </p>
            <Button variant="outline" className="w-full">
              View Invitations
              <Badge className="ml-2">2</Badge>
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Active Groups */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Your Family Groups
          </CardTitle>
          <CardDescription>
            Groups you&apos;re currently part of
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {mockGroups.map((group) => (
              <Card key={group.id} className="border border-border">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-green-500 to-blue-500 flex items-center justify-center">
                        <Users className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-lg">{group.name}</h3>
                        <div className="flex items-center gap-4 text-sm text-muted-foreground mt-1">
                          <span>{group.memberCount} members</span>
                          <span>•</span>
                          <span>
                            Joined{" "}
                            {new Date(group.joinedAt).toLocaleDateString()}
                          </span>
                          <span>•</span>
                          <Badge
                            variant={
                              group.role === "admin" ? "default" : "secondary"
                            }
                            className="text-xs"
                          >
                            {group.role === "admin" ? (
                              <>
                                <Crown className="w-3 h-3 mr-1" />
                                Admin
                              </>
                            ) : (
                              <>
                                <Shield className="w-3 h-3 mr-1" />
                                Member
                              </>
                            )}
                          </Badge>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-4">
                      <div className="text-right">
                        <p className="font-semibold text-lg">
                          {formatCurrency(group.totalBalance)}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          Group Balance
                        </p>
                      </div>

                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem>View Dashboard</DropdownMenuItem>
                          <DropdownMenuItem>Manage Members</DropdownMenuItem>
                          <DropdownMenuItem>Group Settings</DropdownMenuItem>
                          {group.role === "admin" && (
                            <>
                              <DropdownMenuItem>
                                Invite Members
                              </DropdownMenuItem>
                              <DropdownMenuItem className="text-destructive">
                                Delete Group
                              </DropdownMenuItem>
                            </>
                          )}
                          {group.role === "member" && (
                            <DropdownMenuItem className="text-destructive">
                              Leave Group
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>

                  <div className="grid grid-cols-4 gap-4 mt-6 pt-4 border-t">
                    <Button variant="outline" size="sm" className="w-full">
                      Dashboard
                    </Button>
                    <Button variant="outline" size="sm" className="w-full">
                      Accounts
                    </Button>
                    <Button variant="outline" size="sm" className="w-full">
                      Budgets
                    </Button>
                    <Button variant="outline" size="sm" className="w-full">
                      Reports
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Help Section */}
      <Card className="border-l-4 border-blue-500 bg-blue-50/50">
        <CardContent className="pt-6">
          <div className="flex items-start gap-3">
            <div className="p-2 rounded-full bg-blue-100">
              <Shield className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <h3 className="font-semibold text-blue-900">
                Family Group Security
              </h3>
              <p className="text-sm text-blue-700 mt-1">
                Family groups use role-based permissions to ensure financial
                data is shared safely. Admins can manage members and settings,
                while members can view and contribute to shared financial goals
                and budgets.
              </p>
              <div className="flex items-center gap-2 mt-3">
                <Badge variant="outline" className="text-xs">
                  Bank-level Security
                </Badge>
                <Badge variant="outline" className="text-xs">
                  Role-based Access
                </Badge>
                <Badge variant="outline" className="text-xs">
                  Audit Logs
                </Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
