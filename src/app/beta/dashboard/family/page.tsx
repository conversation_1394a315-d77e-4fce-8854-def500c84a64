"use client";

import React from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/shared/components/ui/card";
import { Button } from "@/shared/components/ui/button";
import { Badge } from "@/shared/components/ui/badge";
import {
  Users,
  Heart,
  Plus,
  Sparkles,
  MessageCircle,
  Shield,
  TrendingUp,
  Target,
  ArrowRight,
  Building2,
  UserPlus,
} from "lucide-react";
import { useBetaFinanceType } from "@/beta/contexts/BetaFinanceTypeContext";
import { BetaFeedbackWidget } from "@/beta/components/BetaFeedbackWidget";

export default function FamilyDashboardPage() {
  const { user } = useBetaFinanceType();

  const getWelcomeMessage = () => {
    const hour = new Date().getHours();
    const greeting =
      hour < 12
        ? "Good morning"
        : hour < 18
        ? "Good afternoon"
        : "Good evening";
    return `${greeting}, ${user?.full_name?.split(" ")[0] || "Beta User"}!`;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <Users className="h-8 w-8 text-green-600" />
            {getWelcomeMessage()}
          </h1>
          <p className="text-muted-foreground">
            Welcome to Family Finance - designed for collaboration and shared
            financial goals
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge
            variant="secondary"
            className="bg-gradient-to-r from-green-600 to-blue-600 text-white"
          >
            <Sparkles className="w-3 h-3 mr-1" />
            Family Finance Beta
          </Badge>
          <BetaFeedbackWidget
            trigger={
              <Button variant="outline" size="sm">
                <MessageCircle className="w-4 h-4 mr-2" />
                Feedback
              </Button>
            }
          />
        </div>
      </div>

      {/* Beta Welcome Message */}
      <Card className="border-l-4 border-green-500 bg-green-50/50">
        <CardContent className="pt-6">
          <div className="flex items-start gap-3">
            <div className="p-2 rounded-full bg-green-100">
              <Heart className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <h3 className="font-semibold text-green-900">
                Welcome to Family Finance Beta!
              </h3>
              <p className="text-sm text-green-700 mt-1">
                You&apos;re experiencing a collaboration-first financial
                management interface designed specifically for families. Share
                budgets, track goals together, and build financial harmony.
              </p>
              <div className="flex items-center gap-2 mt-3">
                <Badge variant="outline" className="text-xs">
                  Real-time Collaboration
                </Badge>
                <Badge variant="outline" className="text-xs">
                  Shared Goals
                </Badge>
                <Badge variant="outline" className="text-xs">
                  Family Insights
                </Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="group hover:shadow-lg transition-all duration-300 border-2 hover:border-green-500/50">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Plus className="h-5 w-5 text-green-500" />
              Create Family Group
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              Start your family&apos;s financial journey together. Create a
              group to share budgets, goals, and accounts.
            </p>
            <Button className="w-full group-hover:bg-green-600 transition-colors">
              <Users className="w-4 h-4 mr-2" />
              Create Group
            </Button>
          </CardContent>
        </Card>

        <Card className="group hover:shadow-lg transition-all duration-300 border-2 hover:border-blue-500/50">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <UserPlus className="h-5 w-5 text-blue-500" />
              Join Family Group
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              Join an existing family group using an invitation link or code
              from a family member.
            </p>
            <Button
              variant="outline"
              className="w-full group-hover:bg-blue-50 transition-colors"
            >
              <Heart className="w-4 h-4 mr-2" />
              Join Group
            </Button>
          </CardContent>
        </Card>

        <Card className="group hover:shadow-lg transition-all duration-300 border-2 hover:border-purple-500/50">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Shield className="h-5 w-5 text-purple-500" />
              Family Security
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-4">
              Learn about privacy settings, permissions, and how we keep your
              family&apos;s financial data secure.
            </p>
            <Button
              variant="outline"
              className="w-full group-hover:bg-purple-50 transition-colors"
            >
              <Shield className="w-4 h-4 mr-2" />
              Security Guide
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Family Features Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Heart className="h-5 w-5 text-red-500" />
              Family Collaboration Features
            </CardTitle>
            <CardDescription>What makes family finance special</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-start gap-3 p-3 rounded-lg bg-green-50 border border-green-200">
                <Users className="h-5 w-5 text-green-600 mt-0.5" />
                <div>
                  <h4 className="font-semibold text-green-900">
                    Shared Budgets
                  </h4>
                  <p className="text-sm text-green-700">
                    Create budgets that everyone can contribute to and track
                    together in real-time.
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3 p-3 rounded-lg bg-blue-50 border border-blue-200">
                <Target className="h-5 w-5 text-blue-600 mt-0.5" />
                <div>
                  <h4 className="font-semibold text-blue-900">Family Goals</h4>
                  <p className="text-sm text-blue-700">
                    Set shared financial goals like vacation funds, emergency
                    savings, or home improvements.
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3 p-3 rounded-lg bg-purple-50 border border-purple-200">
                <Building2 className="h-5 w-5 text-purple-600 mt-0.5" />
                <div>
                  <h4 className="font-semibold text-purple-900">
                    Shared Accounts
                  </h4>
                  <p className="text-sm text-purple-700">
                    Manage joint accounts and track family expenses with proper
                    permissions and visibility.
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-green-500" />
              Getting Started Guide
            </CardTitle>
            <CardDescription>
              Your path to family financial harmony
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 rounded-lg border border-gray-200">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center">
                    <span className="text-sm font-semibold text-green-700">
                      1
                    </span>
                  </div>
                  <div>
                    <h4 className="font-medium">Create or Join Group</h4>
                    <p className="text-sm text-muted-foreground">
                      Start with family group setup
                    </p>
                  </div>
                </div>
                <Button size="sm" variant="outline">
                  Start
                </Button>
              </div>

              <div className="flex items-center justify-between p-3 rounded-lg border border-gray-200">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
                    <span className="text-sm font-semibold text-blue-700">
                      2
                    </span>
                  </div>
                  <div>
                    <h4 className="font-medium">Add Shared Accounts</h4>
                    <p className="text-sm text-muted-foreground">
                      Connect your family&apos;s accounts
                    </p>
                  </div>
                </div>
                <Button size="sm" variant="outline" disabled>
                  Next
                </Button>
              </div>

              <div className="flex items-center justify-between p-3 rounded-lg border border-gray-200">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center">
                    <span className="text-sm font-semibold text-purple-700">
                      3
                    </span>
                  </div>
                  <div>
                    <h4 className="font-medium">Set Family Goals</h4>
                    <p className="text-sm text-muted-foreground">
                      Define shared objectives
                    </p>
                  </div>
                </div>
                <Button size="sm" variant="outline" disabled>
                  Later
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Beta Development Notice */}
      <Card className="border-2 border-dashed border-orange-200 bg-orange-50/50">
        <CardContent className="text-center py-12">
          <div className="mx-auto w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mb-4">
            <Users className="w-8 h-8 text-orange-600" />
          </div>
          <h3 className="text-xl font-semibold mb-2">
            Family Dashboard Coming Soon
          </h3>
          <p className="text-muted-foreground mb-4 max-w-2xl mx-auto">
            We&apos;re actively developing the full family finance experience.
            This includes family group management, shared budgets, collaborative
            goal setting, and real-time activity feeds. Your feedback helps us
            prioritize which features to build first!
          </p>
          <div className="flex items-center justify-center gap-2">
            <Button variant="outline">
              <ArrowRight className="w-4 h-4 mr-2" />
              Try Personal Finance
            </Button>
            <BetaFeedbackWidget
              trigger={
                <Button>
                  <MessageCircle className="w-4 h-4 mr-2" />
                  Share Ideas
                </Button>
              }
            />
          </div>
        </CardContent>
      </Card>

      {/* Family Finance Benefits */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-purple-500" />
            Why Choose Family Finance?
          </CardTitle>
          <CardDescription>
            Benefits of collaborative financial management
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h4 className="font-semibold text-green-900">
                🎯 Better Financial Alignment
              </h4>
              <p className="text-sm text-muted-foreground">
                When everyone can see the family&apos;s financial picture,
                it&apos;s easier to make decisions that support shared goals and
                priorities.
              </p>

              <h4 className="font-semibold text-blue-900">
                💬 Improved Communication
              </h4>
              <p className="text-sm text-muted-foreground">
                Reduce money-related stress with transparent spending tracking
                and collaborative budget planning that keeps everyone informed.
              </p>
            </div>

            <div className="space-y-4">
              <h4 className="font-semibold text-purple-900">
                🚀 Faster Goal Achievement
              </h4>
              <p className="text-sm text-muted-foreground">
                Families using collaborative financial tools reach their savings
                goals 40% faster than those managing finances individually.
              </p>

              <h4 className="font-semibold text-red-900">
                🛡️ Enhanced Security
              </h4>
              <p className="text-sm text-muted-foreground">
                Role-based permissions ensure everyone has appropriate access
                while maintaining privacy and security for sensitive financial
                information.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
