"use client";

import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/shared/components/ui/card";
import { <PERSON><PERSON> } from "@/shared/components/ui/button";
import { Badge } from "@/shared/components/ui/badge";
import { Progress } from "@/shared/components/ui/progress";
import {
  Plus,
  PieChart,
  Users,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  DollarSign,
  Calendar,
  Target,
} from "lucide-react";
import { useBetaFinanceType } from "@/beta/contexts/BetaFinanceTypeContext";

export default function FamilyBudgetsPage() {
  const { user } = useBetaFinanceType();

  const [mockBudgets] = useState([
    {
      id: "1",
      name: "Family Groceries",
      category: "Food",
      amount: 800,
      spent: 650,
      period: "monthly",
      contributors: ["<PERSON>", "<PERSON>", "<PERSON>"],
      lastUpdated: "2024-01-14",
      status: "good",
    },
    {
      id: "2",
      name: "Utilities & Bills",
      category: "Utilities",
      amount: 450,
      spent: 425,
      period: "monthly",
      contributors: ["<PERSON>", "<PERSON>"],
      lastUpdated: "2024-01-13",
      status: "warning",
    },
    {
      id: "3",
      name: "Family Vacation Fund",
      category: "Travel",
      amount: 3000,
      spent: 500,
      period: "yearly",
      contributors: ["John", "Sarah", "Emma", "Mike"],
      lastUpdated: "2024-01-10",
      status: "excellent",
    },
  ]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: user?.currency || "USD",
    }).format(amount);
  };

  const getProgressPercentage = (spent: number, amount: number) => {
    return Math.min((spent / amount) * 100, 100);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "excellent":
        return "text-green-600 bg-green-100";
      case "good":
        return "text-blue-600 bg-blue-100";
      case "warning":
        return "text-yellow-600 bg-yellow-100";
      case "danger":
        return "text-red-600 bg-red-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "excellent":
        return <TrendingUp className="h-4 w-4" />;
      case "good":
        return <Target className="h-4 w-4" />;
      case "warning":
        return <AlertTriangle className="h-4 w-4" />;
      case "danger":
        return <TrendingDown className="h-4 w-4" />;
      default:
        return <DollarSign className="h-4 w-4" />;
    }
  };

  const totalBudgeted = mockBudgets.reduce(
    (sum, budget) => sum + budget.amount,
    0
  );
  const totalSpent = mockBudgets.reduce((sum, budget) => sum + budget.spent, 0);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Family Budgets</h1>
          <p className="text-muted-foreground">
            Collaborative budget management for your family
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline">
            <Users className="w-4 h-4 mr-2" />
            Manage Contributors
          </Button>
          <Button>
            <Plus className="w-4 h-4 mr-2" />
            Create Family Budget
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="border-l-4 border-blue-500">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <DollarSign className="h-5 w-5 text-blue-500" />
              Total Budgeted
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">
              {formatCurrency(totalBudgeted)}
            </p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-green-500">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-green-500" />
              Total Spent
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">{formatCurrency(totalSpent)}</p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-purple-500">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <PieChart className="h-5 w-5 text-purple-500" />
              Active Budgets
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">{mockBudgets.length}</p>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-orange-500">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Users className="h-5 w-5 text-orange-500" />
              Contributors
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-2xl font-bold">4</p>
          </CardContent>
        </Card>
      </div>

      {/* Family Budget Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <PieChart className="h-5 w-5" />
            Family Budget Overview
          </CardTitle>
          <CardDescription>
            Combined spending across all family budgets
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">Overall Progress</span>
                <span className="text-sm text-muted-foreground">
                  {formatCurrency(totalSpent)} / {formatCurrency(totalBudgeted)}
                </span>
              </div>
              <Progress
                value={getProgressPercentage(totalSpent, totalBudgeted)}
                className="h-3"
              />
              <p className="text-xs text-muted-foreground mt-1">
                {(((totalBudgeted - totalSpent) / totalBudgeted) * 100).toFixed(
                  1
                )}
                % remaining this period
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Individual Family Budgets */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {mockBudgets.map((budget) => (
          <Card key={budget.id} className="hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg">{budget.name}</CardTitle>
                  <CardDescription className="flex items-center gap-2">
                    <Badge variant="outline" className="text-xs">
                      {budget.category}
                    </Badge>
                    <Badge variant="outline" className="text-xs">
                      <Calendar className="w-3 h-3 mr-1" />
                      {budget.period}
                    </Badge>
                  </CardDescription>
                </div>
                <div
                  className={`p-2 rounded-full ${getStatusColor(
                    budget.status
                  )}`}
                >
                  {getStatusIcon(budget.status)}
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Progress */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">Progress</span>
                    <span className="text-sm text-muted-foreground">
                      {formatCurrency(budget.spent)} /{" "}
                      {formatCurrency(budget.amount)}
                    </span>
                  </div>
                  <Progress
                    value={getProgressPercentage(budget.spent, budget.amount)}
                    className="h-2"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    {getProgressPercentage(budget.spent, budget.amount).toFixed(
                      1
                    )}
                    % used
                  </p>
                </div>

                {/* Contributors */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">Contributors</span>
                    <span className="text-sm text-muted-foreground">
                      {budget.contributors.length} members
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    {budget.contributors
                      .slice(0, 3)
                      .map((contributor, index) => (
                        <div
                          key={index}
                          className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-500 flex items-center justify-center text-white text-xs font-medium"
                        >
                          {contributor.charAt(0)}
                        </div>
                      ))}
                    {budget.contributors.length > 3 && (
                      <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 text-xs font-medium">
                        +{budget.contributors.length - 3}
                      </div>
                    )}
                  </div>
                </div>

                {/* Actions */}
                <div className="flex gap-2 pt-2">
                  <Button variant="outline" size="sm" className="flex-1">
                    View Details
                  </Button>
                  <Button variant="outline" size="sm" className="flex-1">
                    Add Expense
                  </Button>
                </div>

                <div className="text-xs text-muted-foreground">
                  Last updated:{" "}
                  {new Date(budget.lastUpdated).toLocaleDateString()}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Collaboration Features */}
      <Card className="border-l-4 border-green-500 bg-green-50/50">
        <CardContent className="pt-6">
          <div className="flex items-start gap-3">
            <div className="p-2 rounded-full bg-green-100">
              <Users className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <h3 className="font-semibold text-green-900">
                Family Budget Collaboration
              </h3>
              <p className="text-sm text-green-700 mt-1">
                Family budgets allow multiple members to contribute expenses and
                track progress together. Set spending limits, assign categories,
                and receive notifications when budgets need attention.
              </p>
              <div className="flex items-center gap-2 mt-3">
                <Badge variant="outline" className="text-xs">
                  Real-time Updates
                </Badge>
                <Badge variant="outline" className="text-xs">
                  Expense Notifications
                </Badge>
                <Badge variant="outline" className="text-xs">
                  Category Tracking
                </Badge>
                <Badge variant="outline" className="text-xs">
                  Member Permissions
                </Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
