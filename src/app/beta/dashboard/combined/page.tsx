"use client";

import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/shared/components/ui/card";
import { <PERSON><PERSON> } from "@/shared/components/ui/button";
import { Badge } from "@/shared/components/ui/badge";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rigger,
} from "@/shared/components/ui/tabs";
import {
  Layers,
  User,
  Users,
  Sparkles,
  MessageCircle,
  TrendingUp,
  Settings,
} from "lucide-react";
import { useBetaFinanceType } from "@/beta/contexts/BetaFinanceTypeContext";
import { BetaFeedbackWidget } from "@/beta/components/BetaFeedbackWidget";
import { PersonalOverview } from "@/beta/dashboards/personal/components";
import {
  CombinedOverview,
  FamilyOverview,
} from "@/beta/dashboards/combined/components";

export default function CombinedDashboardPage() {
  const { user } = useBetaFinanceType();
  const [activeMode, setActiveMode] = useState<
    "personal" | "family" | "unified"
  >("unified");

  const getWelcomeMessage = () => {
    const hour = new Date().getHours();
    const greeting =
      hour < 12
        ? "Good morning"
        : hour < 18
        ? "Good afternoon"
        : "Good evening";
    return `${greeting}, ${user?.full_name?.split(" ")[0] || "Beta User"}!`;
  };

  // Handler functions for common actions
  const handleAddTransaction = () => {
    // TODO: Implement add transaction dialog
    console.log("Add transaction clicked");
  };

  const handleAddAccount = () => {
    // TODO: Implement add account dialog
    console.log("Add account clicked");
  };

  const handleViewBudgets = () => {
    // TODO: Navigate to budgets page
    console.log("View budgets clicked");
  };

  const handleViewGoals = () => {
    // TODO: Navigate to goals page
    console.log("View goals clicked");
  };

  const handleCreateGroup = () => {
    // TODO: Implement create family group dialog
    console.log("Create group clicked");
  };

  const handleJoinGroup = () => {
    // TODO: Implement join family group dialog
    console.log("Join group clicked");
  };

  const handleViewGroup = (groupId: string) => {
    // TODO: Navigate to specific family group page
    console.log("View group clicked:", groupId);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <Layers className="h-8 w-8 text-purple-600" />
            {getWelcomeMessage()}
          </h1>
          <p className="text-muted-foreground">
            Complete financial control - switch between personal, family, and
            unified views
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge
            variant="secondary"
            className="bg-gradient-to-r from-purple-600 to-blue-600 text-white"
          >
            <Sparkles className="w-3 h-3 mr-1" />
            Combined Finance Beta
          </Badge>
          <BetaFeedbackWidget
            trigger={
              <Button variant="outline" size="sm">
                <MessageCircle className="w-4 h-4 mr-2" />
                Feedback
              </Button>
            }
          />
        </div>
      </div>

      {/* Beta Welcome Message */}
      <Card className="border-l-4 border-purple-500 bg-purple-50/50">
        <CardContent className="pt-6">
          <div className="flex items-start gap-3">
            <div className="p-2 rounded-full bg-purple-100">
              <Settings className="h-5 w-5 text-purple-600" />
            </div>
            <div>
              <h3 className="font-semibold text-purple-900">
                Welcome to Combined Finance Beta!
              </h3>
              <p className="text-sm text-purple-700 mt-1">
                You now have access to the complete financial management
                experience. Switch between personal, family, and unified views
                to get the complete picture of your financial life with real
                data integration.
              </p>
              <div className="flex items-center gap-2 mt-3">
                <Badge variant="outline" className="text-xs">
                  Real Data Integration
                </Badge>
                <Badge variant="outline" className="text-xs">
                  Smart Analytics
                </Badge>
                <Badge variant="outline" className="text-xs">
                  Cross-Account Insights
                </Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Mode Switcher */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Layers className="h-5 w-5" />
            Finance Mode
          </CardTitle>
          <CardDescription>
            Switch between different views of your financial data with real-time
            integration
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs
            value={activeMode}
            onValueChange={(value) =>
              setActiveMode(value as "personal" | "family" | "unified")
            }
          >
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="personal" className="flex items-center gap-2">
                <User className="w-4 h-4" />
                Personal
              </TabsTrigger>
              <TabsTrigger value="family" className="flex items-center gap-2">
                <Users className="w-4 h-4" />
                Family
              </TabsTrigger>
              <TabsTrigger value="unified" className="flex items-center gap-2">
                <Layers className="w-4 h-4" />
                Unified
              </TabsTrigger>
            </TabsList>

            <div className="mt-4">
              <TabsContent value="personal" className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card className="border-l-4 border-blue-500">
                    <CardContent className="pt-6">
                      <div className="flex items-center gap-2">
                        <User className="h-5 w-5 text-blue-600" />
                        <h4 className="font-semibold">Personal Mode</h4>
                      </div>
                      <p className="text-sm text-muted-foreground mt-2">
                        Focus on your individual financial accounts, goals, and
                        budgets with real-time data.
                      </p>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-semibold text-blue-600">
                            Live Data
                          </h4>
                          <p className="text-sm text-muted-foreground">
                            Personal Analytics
                          </p>
                        </div>
                        <TrendingUp className="h-6 w-6 text-blue-600" />
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-semibold text-green-600">
                            Active
                          </h4>
                          <p className="text-sm text-muted-foreground">
                            Personal Goals
                          </p>
                        </div>
                        <TrendingUp className="h-6 w-6 text-green-600" />
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <PersonalOverview
                  onAddTransaction={handleAddTransaction}
                  onAddAccount={handleAddAccount}
                  onViewBudgets={handleViewBudgets}
                  onViewGoals={handleViewGoals}
                />
              </TabsContent>

              <TabsContent value="family" className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card className="border-l-4 border-green-500">
                    <CardContent className="pt-6">
                      <div className="flex items-center gap-2">
                        <Users className="h-5 w-5 text-green-600" />
                        <h4 className="font-semibold">Family Mode</h4>
                      </div>
                      <p className="text-sm text-muted-foreground mt-2">
                        View shared accounts, family budgets, and collaborative
                        goals with real-time synchronization.
                      </p>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-semibold text-green-600">
                            Live Data
                          </h4>
                          <p className="text-sm text-muted-foreground">
                            Family Analytics
                          </p>
                        </div>
                        <Users className="h-6 w-6 text-green-600" />
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-semibold text-purple-600">
                            Groups
                          </h4>
                          <p className="text-sm text-muted-foreground">
                            Family Groups
                          </p>
                        </div>
                        <Users className="h-6 w-6 text-purple-600" />
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <FamilyOverview
                  onCreateGroup={handleCreateGroup}
                  onJoinGroup={handleJoinGroup}
                  onViewGroup={handleViewGroup}
                  onAddTransaction={handleAddTransaction}
                />
              </TabsContent>

              <TabsContent value="unified" className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <Card className="border-l-4 border-purple-500">
                    <CardContent className="pt-6">
                      <div className="flex items-center gap-2">
                        <Layers className="h-5 w-5 text-purple-600" />
                        <h4 className="font-semibold">Unified View</h4>
                      </div>
                      <p className="text-sm text-muted-foreground mt-2">
                        Complete picture combining personal and family finances
                        with intelligent analytics.
                      </p>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-semibold text-purple-600">
                            Live
                          </h4>
                          <p className="text-sm text-muted-foreground">
                            Total Balance
                          </p>
                        </div>
                        <TrendingUp className="h-6 w-6 text-purple-600" />
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-semibold text-green-600">
                            Real-time
                          </h4>
                          <p className="text-sm text-muted-foreground">
                            Net Cash Flow
                          </p>
                        </div>
                        <TrendingUp className="h-6 w-6 text-green-600" />
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-semibold text-blue-600">Smart</h4>
                          <p className="text-sm text-muted-foreground">
                            Goal Progress
                          </p>
                        </div>
                        <TrendingUp className="h-6 w-6 text-blue-600" />
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <CombinedOverview
                  onAddTransaction={handleAddTransaction}
                  onAddAccount={handleAddAccount}
                  onViewBudgets={handleViewBudgets}
                  onViewGoals={handleViewGoals}
                  onSwitchMode={setActiveMode}
                />
              </TabsContent>
            </div>
          </Tabs>
        </CardContent>
      </Card>

      {/* Power User Features */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5 text-purple-500" />
            Power User Features - Now Active
          </CardTitle>
          <CardDescription>
            Advanced tools for comprehensive financial management with real data
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h4 className="font-semibold text-purple-900">
                🔄 Smart Mode Switching
              </h4>
              <p className="text-sm text-muted-foreground">
                Seamlessly switch between personal, family, and unified views
                with real-time data synchronization and intelligent context
                switching.
              </p>

              <h4 className="font-semibold text-blue-900">
                📊 Live Analytics Dashboard
              </h4>
              <p className="text-sm text-muted-foreground">
                Real-time comprehensive reports that span personal and family
                finances with actionable insights and trend analysis.
              </p>
            </div>

            <div className="space-y-4">
              <h4 className="font-semibold text-green-900">
                🎯 Intelligent Insights
              </h4>
              <p className="text-sm text-muted-foreground">
                AI-powered financial insights that analyze your complete
                financial picture and provide personalized recommendations.
              </p>

              <h4 className="font-semibold text-red-900">
                ⚡ Real-time Synchronization
              </h4>
              <p className="text-sm text-muted-foreground">
                Instant updates across all views with real-time notifications
                and cross-account analysis for better decision-making.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
