import { Metadata } from 'next';
import { BetaFinanceTypeProvider } from '@/beta/contexts/BetaFinanceTypeContext';
import { QueryProvider } from '@/shared/components/query-provider';
import { ThemeProvider } from '@/shared/components/theme-provider';
import { Toaster } from '@/shared/components/ui/toaster';

export const metadata: Metadata = {
  title: 'Budget Tracker Beta Program',
  description: 'Exclusive access to the future of financial management',
};

export default function BetaLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
    >
      <QueryProvider>
        <BetaFinanceTypeProvider>
          <div className="min-h-screen bg-background">
            {children}
          </div>
          <Toaster />
        </BetaFinanceTypeProvider>
      </QueryProvider>
    </ThemeProvider>
  );
}