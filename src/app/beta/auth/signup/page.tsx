"use client";

import React, { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/shared/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/shared/components/ui/card";
import { Input } from "@/shared/components/ui/input";
import { Label } from "@/shared/components/ui/label";
import { Badge } from "@/shared/components/ui/badge";
import { Checkbox } from "@/shared/components/ui/checkbox";
import { Sparkles, Mail, Lock, User, ArrowRight } from "lucide-react";
import { createClient } from "@/shared/services/supabase/client";
import { betaService } from "@/beta/services/betaService";
import { useToast } from "@/shared/hooks/use-toast";

export default function BetaSignupPage() {
  const [formData, setFormData] = useState({
    email: "",
    password: "",
    confirmPassword: "",
    fullName: "",
    agreedToTerms: false,
    optInBeta: true,
  });
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { toast } = useToast();
  const supabase = createClient();

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleSignup = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.email || !formData.password || !formData.fullName) {
      toast({
        title: "Missing required fields",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      toast({
        title: "Passwords do not match",
        description: "Please make sure your passwords match.",
        variant: "destructive",
      });
      return;
    }

    if (!formData.agreedToTerms) {
      toast({
        title: "Terms and conditions",
        description: "Please agree to the terms and conditions.",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    try {
      const { data, error } = await supabase.auth.signUp({
        email: formData.email,
        password: formData.password,
        options: {
          data: {
            full_name: formData.fullName,
            beta_user: formData.optInBeta,
            finance_type: "personal", // Default, will be set during onboarding
            beta_joined_at: new Date().toISOString(),
          },
        },
      });

      if (error) {
        toast({
          title: "Signup failed",
          description: error.message,
          variant: "destructive",
        });
        return;
      }

      if (data.user) {
        // Enable beta access for the new user
        if (formData.optInBeta) {
          await betaService.enableBetaAccess(data.user.id);
        }

        toast({
          title: "Welcome to the Beta!",
          description: "Please check your email to verify your account.",
        });
        router.push("/beta/auth/verify-email");
      }
    } catch {
      toast({
        title: "An error occurred",
        description: "Please try again later.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold">BT</span>
            </div>
            <Badge
              variant="secondary"
              className="bg-gradient-to-r from-blue-600 to-purple-600 text-white"
            >
              <Sparkles className="w-3 h-3 mr-1" />
              BETA
            </Badge>
          </div>
          <h1 className="text-2xl font-bold">Join the Beta Program</h1>
          <p className="text-muted-foreground">
            Get exclusive early access to revolutionary financial tools
          </p>
          <div className="mt-2">
            <p className="text-xs text-muted-foreground">
              This is the beta signup. Looking for the main app?{" "}
              <Link
                href="/auth/signup"
                className="text-primary hover:underline"
              >
                Sign up for main app
              </Link>
            </p>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Create Beta Account</CardTitle>
            <CardDescription>
              Sign up to start your exclusive beta experience
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSignup} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="fullName">Full Name</Label>
                <div className="relative">
                  <User className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="fullName"
                    type="text"
                    placeholder="Enter your full name"
                    value={formData.fullName}
                    onChange={(e) =>
                      handleInputChange("fullName", e.target.value)
                    }
                    className="pl-10"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="email"
                    type="email"
                    placeholder="Enter your email"
                    value={formData.email}
                    onChange={(e) => handleInputChange("email", e.target.value)}
                    className="pl-10"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="password"
                    type="password"
                    placeholder="Create a strong password"
                    value={formData.password}
                    onChange={(e) =>
                      handleInputChange("password", e.target.value)
                    }
                    className="pl-10"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="confirmPassword">Confirm Password</Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="confirmPassword"
                    type="password"
                    placeholder="Confirm your password"
                    value={formData.confirmPassword}
                    onChange={(e) =>
                      handleInputChange("confirmPassword", e.target.value)
                    }
                    className="pl-10"
                    required
                  />
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="optInBeta"
                    checked={formData.optInBeta}
                    onCheckedChange={(checked) =>
                      handleInputChange("optInBeta", checked === true)
                    }
                  />
                  <Label htmlFor="optInBeta" className="text-sm">
                    I want to participate in the beta program and receive
                    updates
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="agreedToTerms"
                    checked={formData.agreedToTerms}
                    onCheckedChange={(checked) =>
                      handleInputChange("agreedToTerms", checked === true)
                    }
                    required
                  />
                  <Label htmlFor="agreedToTerms" className="text-sm">
                    I agree to the{" "}
                    <Link
                      href="/terms"
                      className="text-primary hover:underline"
                    >
                      Terms of Service
                    </Link>{" "}
                    and{" "}
                    <Link
                      href="/privacy"
                      className="text-primary hover:underline"
                    >
                      Privacy Policy
                    </Link>
                  </Label>
                </div>
              </div>

              <Button
                type="submit"
                className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                disabled={isLoading}
              >
                {isLoading ? "Creating Account..." : "Join Beta Program"}
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </form>

            <div className="mt-6 text-center space-y-2">
              <p className="text-sm text-muted-foreground">
                Already have a beta account?{" "}
                <Link
                  href="/beta/auth/login"
                  className="text-primary hover:underline"
                >
                  Sign in to beta
                </Link>
              </p>
              <p className="text-xs text-muted-foreground">
                Want to use the main app instead?{" "}
                <Link
                  href="/auth/signup"
                  className="text-primary hover:underline"
                >
                  Main app signup
                </Link>
              </p>
            </div>
          </CardContent>
        </Card>

        <div className="mt-6 text-center space-y-2">
          <Link
            href="/beta"
            className="text-sm text-muted-foreground hover:text-foreground block"
          >
            ← Back to Beta Landing
          </Link>
          <Link
            href="/"
            className="text-xs text-muted-foreground hover:text-foreground block"
          >
            ← Back to Main Site
          </Link>
        </div>
      </div>
    </div>
  );
}
