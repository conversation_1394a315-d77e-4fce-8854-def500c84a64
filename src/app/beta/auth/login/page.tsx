"use client";

import React, { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/shared/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/shared/components/ui/card";
import { Input } from "@/shared/components/ui/input";
import { Label } from "@/shared/components/ui/label";
import { Badge } from "@/shared/components/ui/badge";
import { Sparkles, Mail, Lock, ArrowRight } from "lucide-react";
import { createClient } from "@/shared/services/supabase/client";
import { betaService } from "@/beta/services/betaService";
import { useToast } from "@/shared/hooks/use-toast";

export default function BetaLoginPage() {
  const [formData, setFormData] = useState({
    email: "",
    password: "",
  });
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const { toast } = useToast();
  const supabase = createClient();

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.email || !formData.password) {
      toast({
        title: "Missing credentials",
        description: "Please enter your email and password.",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: formData.email,
        password: formData.password,
      });

      if (error) {
        toast({
          title: "Login failed",
          description: error.message,
          variant: "destructive",
        });
        return;
      }

      if (data.user) {
        // Check if user is a beta user
        let betaUser = await betaService.getBetaUser(data.user.id);

        // If user doesn't have beta access, grant it automatically for existing users
        if (!betaUser?.beta_user) {
          await betaService.enableBetaAccess(data.user.id);
          betaUser = await betaService.getBetaUser(data.user.id);

          toast({
            title: "Beta access granted!",
            description:
              "Welcome to the beta program! Complete onboarding to get started.",
          });
        } else {
          toast({
            title: "Welcome back!",
            description: "Successfully logged into your beta account.",
          });
        }

        if (betaUser?.onboarding_completed) {
          router.push("/beta/dashboard");
        } else {
          router.push("/beta/onboarding");
        }
      }
    } catch {
      toast({
        title: "An error occurred",
        description: "Please try again later.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold">BT</span>
            </div>
            <Badge
              variant="secondary"
              className="bg-gradient-to-r from-blue-600 to-purple-600 text-white"
            >
              <Sparkles className="w-3 h-3 mr-1" />
              BETA
            </Badge>
          </div>
          <h1 className="text-2xl font-bold">Beta Program Login</h1>
          <p className="text-muted-foreground">
            Sign in to access your exclusive beta features
          </p>
          <div className="mt-2">
            <p className="text-xs text-muted-foreground">
              This is the beta version. Looking for the main app?{" "}
              <Link href="/auth/login" className="text-primary hover:underline">
                Sign in to main app
              </Link>
            </p>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Welcome Back to Beta</CardTitle>
            <CardDescription>
              Enter your beta account credentials to continue
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleLogin} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="email"
                    type="email"
                    placeholder="Enter your email"
                    value={formData.email}
                    onChange={(e) => handleInputChange("email", e.target.value)}
                    className="pl-10"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="password"
                    type="password"
                    placeholder="Enter your password"
                    value={formData.password}
                    onChange={(e) =>
                      handleInputChange("password", e.target.value)
                    }
                    className="pl-10"
                    required
                  />
                </div>
              </div>

              <div className="flex items-center justify-between text-sm">
                <Link
                  href="/beta/auth/forgot-password"
                  className="text-primary hover:underline"
                >
                  Forgot password?
                </Link>
              </div>

              <Button
                type="submit"
                className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                disabled={isLoading}
              >
                {isLoading ? "Signing In..." : "Sign In to Beta"}
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </form>

            <div className="mt-6 text-center space-y-2">
              <p className="text-sm text-muted-foreground">
                Don&apos;t have beta access yet?{" "}
                <Link
                  href="/beta/auth/signup"
                  className="text-primary hover:underline"
                >
                  Join the beta program
                </Link>
              </p>
              <p className="text-xs text-muted-foreground">
                Need to access the main app?{" "}
                <Link
                  href="/auth/login"
                  className="text-primary hover:underline"
                >
                  Main app login
                </Link>
              </p>
            </div>
          </CardContent>
        </Card>

        <div className="mt-6 text-center space-y-2">
          <Link
            href="/beta"
            className="text-sm text-muted-foreground hover:text-foreground block"
          >
            ← Back to Beta Landing
          </Link>
          <Link
            href="/"
            className="text-xs text-muted-foreground hover:text-foreground block"
          >
            ← Back to Main Site
          </Link>
        </div>
      </div>
    </div>
  );
}
