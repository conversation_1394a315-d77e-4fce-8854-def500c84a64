"use client";

import React from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  getPendingInvitationsForUser,
  respondToInvitation,
} from "@/features/family-groups/services/family-groups";
import {
  type PendingInvitation,
  type RespondToInvitationResponse,
} from "@/features/family-groups/types/family";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/shared/components/ui/card";
import { Button } from "@/shared/components/ui/button";
import { Loader2, MailWarning, CheckCircle, XCircle, Info } from "lucide-react";
import { format } from "date-fns";
import { toast } from "sonner";

export default function MyInvitationsPage() {
  const queryClient = useQueryClient();

  const {
    data: invitations,
    isLoading,
    error,
    isError,
  } = useQuery<PendingInvitation[], Error>({
    queryKey: ["pendingInvitations"],
    queryFn: getPendingInvitationsForUser,
  });

  const respondMutation = useMutation<
    RespondToInvitationResponse,
    Error,
    { invitationId: string; action: "accept" | "decline" }
  >({
    mutationFn: ({ invitationId, action }) =>
      respondToInvitation(invitationId, action),
    onSuccess: (data, variables) => {
      if (data.success) {
        toast.success(data.message);
      } else {
        toast.error(data.message || "Failed to process invitation.");
      }
      queryClient.invalidateQueries({ queryKey: ["pendingInvitations"] });
      // Potentially invalidate queries for user's group list if they accept
      if (variables.action === "accept" && data.success) {
        queryClient.invalidateQueries({ queryKey: ["userFamilyGroups"] }); // Assuming this key is used for the list of user's groups
      }
    },
    onError: (err) => {
      toast.error(`An error occurred: ${err.message}`);
      queryClient.invalidateQueries({ queryKey: ["pendingInvitations"] });
    },
  });

  const handleResponse = (
    invitationId: string,
    action: "accept" | "decline"
  ) => {
    respondMutation.mutate({ invitationId, action });
  };

  if (isLoading) {
    return (
      <div className="flex flex-col justify-center items-center h-[calc(100vh-10rem)]">
        <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
        <p className="text-lg text-gray-600 dark:text-gray-300">
          Loading your invitations...
        </p>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="container mx-auto p-4 py-8 flex justify-center items-center min-h-[calc(100vh-10rem)]">
        <Card className="w-full max-w-md bg-red-50 dark:bg-red-900/30 border-red-200 dark:border-red-700">
          <CardHeader>
            <CardTitle className="flex items-center text-red-700 dark:text-red-300">
              <MailWarning className="h-6 w-6 mr-2" />
              Error Loading Invitations
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-600 dark:text-red-400">
              There was a problem fetching your invitations. Please try again
              later.
            </p>
            {error?.message && (
              <p className="text-xs text-red-500 dark:text-red-500 mt-2">
                Details: {error.message}
              </p>
            )}
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 sm:p-6 lg:p-8">
      <header className="mb-8">
        <h1 className="text-3xl sm:text-4xl font-bold text-gray-800 dark:text-white tracking-tight">
          Family Finance Invitations
        </h1>
        <p className="text-lg text-gray-600 dark:text-gray-400 mt-1">
          View and respond to pending family finance group invitations.
        </p>
      </header>

      {invitations && invitations.length > 0 ? (
        <div className="space-y-6">
          {invitations.map((invite) => (
            <Card
              key={invite.invitation_id}
              className="shadow-md hover:shadow-lg transition-shadow dark:bg-gray-800"
            >
              <CardHeader>
                <CardTitle className="text-xl text-primary dark:text-primary-foreground">
                  Invitation to join: {invite.group_name}
                </CardTitle>
                <CardDescription className="text-sm text-gray-500 dark:text-gray-400">
                  Invited by: {invite.invited_by_user_email} on{" "}
                  {format(new Date(invite.invited_at), "MMMM d, yyyy")}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 dark:text-gray-300">
                  You have been invited to join the family finance group &quot;
                  <strong>{invite.group_name}</strong>&quot; to manage shared finances together.
                </p>
              </CardContent>
              <CardFooter className="flex justify-end space-x-3">
                <Button
                  variant="outline"
                  className="border-red-500 text-red-500 hover:bg-red-50 hover:text-red-600 dark:hover:bg-red-700/20"
                  onClick={() =>
                    handleResponse(invite.invitation_id, "decline")
                  }
                  disabled={
                    respondMutation.isPending &&
                    respondMutation.variables?.invitationId ===
                      invite.invitation_id
                  }
                >
                  {respondMutation.isPending &&
                  respondMutation.variables?.invitationId ===
                    invite.invitation_id &&
                  respondMutation.variables?.action === "decline" ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <XCircle className="mr-2 h-4 w-4" />
                  )}
                  Decline
                </Button>
                <Button
                  className="bg-green-500 hover:bg-green-600 text-white dark:bg-green-600 dark:hover:bg-green-700"
                  onClick={() => handleResponse(invite.invitation_id, "accept")}
                  disabled={
                    respondMutation.isPending &&
                    respondMutation.variables?.invitationId ===
                      invite.invitation_id
                  }
                >
                  {respondMutation.isPending &&
                  respondMutation.variables?.invitationId ===
                    invite.invitation_id &&
                  respondMutation.variables?.action === "accept" ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <CheckCircle className="mr-2 h-4 w-4" />
                  )}
                  Accept
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center py-12 border-2 border-dashed border-gray-300 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-800/30">
          <Info className="h-12 w-12 text-gray-400 dark:text-gray-500 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-700 dark:text-gray-200">
            No Pending Invitations
          </h3>
          <p className="text-gray-500 dark:text-gray-400 mt-1">
            You currently have no pending invitations to join any family finance groups.
          </p>
        </div>
      )}
    </div>
  );
}
