import { getDashboardData, DashboardClient } from "@/features/dashboard";
import { calculatePercentageChange } from "@/shared/utils";

export const dynamic = "force-dynamic";

interface DashboardStats {
  totalBalance: number;
  monthlyIncome: number;
  monthlyExpenses: number;
  netCashFlow: number;
  savingsRate: number;
  incomeToExpenseRatio: number;
  activeBudgets: number;
  activeGoalsCount: number;
}

interface DashboardChanges {
  totalBalance: number;
  income: number;
  expenses: number;
}

export default async function PersonalDashboardPage() {
  try {
    const data = await getDashboardData();

    const stats: DashboardStats = {
      totalBalance: data.totalBalance,
      monthlyIncome: data.monthlyIncome,
      monthlyExpenses: data.monthlyExpenses,
      netCashFlow: data.netCashFlow,
      savingsRate: data.savingsRate,
      incomeToExpenseRatio: data.incomeToExpenseRatio,
      activeBudgets: data.budgets?.length || 0,
      activeGoalsCount: data.activeGoalsCount || 0,
    };

    // Calculate percentage changes using real data
    const changes: DashboardChanges = {
      totalBalance:
        data.previousTotalBalance !== undefined
          ? calculatePercentageChange(
              data.previousTotalBalance,
              data.totalBalance
            )
          : 0,
      income:
        data.previousMonthlyIncome !== undefined
          ? calculatePercentageChange(
              data.previousMonthlyIncome,
              data.monthlyIncome
            )
          : 0,
      expenses:
        data.previousMonthlyExpenses !== undefined
          ? calculatePercentageChange(
              data.previousMonthlyExpenses,
              data.monthlyExpenses
            )
          : 0,
    };

    return (
      <DashboardClient
        stats={stats}
        changes={changes}
        recentTransactions={data.recentTransactions}
        accounts={data.accounts}
        spendingByCategory={data.spendingByCategory}
        allTransactions={data.allTransactions}
        budgets={data.budgets}
        financialGoalsPreview={data.financialGoalsPreview}
      />
    );
  } catch (error) {
    console.error("Error loading dashboard data:", error);
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-2">Something went wrong</h2>
          <p className="text-muted-foreground">
            We couldn&apos;t load your dashboard data. Please try again later.
          </p>
        </div>
      </div>
    );
  }
}
