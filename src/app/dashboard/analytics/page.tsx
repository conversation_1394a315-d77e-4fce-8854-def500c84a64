"use client";

import { useState, useEffect, useCallback } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/shared/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/shared/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/shared/components/ui/select";
import { Button } from "@/shared/components/ui/button";
import {
  BarChart,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  Download,
  <PERSON><PERSON><PERSON> as PieChartIcon,
  Bar<PERSON>hart as BarChartIcon,
  LineChart as LineChartIcon,
  Calendar,
  Target,
  Activity,
} from "lucide-react";
import { createClient } from "@/shared/services/supabase/client";
import { toast } from "sonner";
import { useUserSettings } from "@/shared/contexts/UserSettingsContext";
import { useAppMode } from "@/shared/contexts/AppModeContext";

interface Transaction {
  id: string;
  amount: number;
  type: "income" | "expense";
  date: string;
  category_id?: string;
  categories?: {
    name: string;
    color?: string;
  };
}

interface MonthlyData {
  month: string;
  income: number;
  expenses: number;
  net: number;
}

interface CategoryData {
  name: string;
  value: number;
  color: string;
}

interface DailyData {
  date: string;
  income: number;
  expenses: number;
  net: number;
  dayOfWeek: string;
}

interface SpendingVelocity {
  dailyAverage: number;
  weeklyAverage: number;
  monthlyAverage: number;
  trend: 'increasing' | 'decreasing' | 'stable';
}

const COLORS = [
  "#0088FE",
  "#00C49F",
  "#FFBB28",
  "#FF8042",
  "#8884D8",
  "#82CA9D",
  "#FFC658",
  "#FF6B6B",
];

export default function AnalyticsPage() {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [monthlyData, setMonthlyData] = useState<MonthlyData[]>([]);
  const [categoryData, setCategoryData] = useState<CategoryData[]>([]);
  const [dailyData, setDailyData] = useState<DailyData[]>([]);
  const [spendingVelocity, setSpendingVelocity] = useState<SpendingVelocity>({
    dailyAverage: 0,
    weeklyAverage: 0,
    monthlyAverage: 0,
    trend: 'stable'
  });
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState("6months");

  const {
    settings: { currency: defaultCurrency },
  } = useUserSettings();
  const { isPersonalMode } = useAppMode();

  const formatMonth = useCallback((monthKey: string) => {
    const [year, month] = monthKey.split("-");
    const date = new Date(parseInt(year), parseInt(month) - 1);
    return date.toLocaleDateString("en-US", {
      month: "short",
      year: "numeric",
    });
  }, []);

  const processAnalyticsData = useCallback(
    (transactions: Transaction[]) => {
      // Process monthly data
      const monthlyMap = new Map<
        string,
        { income: number; expenses: number }
      >();

      transactions.forEach((transaction) => {
        const date = new Date(transaction.date);
        const monthKey = `${date.getFullYear()}-${String(
          date.getMonth() + 1
        ).padStart(2, "0")}`;

        if (!monthlyMap.has(monthKey)) {
          monthlyMap.set(monthKey, { income: 0, expenses: 0 });
        }

        const monthData = monthlyMap.get(monthKey)!;
        if (transaction.type === "income") {
          monthData.income += transaction.amount;
        } else {
          monthData.expenses += transaction.amount;
        }
      });

      const monthlyArray: MonthlyData[] = Array.from(monthlyMap.entries())
        .map(([month, data]) => ({
          month: formatMonth(month),
          income: data.income,
          expenses: data.expenses,
          net: data.income - data.expenses,
        }))
        .sort((a, b) => a.month.localeCompare(b.month));

      setMonthlyData(monthlyArray);

      // Process daily data
      const dailyMap = new Map<string, { income: number; expenses: number }>();
      const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

      transactions.forEach((transaction) => {
        const date = new Date(transaction.date);
        const dayKey = date.toISOString().split('T')[0];

        if (!dailyMap.has(dayKey)) {
          dailyMap.set(dayKey, { income: 0, expenses: 0 });
        }

        const dayData = dailyMap.get(dayKey)!;
        if (transaction.type === "income") {
          dayData.income += transaction.amount;
        } else {
          dayData.expenses += transaction.amount;
        }
      });

      const dailyArray: DailyData[] = Array.from(dailyMap.entries())
        .map(([date, data]) => {
          const dateObj = new Date(date);
          return {
            date: dateObj.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
            income: data.income,
            expenses: data.expenses,
            net: data.income - data.expenses,
            dayOfWeek: dayNames[dateObj.getDay()],
          };
        })
        .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
        .slice(-30); // Last 30 days

      setDailyData(dailyArray);

      // Calculate spending velocity
      const expenseTransactions = transactions.filter(t => t.type === 'expense');
      const totalExpenses = expenseTransactions.reduce((sum, t) => sum + t.amount, 0);
      const daysSinceFirst = expenseTransactions.length > 0 ? 
        Math.max(1, Math.ceil((Date.now() - new Date(expenseTransactions[0].date).getTime()) / (1000 * 60 * 60 * 24))) : 1;
      
      const dailyAverage = totalExpenses / daysSinceFirst;
      const weeklyAverage = dailyAverage * 7;
      const monthlyAverage = dailyAverage * 30;

      // Calculate trend (compare last 2 weeks vs previous 2 weeks)
      const twoWeeksAgo = new Date();
      twoWeeksAgo.setDate(twoWeeksAgo.getDate() - 14);
      const fourWeeksAgo = new Date();
      fourWeeksAgo.setDate(fourWeeksAgo.getDate() - 28);

      const recentExpenses = expenseTransactions
        .filter(t => new Date(t.date) >= twoWeeksAgo)
        .reduce((sum, t) => sum + t.amount, 0);
      
      const previousExpenses = expenseTransactions
        .filter(t => new Date(t.date) >= fourWeeksAgo && new Date(t.date) < twoWeeksAgo)
        .reduce((sum, t) => sum + t.amount, 0);

      let trend: 'increasing' | 'decreasing' | 'stable' = 'stable';
      if (previousExpenses > 0) {
        const changePercent = ((recentExpenses - previousExpenses) / previousExpenses) * 100;
        if (changePercent > 10) trend = 'increasing';
        else if (changePercent < -10) trend = 'decreasing';
      }

      setSpendingVelocity({
        dailyAverage,
        weeklyAverage,
        monthlyAverage,
        trend
      });

      // Process category data for expenses
      const categoryMap = new Map<string, number>();
      const categoryColors = new Map<string, string>();

      transactions
        .filter((t) => t.type === "expense" && t.categories)
        .forEach((transaction) => {
          const categoryName = transaction.categories?.name || "Uncategorized";
          const currentAmount = categoryMap.get(categoryName) || 0;
          categoryMap.set(categoryName, currentAmount + transaction.amount);

          if (transaction.categories?.color) {
            categoryColors.set(categoryName, transaction.categories.color);
          }
        });

      const categoryArray: CategoryData[] = Array.from(categoryMap.entries())
        .map(([name, value], index) => ({
          name,
          value,
          color: categoryColors.get(name) || COLORS[index % COLORS.length],
        }))
        .sort((a, b) => b.value - a.value)
        .slice(0, 8); // Top 8 categories

      setCategoryData(categoryArray);
    },
    [formatMonth]
  );

  const fetchTransactions = useCallback(async () => {
    try {
      const supabase = createClient();
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        toast.error("Please login to view analytics");
        return;
      }

      // Calculate date range
      const endDate = new Date();
      const startDate = new Date();

      switch (timeRange) {
        case "1month":
          startDate.setMonth(startDate.getMonth() - 1);
          break;
        case "3months":
          startDate.setMonth(startDate.getMonth() - 3);
          break;
        case "6months":
          startDate.setMonth(startDate.getMonth() - 6);
          break;
        case "1year":
          startDate.setFullYear(startDate.getFullYear() - 1);
          break;
        case "all":
          startDate.setFullYear(2000); // Far back enough
          break;
      }

      const { data, error } = await supabase
        .from("transactions")
        .select(
          `
          *,
          categories (
            name,
            color
          )
        `
        )
        .eq("user_id", user.id)
        .gte("date", startDate.toISOString())
        .lte("date", endDate.toISOString())
        .order("date", { ascending: true });

      if (error) throw error;

      // Cast the data to our Transaction interface
      const typedTransactions = (data || []).map((item) => ({
        ...item,
        type: item.type as "income" | "expense",
      })) as Transaction[];

      setTransactions(typedTransactions);
      processAnalyticsData(typedTransactions);
    } catch (error) {
      console.error("Error fetching transactions:", error);
      toast.error("Failed to load analytics data");
    } finally {
      setLoading(false);
    }
  }, [timeRange, processAnalyticsData]);

  useEffect(() => {
    fetchTransactions();
  }, [fetchTransactions]);

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: defaultCurrency || "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const calculateTotals = () => {
    const totals = transactions.reduce(
      (acc, transaction) => {
        if (transaction.type === "income") {
          acc.totalIncome += transaction.amount;
        } else {
          acc.totalExpenses += transaction.amount;
        }
        return acc;
      },
      { totalIncome: 0, totalExpenses: 0 }
    );

    return {
      ...totals,
      netIncome: totals.totalIncome - totals.totalExpenses,
      savingsRate:
        totals.totalIncome > 0
          ? ((totals.totalIncome - totals.totalExpenses) / totals.totalIncome) *
            100
          : 0,
    };
  };

  const exportData = () => {
    // Create CSV content
    const headers = ["Date", "Type", "Category", "Amount", "Description"];
    const rows = transactions.map((t) => [
      t.date,
      t.type,
      t.categories?.name || "",
      t.amount,
      "", // Description field if available
    ]);

    const csvContent = [
      headers.join(","),
      ...rows.map((row) => row.join(",")),
    ].join("\n");

    // Download CSV
    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `transactions-${new Date().toISOString().split("T")[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);

    toast.success("Data exported successfully");
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  const totals = calculateTotals();

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {isPersonalMode ? "Personal Analytics" : "Family Analytics"}
          </h1>
          <p className="text-muted-foreground">
            {isPersonalMode 
              ? "Visualize your personal financial data and spending trends"
              : "Analyze family group financial patterns and shared expenses"
            }
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-[140px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1month">Last Month</SelectItem>
              <SelectItem value="3months">Last 3 Months</SelectItem>
              <SelectItem value="6months">Last 6 Months</SelectItem>
              <SelectItem value="1year">Last Year</SelectItem>
              <SelectItem value="all">All Time</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="icon" onClick={exportData}>
            <Download className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Enhanced Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="border-l-4 border-green-500 overflow-hidden relative group hover:shadow-lg transition-shadow duration-300">
          <div className="absolute top-0 right-0 w-16 h-16 bg-green-500/10 rounded-bl-full"></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Income</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-500">
              {formatCurrency(totals.totalIncome)}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              {timeRange === "all"
                ? "All time"
                : `Last ${timeRange
                    .replace("months", " Months")
                    .replace("month", " Month")
                    .replace("year", " Year")}`}
            </p>
          </CardContent>
        </Card>
        <Card className="border-l-4 border-red-500 overflow-hidden relative group hover:shadow-lg transition-shadow duration-300">
          <div className="absolute top-0 right-0 w-16 h-16 bg-red-500/10 rounded-bl-full"></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Expenses
            </CardTitle>
            <TrendingDown className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-500">
              {formatCurrency(totals.totalExpenses)}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              {timeRange === "all"
                ? "All time"
                : `Last ${timeRange
                    .replace("months", " Months")
                    .replace("month", " Month")
                    .replace("year", " Year")}`}
            </p>
          </CardContent>
        </Card>
        <Card
          className={`border-l-4 ${
            totals.netIncome >= 0 ? "border-blue-500" : "border-red-500"
          } overflow-hidden relative group hover:shadow-lg transition-shadow duration-300`}
        >
          <div
            className={`absolute top-0 right-0 w-16 h-16 ${
              totals.netIncome >= 0 ? "bg-blue-500/10" : "bg-red-500/10"
            } rounded-bl-full`}
          ></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Net Income</CardTitle>
            <DollarSign
              className={`h-4 w-4 ${
                totals.netIncome >= 0 ? "text-blue-500" : "text-red-500"
              }`}
            />
          </CardHeader>
          <CardContent>
            <div
              className={`text-2xl font-bold ${
                totals.netIncome >= 0 ? "text-blue-500" : "text-red-500"
              }`}
            >
              {formatCurrency(totals.netIncome)}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Income - Expenses
            </p>
          </CardContent>
        </Card>
        <Card className="border-l-4 border-purple-500 overflow-hidden relative group hover:shadow-lg transition-shadow duration-300">
          <div className="absolute top-0 right-0 w-16 h-16 bg-purple-500/10 rounded-bl-full"></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Savings Rate</CardTitle>
            <PieChartIcon className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-500">
              {totals.savingsRate.toFixed(1)}%
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Of total income
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Spending Velocity & Financial Health Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card className="border-l-4 border-orange-500 overflow-hidden relative group hover:shadow-lg transition-shadow duration-300">
          <div className="absolute top-0 right-0 w-16 h-16 bg-orange-500/10 rounded-bl-full"></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Daily Spending</CardTitle>
            <Activity className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-500">
              {formatCurrency(spendingVelocity.dailyAverage)}
            </div>
            <p className="text-xs text-muted-foreground mt-1 flex items-center gap-1">
              Average per day
              {spendingVelocity.trend === 'increasing' && (
                <TrendingUp className="h-3 w-3 text-red-500" />
              )}
              {spendingVelocity.trend === 'decreasing' && (
                <TrendingDown className="h-3 w-3 text-green-500" />
              )}
            </p>
          </CardContent>
        </Card>
        
        <Card className="border-l-4 border-indigo-500 overflow-hidden relative group hover:shadow-lg transition-shadow duration-300">
          <div className="absolute top-0 right-0 w-16 h-16 bg-indigo-500/10 rounded-bl-full"></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monthly Projection</CardTitle>
            <Calendar className="h-4 w-4 text-indigo-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-indigo-500">
              {formatCurrency(spendingVelocity.monthlyAverage)}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Based on current pace
            </p>
          </CardContent>
        </Card>
        
        <Card className={`border-l-4 ${
          totals.savingsRate >= 20 ? 'border-emerald-500' : 
          totals.savingsRate >= 10 ? 'border-yellow-500' : 'border-red-500'
        } overflow-hidden relative group hover:shadow-lg transition-shadow duration-300`}>
          <div className={`absolute top-0 right-0 w-16 h-16 ${
            totals.savingsRate >= 20 ? 'bg-emerald-500/10' : 
            totals.savingsRate >= 10 ? 'bg-yellow-500/10' : 'bg-red-500/10'
          } rounded-bl-full`}></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Financial Health</CardTitle>
            <Target className={`h-4 w-4 ${
              totals.savingsRate >= 20 ? 'text-emerald-500' : 
              totals.savingsRate >= 10 ? 'text-yellow-500' : 'text-red-500'
            }`} />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${
              totals.savingsRate >= 20 ? 'text-emerald-500' : 
              totals.savingsRate >= 10 ? 'text-yellow-500' : 'text-red-500'
            }`}>
              {totals.savingsRate >= 20 ? 'Excellent' : 
               totals.savingsRate >= 10 ? 'Good' : 'Needs Work'}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              {totals.savingsRate.toFixed(1)}% savings rate
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <Tabs defaultValue="trends" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4 lg:w-[520px]">
          <TabsTrigger value="trends">
            <LineChartIcon className="h-4 w-4 mr-2" />
            Trends
          </TabsTrigger>
          <TabsTrigger value="daily">
            <Activity className="h-4 w-4 mr-2" />
            Daily
          </TabsTrigger>
          <TabsTrigger value="comparison">
            <BarChartIcon className="h-4 w-4 mr-2" />
            Monthly
          </TabsTrigger>
          <TabsTrigger value="categories">
            <PieChartIcon className="h-4 w-4 mr-2" />
            Categories
          </TabsTrigger>
        </TabsList>

        <TabsContent value="trends" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Income & Expense Trends</CardTitle>
              <CardDescription>
                Track your income and expenses over time
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <LineChart data={monthlyData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis tickFormatter={(value) => `$${value}`} />
                  <Tooltip
                    formatter={(value: number) => formatCurrency(value)}
                  />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="income"
                    stroke="#10b981"
                    strokeWidth={2}
                    name="Income"
                  />
                  <Line
                    type="monotone"
                    dataKey="expenses"
                    stroke="#ef4444"
                    strokeWidth={2}
                    name="Expenses"
                  />
                  <Line
                    type="monotone"
                    dataKey="net"
                    stroke="#3b82f6"
                    strokeWidth={2}
                    strokeDasharray="5 5"
                    name="Net"
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="daily" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Daily Spending Patterns</CardTitle>
              <CardDescription>
                Analyze your daily financial activity over the last 30 days
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <LineChart data={dailyData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis tickFormatter={(value) => `$${value}`} />
                  <Tooltip
                    formatter={(value: number) => formatCurrency(value)}
                  />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="expenses"
                    stroke="#ef4444"
                    strokeWidth={2}
                    name="Daily Expenses"
                    dot={{ fill: '#ef4444', strokeWidth: 2, r: 4 }}
                  />
                  <Line
                    type="monotone"
                    dataKey="income"
                    stroke="#10b981"
                    strokeWidth={2}
                    name="Daily Income"
                    dot={{ fill: '#10b981', strokeWidth: 2, r: 4 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="comparison" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Monthly Comparison</CardTitle>
              <CardDescription>
                Compare income and expenses by month
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <BarChart data={monthlyData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis tickFormatter={(value) => `$${value}`} />
                  <Tooltip
                    formatter={(value: number) => formatCurrency(value)}
                  />
                  <Legend />
                  <Bar dataKey="income" fill="#10b981" name="Income" />
                  <Bar dataKey="expenses" fill="#ef4444" name="Expenses" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="categories" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Expense Categories</CardTitle>
              <CardDescription>
                Breakdown of your expenses by category
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-8 md:grid-cols-2">
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={categoryData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) =>
                        `${name} ${(percent * 100).toFixed(0)}%`
                      }
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {categoryData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip
                      formatter={(value: number) => formatCurrency(value)}
                    />
                  </PieChart>
                </ResponsiveContainer>
                <div className="space-y-2">
                  <h4 className="font-semibold mb-4">Category Breakdown</h4>
                  {categoryData.map((category) => (
                    <div
                      key={category.name}
                      className="flex items-center justify-between"
                    >
                      <div className="flex items-center gap-2">
                        <div
                          className="w-3 h-3 rounded-full"
                          style={{ backgroundColor: category.color }}
                        />
                        <span className="text-sm">{category.name}</span>
                      </div>
                      <span className="text-sm font-medium">
                        {formatCurrency(category.value)}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
