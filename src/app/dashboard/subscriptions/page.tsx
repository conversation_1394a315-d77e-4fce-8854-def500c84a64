"use client";

import React, { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { But<PERSON> } from "@/shared/components/ui/button";
import { Input } from "@/shared/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/shared/components/ui/select";
import { Card, CardContent, CardHeader } from "@/shared/components/ui/card";
import { useToast } from "@/shared/hooks/use-toast";
import {
  Plus,
  Search,
  Filter,
  RefreshCw,
  TrendingUp,
  Calendar,
  DollarSign,
  AlertTriangle,
} from "lucide-react";
// import { format } from "date-fns"; // Removed unused import
import {
  SubscriptionCard,
  AddSubscriptionDialog,
} from "@/features/subscriptions/components";
import { SubscriptionService } from "@/features/subscriptions/services/subscriptions";
import type {
  SubscriptionWithDetails,
  CreateSubscriptionData,
  UpdateSubscriptionData,
} from "@/shared/types/subscriptions";
import { useCurrencyFormatter } from "@/shared/hooks/useCurrencyFormatter";

const FILTER_OPTIONS = [
  { value: "all", label: "All Subscriptions" },
  { value: "active", label: "Active Only" },
  { value: "inactive", label: "Inactive Only" },
  { value: "upcoming", label: "Due This Week" },
  { value: "expiring", label: "Expiring Soon" },
];

const SORT_OPTIONS = [
  { value: "next_billing_date", label: "Next Payment" },
  { value: "amount", label: "Amount" },
  { value: "name", label: "Name" },
  { value: "monthly_cost", label: "Monthly Cost" },
];

export default function SubscriptionsPage() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { formatCurrency } = useCurrencyFormatter();
  const [searchTerm, setSearchTerm] = useState("");
  const [filterBy, setFilterBy] = useState("all");
  const [sortBy, setSortBy] = useState("next_billing_date");
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [editingSubscription, setEditingSubscription] =
    useState<SubscriptionWithDetails | null>(null);

  // Fetch subscriptions
  const { data: subscriptions = [], isLoading } = useQuery({
    queryKey: ["subscriptions", "personal"],
    queryFn: () => SubscriptionService.getUserSubscriptions(),
  });

  // Fetch subscription summary
  const { data: summary } = useQuery({
    queryKey: ["subscription-summary", "personal"],
    queryFn: () => SubscriptionService.getSubscriptionSummary(),
  });

  // Create subscription mutation
  const createMutation = useMutation({
    mutationFn: (data: CreateSubscriptionData) =>
      SubscriptionService.createSubscription(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["subscriptions"] });
      queryClient.invalidateQueries({ queryKey: ["subscription-summary"] });
      toast({
        title: "Success",
        description: "Subscription created successfully",
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to create subscription",
        variant: "destructive",
      });
    },
  });

  // Update subscription mutation
  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateSubscriptionData }) =>
      SubscriptionService.updateSubscription(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["subscriptions"] });
      queryClient.invalidateQueries({ queryKey: ["subscription-summary"] });
      setEditingSubscription(null);
      toast({
        title: "Success",
        description: "Subscription updated successfully",
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to update subscription",
        variant: "destructive",
      });
    },
  });

  // Delete subscription mutation
  const deleteMutation = useMutation({
    mutationFn: (id: string) => SubscriptionService.deleteSubscription(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["subscriptions"] });
      queryClient.invalidateQueries({ queryKey: ["subscription-summary"] });
      toast({
        title: "Success",
        description: "Subscription deleted successfully",
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to delete subscription",
        variant: "destructive",
      });
    },
  });

  // Toggle subscription status mutation
  const toggleStatusMutation = useMutation({
    mutationFn: ({ id, isActive }: { id: string; isActive: boolean }) =>
      SubscriptionService.toggleSubscriptionStatus(id, isActive),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["subscriptions"] });
      queryClient.invalidateQueries({ queryKey: ["subscription-summary"] });
    },
  });

  // Filter and sort subscriptions
  const filteredAndSortedSubscriptions = React.useMemo(() => {
    const filtered = subscriptions.filter((subscription) => {
      // Search filter
      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase();
        const matchesSearch =
          subscription.name.toLowerCase().includes(searchLower) ||
          subscription.provider?.toLowerCase().includes(searchLower) ||
          subscription.category_name?.toLowerCase().includes(searchLower);
        if (!matchesSearch) return false;
      }

      // Status filter
      switch (filterBy) {
        case "active":
          return subscription.is_active;
        case "inactive":
          return !subscription.is_active;
        case "upcoming":
          return subscription.is_active && subscription.days_until_payment <= 7;
        case "expiring":
          return (
            subscription.end_date &&
            new Date(subscription.end_date) <=
              new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
          );
        default:
          return true;
      }
    });

    // Sort
    filtered.sort((a, b) => {
      switch (sortBy) {
        case "amount":
          return b.amount - a.amount;
        case "name":
          return a.name.localeCompare(b.name);
        case "monthly_cost":
          return b.monthly_cost - a.monthly_cost;
        case "next_billing_date":
        default:
          return (
            new Date(a.next_billing_date).getTime() -
            new Date(b.next_billing_date).getTime()
          );
      }
    });

    return filtered;
  }, [subscriptions, searchTerm, filterBy, sortBy]);


  const handleCreateSubscription = async (data: CreateSubscriptionData) => {
    await createMutation.mutateAsync(data);
  };

  const handleUpdateSubscription = async (data: CreateSubscriptionData) => {
    if (!editingSubscription) return;
    await updateMutation.mutateAsync({
      id: editingSubscription.id,
      data,
    });
  };

  const handleDeleteSubscription = (subscription: SubscriptionWithDetails) => {
    if (confirm(`Are you sure you want to delete "${subscription.name}"?`)) {
      deleteMutation.mutate(subscription.id);
    }
  };

  const handleToggleStatus = (subscription: SubscriptionWithDetails) => {
    toggleStatusMutation.mutate({
      id: subscription.id,
      isActive: !subscription.is_active,
    });
  };

  // Note: Error handling removed to simplify - consider adding back if needed

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Subscriptions</h1>
          <p className="text-muted-foreground">
            Manage your recurring payments and bills
          </p>
        </div>
        <Button onClick={() => setShowAddDialog(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Add Subscription
        </Button>
      </div>

      {/* Summary Cards */}
      {summary && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <DollarSign className="h-4 w-4 text-green-600" />
                <div>
                  <p className="text-2xl font-bold text-green-600">
                    {formatCurrency(summary.total_monthly_cost)}
                  </p>
                  <p className="text-xs text-muted-foreground">Monthly Total</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <RefreshCw className="h-4 w-4 text-blue-600" />
                <div>
                  <p className="text-2xl font-bold text-blue-600">
                    {summary.active_subscriptions}
                  </p>
                  <p className="text-xs text-muted-foreground">Active</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Calendar className="h-4 w-4 text-orange-600" />
                <div>
                  <p className="text-2xl font-bold text-orange-600">
                    {summary.upcoming_payments_7_days}
                  </p>
                  <p className="text-xs text-muted-foreground">Due This Week</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <AlertTriangle className="h-4 w-4 text-red-600" />
                <div>
                  <p className="text-2xl font-bold text-red-600">
                    {summary.expiring_soon}
                  </p>
                  <p className="text-xs text-muted-foreground">Expiring Soon</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search subscriptions..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={filterBy} onValueChange={setFilterBy}>
          <SelectTrigger className="w-[200px]">
            <Filter className="h-4 w-4 mr-2" />
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {FILTER_OPTIONS.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Select value={sortBy} onValueChange={setSortBy}>
          <SelectTrigger className="w-[200px]">
            <TrendingUp className="h-4 w-4 mr-2" />
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {SORT_OPTIONS.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                Sort by {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Subscriptions Grid */}
      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-muted rounded w-3/4"></div>
                <div className="h-3 bg-muted rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="h-3 bg-muted rounded"></div>
                  <div className="h-3 bg-muted rounded w-2/3"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : filteredAndSortedSubscriptions.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <RefreshCw className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">
              {searchTerm || filterBy !== "all"
                ? "No subscriptions found"
                : "No subscriptions yet"}
            </h3>
            <p className="text-muted-foreground mb-4">
              {searchTerm || filterBy !== "all"
                ? "Try adjusting your search or filters"
                : "Start tracking your recurring payments and bills"}
            </p>
            {!searchTerm && filterBy === "all" && (
              <Button onClick={() => setShowAddDialog(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Add Your First Subscription
              </Button>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredAndSortedSubscriptions.map((subscription) => (
            <SubscriptionCard
              key={subscription.id}
              subscription={subscription}
              onEdit={setEditingSubscription}
              onDelete={handleDeleteSubscription}
              onToggleStatus={handleToggleStatus}
            />
          ))}
        </div>
      )}

      {/* Add/Edit Dialog */}
      <AddSubscriptionDialog
        open={showAddDialog || !!editingSubscription}
        onOpenChange={(open) => {
          if (!open) {
            setShowAddDialog(false);
            setEditingSubscription(null);
          }
        }}
        onSubmit={
          editingSubscription
            ? handleUpdateSubscription
            : handleCreateSubscription
        }
        subscription={editingSubscription || undefined}
        isLoading={createMutation.isPending || updateMutation.isPending}
      />
    </div>
  );
}
