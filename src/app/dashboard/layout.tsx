"use client";

import { Sidebar, Head<PERSON> } from "@/shared/components";
import { AppModeProvider } from "@/shared/contexts/AppModeContext";
import { usePathname } from "next/navigation";

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();

  // Show sidebar only for specific routes (not on welcome dashboard)
  const showSidebar =
    pathname !== "/dashboard" &&
    (pathname.startsWith("/dashboard/personal") ||
      pathname.startsWith("/dashboard/family") ||
      pathname.startsWith("/dashboard/transactions") ||
      pathname.startsWith("/dashboard/subscriptions") ||
      pathname.startsWith("/dashboard/budgets") ||
      pathname.startsWith("/dashboard/categories") ||
      pathname.startsWith("/dashboard/goals") ||
      pathname.startsWith("/dashboard/analytics") ||
      pathname.startsWith("/dashboard/accounts") ||
      pathname.startsWith("/dashboard/invitations") ||
      pathname.startsWith("/dashboard/settings"));

  // Show header only for specific routes (not on welcome dashboard)
  const showHeader = pathname !== "/dashboard";

  // For welcome dashboard, render without layout wrapper
  if (pathname === "/dashboard") {
    return <AppModeProvider>{children}</AppModeProvider>;
  }

  return (
    <AppModeProvider>
      <div className="relative flex h-screen w-full overflow-hidden">
        {showSidebar && <Sidebar />}
        <div className="flex flex-1 flex-col overflow-hidden">
          {showHeader && <Header />}
          <main className="flex-1 overflow-y-auto bg-background transition-[padding] duration-300 ease-in-out">
            <div className="mx-auto h-full w-full max-w-[2000px] p-4 md:p-6 lg:px-8">
              {children}
            </div>
          </main>
        </div>
      </div>
    </AppModeProvider>
  );
}
