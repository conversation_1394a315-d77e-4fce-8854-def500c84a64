"use client";

import { useState, useEffect, useRef } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/shared/components/ui/card";
import { Button } from "@/shared/components/ui/button";

import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/shared/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/shared/components/ui/select";
import { Input } from "@/shared/components/ui/input";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/shared/components/ui/avatar";
import { useForm, SubmitHandler } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  User,
  Bell,
  Globe,
  Save,
  LogOut,
  Shield,
  Trash2,
  HelpCircle,
  AlertTriangle,
  Camera,
  Upload,
} from "lucide-react";
import { createClient } from "@/shared/services/supabase/client";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { useUserSettings } from "@/shared/contexts/UserSettingsContext";
import { AvatarService } from "@/shared/services/supabase/avatar-service";

// Define profile form schema
const profileFormSchema = z.object({
  full_name: z.string().min(1, "Full name is required"),
  email: z.string().email("Invalid email address"),
  currency: z.string().min(1, "Currency is required"),
  notifications_enabled: z.boolean(),
  dark_mode: z.boolean(),
});

// Infer type from schema
interface ProfileFormValues {
  full_name: string;
  email: string;
  currency: string;
  notifications_enabled: boolean;
  dark_mode: boolean;
}

// User profile interface
interface UserProfile {
  id: string;
  email: string;
  full_name: string;
  avatar_url?: string;
  currency: string;
  created_at: string;
  updated_at: string;
  dark_mode?: boolean;
  notifications_enabled?: boolean;
}

export default function SettingsPage() {
  const [loading, setLoading] = useState(true);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [avatarUploading, setAvatarUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const router = useRouter();

  const avatarService = new AvatarService();

  const {
    settings: contextSettings,
    updateSettings: updateContextSettings,
    refreshSettings,
  } = useUserSettings();

  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      full_name: "",
      email: "",
      currency: contextSettings.currency || "USD",
      notifications_enabled: true,
      dark_mode: false,
    },
  });

  useEffect(() => {
    const fetchNonContextProfileData = async () => {
      try {
        setLoading(true);
        const supabase = createClient();
        const {
          data: { user },
        } = await supabase.auth.getUser();

        if (!user) {
          toast.error("Please login to view settings");
          router.push("/auth/login");
          return;
        }

        const { data, error } = await supabase
          .from("user_profiles")
          .select(
            "id, email, full_name, avatar_url, created_at, updated_at, dark_mode, notifications_enabled"
          )
          .eq("id", user.id)
          .single();

        if (error && error.code !== "PGRST116") throw error;

        if (data) {
          const dbProfile = data as Omit<UserProfile, "currency"> & {
            currency?: string;
          };

          setUserProfile(dbProfile as UserProfile);

          form.reset({
            full_name: dbProfile.full_name || "",
            email: dbProfile.email || user.email || "",
            currency: contextSettings.currency,
            notifications_enabled:
              dbProfile.notifications_enabled === undefined
                ? true
                : dbProfile.notifications_enabled,
            dark_mode:
              dbProfile.dark_mode === undefined
                ? localStorage.getItem("theme") === "dark"
                : dbProfile.dark_mode,
          });

          const currentThemeIsDark =
            dbProfile.dark_mode === undefined
              ? localStorage.getItem("theme") === "dark"
              : dbProfile.dark_mode;
          setIsDarkMode(currentThemeIsDark);
        } else {
          form.reset({
            ...form.getValues(),
            currency: contextSettings.currency || "USD",
          });
        }
      } catch (error: unknown) {
        let errorMessage = "Failed to load user profile data.";
        if (error && typeof error === "object" && "message" in error) {
          const typedError = error as {
            message?: string;
            details?: string;
            hint?: string;
            code?: string;
          };
          console.error("Error fetching user profile data:", {
            message: typedError.message,
            details: typedError.details,
            hint: typedError.hint,
            code: typedError.code,
            fullError: error,
          });
          if (typedError.message) {
            errorMessage = `Failed to load user profile data: ${typedError.message}`;
          }
        } else {
          console.error("Error fetching user profile data:", error);
        }
        toast.error(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    fetchNonContextProfileData();

    if (
      contextSettings.currency &&
      contextSettings.currency !== form.getValues("currency")
    ) {
      form.setValue("currency", contextSettings.currency);
    }
  }, [contextSettings.currency, form, router]);

  const onSubmit: SubmitHandler<ProfileFormValues> = async (values) => {
    setLoading(true);
    try {
      if (values.currency !== contextSettings.currency) {
        await updateContextSettings({ currency: values.currency });
      }

      const supabase = createClient();
      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) {
        toast.error("Please login to update settings");
        setLoading(false);
        return;
      }

      const profileUpdates: Partial<
        Omit<UserProfile, "id" | "email" | "created_at" | "currency">
      > = {};
      if (values.full_name !== userProfile?.full_name)
        profileUpdates.full_name = values.full_name;
      if (values.dark_mode !== form.getValues("dark_mode"))
        profileUpdates.dark_mode = values.dark_mode;
      if (
        values.notifications_enabled !== form.getValues("notifications_enabled")
      )
        profileUpdates.notifications_enabled = values.notifications_enabled;

      let nonContextSettingsUpdated = false;
      if (Object.keys(profileUpdates).length > 0) {
        profileUpdates.updated_at = new Date().toISOString();
        const { error: otherError } = await supabase
          .from("user_profiles")
          .update(profileUpdates)
          .eq("id", user.id);

        if (otherError) throw otherError;
        nonContextSettingsUpdated = true;
      }

      if (values.dark_mode !== isDarkMode) {
        setIsDarkMode(values.dark_mode);
      }

      if (
        values.currency !== contextSettings.currency &&
        !nonContextSettingsUpdated
      ) {
        toast.success("Currency updated successfully!");
      } else if (nonContextSettingsUpdated) {
        toast.success("Profile updated successfully!");
      } else {
        toast.info("No changes detected.");
      }

      await refreshSettings();
    } catch (error) {
      console.error("Error updating settings:", error);
      toast.error("Failed to update settings");
    } finally {
      setLoading(false);
    }
  };

  const handleSignOut = async () => {
    try {
      const supabase = createClient();
      await supabase.auth.signOut();
      toast.success("Signed out successfully");
      router.push("/");
    } catch (error) {
      console.error("Error signing out:", error);
      toast.error("Failed to sign out");
    }
  };

  const handleDeleteAccount = async () => {
    if (
      !confirm(
        "Are you sure you want to delete your account? This action cannot be undone."
      )
    )
      return;
    if (
      !confirm(
        "All your data will be permanently deleted. Confirm again to proceed."
      )
    )
      return;

    try {
      toast.error("Account deletion is disabled in this demo");
    } catch (error) {
      console.error("Error deleting account:", error);
      toast.error("Failed to delete account");
    }
  };

  const handleAvatarUpload = async (file: File) => {
    setAvatarUploading(true);
    try {
      const supabase = createClient();
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        toast.error("Please login to upload avatar");
        return;
      }

      const result = await avatarService.uploadAvatar(file, user.id);

      if (result.success && result.url) {
        // Update user profile with new avatar URL
        const { error } = await supabase
          .from("user_profiles")
          .update({
            avatar_url: result.url,
            updated_at: new Date().toISOString(),
          })
          .eq("id", user.id);

        if (error) throw error;

        // Update local state
        setUserProfile((prev) =>
          prev ? { ...prev, avatar_url: result.url } : null
        );

        // Trigger header refresh
        window.dispatchEvent(new CustomEvent("avatar-updated"));

        toast.success("Avatar updated successfully!");
      } else {
        toast.error(result.error || "Failed to upload avatar");
      }
    } catch (error) {
      console.error("Error uploading avatar:", error);
      toast.error("Failed to upload avatar");
    } finally {
      setAvatarUploading(false);
    }
  };

  const handleAvatarClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleAvatarUpload(file);
    }
  };

  const getInitials = (fullName?: string, email?: string) => {
    if (fullName) {
      return fullName
        .split(" ")
        .map((name) => name[0])
        .join("")
        .toUpperCase()
        .slice(0, 2);
    }
    if (email) {
      return email[0].toUpperCase();
    }
    return "U";
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Settings</h1>
        <p className="text-muted-foreground">
          Manage your account settings and preferences
        </p>
      </div>

      <div className="grid gap-8 md:grid-cols-[250px_1fr]">
        <Card>
          <CardContent className="p-6">
            <nav className="flex flex-col space-y-1">
              <Button variant="ghost" className="justify-start">
                <User className="mr-2 h-4 w-4" />
                Profile
              </Button>
              <Button variant="ghost" className="justify-start">
                <Globe className="mr-2 h-4 w-4" />
                Preferences
              </Button>
              <Button variant="ghost" className="justify-start">
                <Bell className="mr-2 h-4 w-4" />
                Notifications
              </Button>
              <Button variant="ghost" className="justify-start">
                <Shield className="mr-2 h-4 w-4" />
                Security
              </Button>
              <Button variant="ghost" className="justify-start">
                <HelpCircle className="mr-2 h-4 w-4" />
                Help & Support
              </Button>
            </nav>
          </CardContent>
        </Card>

        <Form {...form}>
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Profile</CardTitle>
                <CardDescription>
                  Manage your personal information
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form
                  onSubmit={form.handleSubmit(onSubmit)}
                  className="space-y-4"
                >
                  <div className="space-y-4">
                    <div>
                      <FormLabel>Avatar</FormLabel>
                      <div className="flex items-center space-x-4 mt-2">
                        <div className="relative">
                          <Avatar className="h-20 w-20">
                            <AvatarImage
                              src={userProfile?.avatar_url}
                              alt="Profile picture"
                            />
                            <AvatarFallback className="text-lg">
                              {getInitials(
                                userProfile?.full_name,
                                userProfile?.email
                              )}
                            </AvatarFallback>
                          </Avatar>
                          <Button
                            type="button"
                            size="sm"
                            variant="outline"
                            className="absolute -bottom-2 -right-2 h-8 w-8 rounded-full p-0"
                            onClick={handleAvatarClick}
                            disabled={avatarUploading}
                          >
                            {avatarUploading ? (
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                            ) : (
                              <Camera className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                        <div className="space-y-1">
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={handleAvatarClick}
                            disabled={avatarUploading}
                          >
                            <Upload className="mr-2 h-4 w-4" />
                            {avatarUploading ? "Uploading..." : "Change Avatar"}
                          </Button>
                          <p className="text-xs text-muted-foreground">
                            JPG, PNG up to 5MB
                          </p>
                        </div>
                      </div>
                      <input
                        ref={fileInputRef}
                        type="file"
                        accept="image/*"
                        onChange={handleFileChange}
                        className="hidden"
                        disabled={avatarUploading}
                      />
                    </div>
                  </div>

                  <FormField
                    control={form.control}
                    name="full_name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Full Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Your name" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email</FormLabel>
                        <FormControl>
                          <Input placeholder="Your email" {...field} disabled />
                        </FormControl>
                        <FormDescription>
                          Contact support to change your email address
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="currency"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Default Currency</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select currency" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="USD">USD ($)</SelectItem>
                            <SelectItem value="EUR">EUR (€)</SelectItem>
                            <SelectItem value="GBP">GBP (£)</SelectItem>
                            <SelectItem value="JPY">JPY (¥)</SelectItem>
                            <SelectItem value="CAD">CAD ($)</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Used for displaying amounts throughout the app
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <Button type="submit" className="mt-4">
                    <Save className="mr-2 h-4 w-4" />
                    Save Changes
                  </Button>
                </form>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Preferences</CardTitle>
                <CardDescription>Customize your app experience</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <h3 className="font-medium">Dark Mode</h3>
                    <p className="text-sm text-muted-foreground">
                      Switch between light and dark themes
                    </p>
                  </div>
                  <FormField
                    control={form.control}
                    name="dark_mode"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <div className="flex items-center">
                            <input
                              type="checkbox"
                              id="dark-mode-toggle"
                              checked={field.value}
                              onChange={(e) => {
                                const checked = e.target.checked;
                                field.onChange(checked);
                                setIsDarkMode(checked);
                              }}
                              className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                            />
                            <label
                              htmlFor="dark-mode-toggle"
                              className="ml-2 sr-only"
                            >
                              Dark Mode
                            </label>
                          </div>
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <h3 className="font-medium">Notifications</h3>
                    <p className="text-sm text-muted-foreground">
                      Receive alerts about your account activity
                    </p>
                  </div>
                  <FormField
                    control={form.control}
                    name="notifications_enabled"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <div className="flex items-center">
                            <input
                              type="checkbox"
                              id="notifications-toggle"
                              checked={field.value}
                              onChange={(e) => field.onChange(e.target.checked)}
                              className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                            />
                            <label
                              htmlFor="notifications-toggle"
                              className="ml-2 sr-only"
                            >
                              Enable Notifications
                            </label>
                          </div>
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
                <Button
                  variant="outline"
                  className="w-full mt-4"
                  onClick={() => form.handleSubmit(onSubmit)()}
                >
                  <Save className="mr-2 h-4 w-4" />
                  Save Preferences
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Account Actions</CardTitle>
                <CardDescription>Manage your account</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex flex-col space-y-2">
                  <Button variant="outline" onClick={handleSignOut}>
                    <LogOut className="mr-2 h-4 w-4" />
                    Sign Out
                  </Button>
                  <Button variant="destructive" onClick={handleDeleteAccount}>
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete Account
                  </Button>
                </div>
                <div className="mt-4 rounded-md bg-yellow-50 dark:bg-yellow-900/20 p-4 text-sm text-yellow-800 dark:text-yellow-300">
                  <div className="flex items-start">
                    <AlertTriangle className="h-5 w-5 mr-2 flex-shrink-0" />
                    <div>
                      <h3 className="font-medium">Warning</h3>
                      <p className="mt-1">
                        Deleting your account will permanently remove all your
                        data, including transactions, budgets, and goals. This
                        action cannot be undone.
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </Form>
      </div>
    </div>
  );
}
