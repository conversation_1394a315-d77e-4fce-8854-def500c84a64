"use client";

import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/shared/components/ui/card";
import { <PERSON><PERSON> } from "@/shared/components/ui/button";
import {
  Users,
  Plus,
  ArrowRight,
  Heart,
  Shield,
  Target,
  TrendingUp,
  Loader2,
  AlertCircle,
  Settings,
  DollarSign,
} from "lucide-react";
import Link from "next/link";
import { useQuery } from "@tanstack/react-query";
import { getUserFamilyGroups } from "@/features/family-groups/services/family-groups";
import { type UserFamilyGroupRpcResponseItem } from "@/features/family-groups/types/family";
import { Badge } from "@/shared/components/ui/badge";

export default function FamilyDashboardPage() {
  const {
    data: groups,
    isLoading,
    error,
  } = useQuery<UserFamilyGroupRpcResponseItem[], Error>({
    queryKey: ["familyGroups"],
    queryFn: getUserFamilyGroups,
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2 text-lg">Loading family groups...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-8">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-3">
            <Users className="h-8 w-8 text-primary" />
            Family Finance
          </h1>
          <p className="text-muted-foreground">
            Manage your family&apos;s shared finances, create groups, and
            collaborate on budgets.
          </p>
        </div>

        <Card className="bg-destructive/10 border-destructive">
          <CardHeader>
            <CardTitle className="flex items-center text-destructive">
              <AlertCircle className="mr-2 h-5 w-5" /> Error
            </CardTitle>
          </CardHeader>
          <CardContent className="text-destructive">
            <p>Could not load family groups: {error.message}</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const hasGroups = groups && groups.length > 0;

  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl font-bold tracking-tight flex items-center gap-3">
          <Users className="h-8 w-8 text-primary" />
          Family Finance
        </h1>
        <p className="text-muted-foreground">
          Manage your family&apos;s shared finances, create groups, and
          collaborate on budgets.
        </p>
      </div>

      {/* Quick Actions */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card className="group hover:shadow-lg transition-all duration-300 border-2 hover:border-primary/50 flex flex-col">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Plus className="h-5 w-5 text-green-500" />
              Create Family Group
            </CardTitle>
          </CardHeader>
          <CardContent className="flex-1 flex flex-col">
            <p className="text-sm text-muted-foreground mb-4 flex-1">
              Start a new family group to manage shared expenses and budgets
              together.
            </p>
            <Button className="w-full group-hover:bg-primary transition-colors mt-auto">
              Get Started
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </CardContent>
        </Card>

        <Card className="group hover:shadow-lg transition-all duration-300 border-2 hover:border-primary/50 flex flex-col">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Heart className="h-5 w-5 text-red-500" />
              Join Family Group
            </CardTitle>
          </CardHeader>
          <CardContent className="flex-1 flex flex-col">
            <p className="text-sm text-muted-foreground mb-4 flex-1">
              Join an existing family group using an invitation link or code.
            </p>
            <Link href="/dashboard/invitations" className="block mt-auto">
              <Button
                variant="outline"
                className="w-full group-hover:bg-muted transition-colors"
              >
                View Invitations
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </CardContent>
        </Card>

        <Card className="group hover:shadow-lg transition-all duration-300 border-2 hover:border-primary/50 flex flex-col">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center gap-2">
              <Shield className="h-5 w-5 text-blue-500" />
              Security & Privacy
            </CardTitle>
          </CardHeader>
          <CardContent className="flex-1 flex flex-col">
            <p className="text-sm text-muted-foreground mb-4 flex-1">
              Learn about family group security and privacy settings.
            </p>
            <Button
              variant="outline"
              className="w-full group-hover:bg-muted transition-colors mt-auto"
            >
              Learn More
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Family Groups Overview */}
      <div className="space-y-6">
        {/* Your Family Groups Section */}
        <Card>
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Your Family Groups
              </div>
              {hasGroups && (
                <Badge variant="secondary" className="text-xs">
                  {groups.length} {groups.length === 1 ? "Group" : "Groups"}
                </Badge>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            {!hasGroups ? (
              <div className="text-center py-12">
                <div className="mx-auto w-16 h-16 bg-muted rounded-full flex items-center justify-center mb-4">
                  <Users className="h-8 w-8 text-muted-foreground" />
                </div>
                <h3 className="text-lg font-semibold mb-2">
                  No Family Groups Yet
                </h3>
                <p className="text-muted-foreground mb-6 max-w-sm mx-auto">
                  Create or join a family group to start collaborating on
                  finances.
                </p>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Create Your First Group
                </Button>
              </div>
            ) : (
              <div className="space-y-3">
                {groups.map((group) => (
                  <div
                    key={group.group_id}
                    className="group border rounded-lg p-4 hover:bg-muted/30 transition-colors"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4 flex-1 min-w-0">
                        <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center flex-shrink-0">
                          <Users className="h-6 w-6 text-white" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="font-semibold text-lg truncate">
                              {group.group_name}
                            </h4>
                            <Badge
                              variant={
                                group.user_role === "admin"
                                  ? "default"
                                  : "secondary"
                              }
                              className="text-xs flex-shrink-0"
                            >
                              {group.user_role === "admin" ? (
                                <>
                                  <Shield className="h-3 w-3 mr-1" />
                                  Admin
                                </>
                              ) : (
                                <>
                                  <Users className="h-3 w-3 mr-1" />
                                  Member
                                </>
                              )}
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            Created{" "}
                            {new Date(group.created_at).toLocaleDateString(
                              "en-US",
                              {
                                year: "numeric",
                                month: "short",
                                day: "numeric",
                              }
                            )}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2 flex-shrink-0 ml-4">
                        <Link href={`/dashboard/family/${group.group_id}`}>
                          <Button size="sm" className="group/btn">
                            <DollarSign className="h-4 w-4 mr-2" />
                            Manage
                            <ArrowRight className="h-4 w-4 ml-2 group-hover/btn:translate-x-1 transition-transform" />
                          </Button>
                        </Link>
                        {group.user_role === "admin" && (
                          <Link
                            href={`/dashboard/family/${group.group_id}/settings`}
                          >
                            <Button
                              variant="outline"
                              size="sm"
                              className="px-3"
                            >
                              <Settings className="h-4 w-4" />
                            </Button>
                          </Link>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Family Features Section */}
        <Card>
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Family Features
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="grid gap-3 sm:grid-cols-2">
              <div className="flex items-start gap-3 p-4 rounded-lg bg-green-500/5 border border-green-500/20 hover:bg-green-500/10 transition-colors">
                <div className="p-2 rounded-full bg-green-500/15 flex-shrink-0">
                  <Heart className="h-4 w-4 text-green-600" />
                </div>
                <div className="min-w-0">
                  <h4 className="font-semibold text-green-900 dark:text-green-100 mb-1">
                    Shared Budgets
                  </h4>
                  <p className="text-sm text-green-700 dark:text-green-300 leading-relaxed">
                    Create and manage budgets that everyone can contribute to.
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3 p-4 rounded-lg bg-blue-500/5 border border-blue-500/20 hover:bg-blue-500/10 transition-colors">
                <div className="p-2 rounded-full bg-blue-500/15 flex-shrink-0">
                  <Shield className="h-4 w-4 text-blue-600" />
                </div>
                <div className="min-w-0">
                  <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-1">
                    Safe Collaboration
                  </h4>
                  <p className="text-sm text-blue-700 dark:text-blue-300 leading-relaxed">
                    Secure permissions and privacy controls for family members.
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3 p-4 rounded-lg bg-purple-500/5 border border-purple-500/20 hover:bg-purple-500/10 transition-colors">
                <div className="p-2 rounded-full bg-purple-500/15 flex-shrink-0">
                  <Target className="h-4 w-4 text-purple-600" />
                </div>
                <div className="min-w-0">
                  <h4 className="font-semibold text-purple-900 dark:text-purple-100 mb-1">
                    Family Goals
                  </h4>
                  <p className="text-sm text-purple-700 dark:text-purple-300 leading-relaxed">
                    Set and track financial goals together as a family.
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3 p-4 rounded-lg bg-orange-500/5 border border-orange-500/20 hover:bg-orange-500/10 transition-colors">
                <div className="p-2 rounded-full bg-orange-500/15 flex-shrink-0">
                  <TrendingUp className="h-4 w-4 text-orange-600" />
                </div>
                <div className="min-w-0">
                  <h4 className="font-semibold text-orange-900 dark:text-orange-100 mb-1">
                    Joint Planning
                  </h4>
                  <p className="text-sm text-orange-700 dark:text-orange-300 leading-relaxed">
                    Plan financial decisions together with shared insights.
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
