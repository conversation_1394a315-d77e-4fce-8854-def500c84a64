"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/shared/components/ui/card";
import { But<PERSON> } from "@/shared/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/shared/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/shared/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/shared/components/ui/select";
import { Badge } from "@/shared/components/ui/badge";
import { Input } from "@/shared/components/ui/input";
import { useForm, SubmitHandler } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Plus,
  Wallet,
  CreditCard,
  PiggyBank,
  Edit,
  Trash2,
  TrendingUp,
  AlertCircle,
  Banknote,
  Building2,
  Home,
} from "lucide-react";
import { createClient } from "@/shared/services/supabase/client";
import {
  getFamilyGroupAccounts,
  type FamilyGroupAccount,
} from "@/features/family-groups/services/family-finances";
import { toast } from "sonner";
import { useUserSettings } from "@/shared/contexts/UserSettingsContext";
import { useCurrencyFormatter } from "@/shared/hooks/useCurrencyFormatter";
import Link from "next/link";

// Define form schema
const accountFormSchema = z
  .object({
    bank_name_select: z.string().min(1, "Bank name is required"),
    bank_name_custom: z.string().optional(),
    type: z.enum([
      "checking",
      "savings",
      "credit_card",
      "cash",
      "investment",
      "other",
    ]),
    balance: z.string().refine((val) => !isNaN(parseFloat(val)), {
      message: "Balance must be a valid number",
    }),
    currency: z.string().min(1, "Currency is required"),
  })
  .superRefine((data, ctx) => {
    if (
      data.bank_name_select === "Other" &&
      (!data.bank_name_custom || data.bank_name_custom.trim() === "")
    ) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ["bank_name_custom"],
        message: 'Custom bank name is required when "Other" is selected',
      });
    }
  });

// Infer type from schema
type AccountFormValues = z.infer<typeof accountFormSchema>;

const presetBanks = [
  "BNP Paribas Fortis",
  "KBC Bank",
  "Belfius Bank",
  "ING Belgium",
  "Argenta",
  "AXA Bank Belgium",
  "Beobank",
  "Crelan",
  "Hello Bank!",
  "Keytrade Bank",
  "Triodos Bank",
  "vdk bank",
  "Bank Van Breda",
  "ING Group",
  "Rabobank",
  "ABN AMRO",
  "De Volksbank",
  "Van Lanschot Kempen",
  "SNS Bank",
  "RegioBank",
  "ASN Bank",
  "Bunq",
  "Knab",
  "Monzo",
  "Revolut",
  "N26",
  "Starling Bank",
  "Chase",
  "Bank of America",
  "Wells Fargo",
  "Citibank",
  "Goldman Sachs",
  "Capital One",
  "American Express",
  "Discover",
  "USAA",
  "TD Bank",
  "PNC Bank",
  "US Bank",
  "Truist",
  "Fifth Third Bank",
  "KeyBank",
  "Regions Bank",
  "SunTrust",
  "BB&T",
  "Charles Schwab",
  "Fidelity",
  "Vanguard",
  "E*TRADE",
  "Ally Bank",
  "Marcus by Goldman Sachs",
  "Other",
];

const currencies = [
  { value: "USD", label: "USD - US Dollar" },
  { value: "EUR", label: "EUR - Euro" },
  { value: "GBP", label: "GBP - British Pound" },
  { value: "CAD", label: "CAD - Canadian Dollar" },
  { value: "AUD", label: "AUD - Australian Dollar" },
  { value: "JPY", label: "JPY - Japanese Yen" },
  { value: "CHF", label: "CHF - Swiss Franc" },
  { value: "CNY", label: "CNY - Chinese Yuan" },
  { value: "SEK", label: "SEK - Swedish Krona" },
  { value: "NOK", label: "NOK - Norwegian Krone" },
  { value: "DKK", label: "DKK - Danish Krone" },
  { value: "PLN", label: "PLN - Polish Zloty" },
  { value: "CZK", label: "CZK - Czech Koruna" },
  { value: "HUF", label: "HUF - Hungarian Forint" },
  { value: "RON", label: "RON - Romanian Leu" },
  { value: "BGN", label: "BGN - Bulgarian Lev" },
];

// Helper function to get account type icon
const getAccountTypeIcon = (type: string) => {
  switch (type.toLowerCase()) {
    case "checking":
      return <Wallet className="w-5 h-5" />;
    case "savings":
      return <PiggyBank className="w-5 h-5" />;
    case "credit_card":
      return <CreditCard className="w-5 h-5" />;
    case "cash":
      return <Banknote className="w-5 h-5" />;
    case "investment":
      return <TrendingUp className="w-5 h-5" />;
    default:
      return <Building2 className="w-5 h-5" />;
  }
};

// Helper function to get account type color
const getAccountTypeColor = (type: string) => {
  switch (type.toLowerCase()) {
    case "checking":
      return "text-blue-600 bg-blue-100";
    case "savings":
      return "text-green-600 bg-green-100";
    case "credit_card":
      return "text-purple-600 bg-purple-100";
    case "cash":
      return "text-yellow-600 bg-yellow-100";
    case "investment":
      return "text-indigo-600 bg-indigo-100";
    default:
      return "text-gray-600 bg-gray-100";
  }
};

const supabase = createClient();

export default function FamilyAccountsPage() {
  const params = useParams();
  const groupId = params.groupId as string;
  const { settings } = useUserSettings();
  const { formatCurrency } = useCurrencyFormatter();
  const queryClient = useQueryClient();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingAccount, setEditingAccount] =
    useState<FamilyGroupAccount | null>(null);
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);

  useEffect(() => {
    const fetchUser = async () => {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      setCurrentUserId(user?.id || null);
    };
    fetchUser();
  }, []);

  // Fetch family group accounts
  const {
    data: accounts,
    isLoading,
    error,
  } = useQuery<FamilyGroupAccount[]>({
    queryKey: ["familyGroupAccounts", groupId],
    queryFn: () => getFamilyGroupAccounts(groupId),
    enabled: !!groupId,
  });

  const form = useForm<AccountFormValues>({
    resolver: zodResolver(accountFormSchema),
    defaultValues: {
      bank_name_select: "",
      bank_name_custom: "",
      type: "checking",
      balance: "0",
      currency: settings.currency || "USD",
    },
  });

  // Create account mutation
  const createAccountMutation = useMutation({
    mutationFn: async (data: AccountFormValues) => {
      if (!currentUserId) throw new Error("User not authenticated");

      const accountName =
        data.bank_name_select === "Other"
          ? data.bank_name_custom || "Unknown Bank"
          : data.bank_name_select;

      const { data: result, error } = await supabase
        .from("accounts")
        .insert({
          name: accountName,
          type: data.type,
          balance: parseFloat(data.balance),
          currency: data.currency,
          user_id: currentUserId as string,
          family_group_id: groupId,
          is_active: true,
        })
        .select()
        .single();

      if (error) throw error;
      return result;
    },
    onSuccess: () => {
      toast.success("Account created successfully!");
      setIsDialogOpen(false);
      form.reset();
      queryClient.invalidateQueries({
        queryKey: ["familyGroupAccounts", groupId],
      });
    },
    onError: (error: Error) => {
      toast.error(`Failed to create account: ${error.message}`);
    },
  });

  // Update account mutation
  const updateAccountMutation = useMutation({
    mutationFn: async (data: AccountFormValues & { id: string }) => {
      const accountName =
        data.bank_name_select === "Other"
          ? data.bank_name_custom || "Unknown Bank"
          : data.bank_name_select;

      const { data: result, error } = await supabase
        .from("accounts")
        .update({
          name: accountName,
          type: data.type,
          balance: parseFloat(data.balance),
          currency: data.currency,
        })
        .eq("id", data.id)
        .eq("family_group_id", groupId)
        .select()
        .single();

      if (error) throw error;
      return result;
    },
    onSuccess: () => {
      toast.success("Account updated successfully!");
      setIsDialogOpen(false);
      setEditingAccount(null);
      form.reset();
      queryClient.invalidateQueries({
        queryKey: ["familyGroupAccounts", groupId],
      });
    },
    onError: (error: Error) => {
      toast.error(`Failed to update account: ${error.message}`);
    },
  });

  // Delete account mutation
  const deleteAccountMutation = useMutation({
    mutationFn: async (accountId: string) => {
      const { error } = await supabase
        .from("accounts")
        .delete()
        .eq("id", accountId)
        .eq("family_group_id", groupId);

      if (error) throw error;
    },
    onSuccess: () => {
      toast.success("Account deleted successfully!");
      queryClient.invalidateQueries({
        queryKey: ["familyGroupAccounts", groupId],
      });
    },
    onError: (error: Error) => {
      toast.error(`Failed to delete account: ${error.message}`);
    },
  });

  const onSubmit: SubmitHandler<AccountFormValues> = (data) => {
    if (editingAccount) {
      updateAccountMutation.mutate({ ...data, id: editingAccount.id });
    } else {
      createAccountMutation.mutate(data);
    }
  };

  const handleEdit = (account: FamilyGroupAccount) => {
    setEditingAccount(account);

    // Determine if account name is in preset list
    const isPresetBank = presetBanks.includes(account.name);

    form.reset({
      bank_name_select: isPresetBank ? account.name : "Other",
      bank_name_custom: isPresetBank ? "" : account.name,
      type: account.type as
        | "checking"
        | "savings"
        | "credit_card"
        | "cash"
        | "investment"
        | "other",
      balance: account.balance.toString(),
      currency: account.currency,
    });
    setIsDialogOpen(true);
  };

  const handleDelete = (accountId: string) => {
    if (confirm("Are you sure you want to delete this account?")) {
      deleteAccountMutation.mutate(accountId);
    }
  };


  if (isLoading) {
    return (
      <div className="container mx-auto p-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading accounts...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <Card className="w-full max-w-md">
            <CardContent className="p-6 text-center">
              <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                Error Loading Accounts
              </h3>
              <p className="text-muted-foreground">
                {error instanceof Error
                  ? error.message
                  : "Failed to load accounts"}
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 py-8 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center gap-2 mb-2 text-sm text-muted-foreground">
            <Link
              href="/dashboard/family"
              className="hover:text-primary transition-colors flex items-center gap-1"
            >
              <Home className="h-4 w-4" />
              Family Finance
            </Link>
            <span>/</span>
            <Link
              href={`/dashboard/family/${groupId}`}
              className="hover:text-primary transition-colors"
            >
              Group Dashboard
            </Link>
            <span>/</span>
            <span className="text-foreground font-medium">Accounts</span>
          </div>
          <h1 className="text-3xl font-bold tracking-tight">Family Accounts</h1>
          <p className="text-muted-foreground">
            Manage your family group&apos;s bank accounts and financial assets
          </p>
        </div>

        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button
              onClick={() => {
                setEditingAccount(null);
                form.reset({
                  bank_name_select: "",
                  bank_name_custom: "",
                  type: "checking",
                  balance: "0",
                  currency: settings.currency || "USD",
                });
              }}
            >
              <Plus className="mr-2 h-4 w-4" />
              Add Account
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>
                {editingAccount ? "Edit Account" : "Add New Account"}
              </DialogTitle>
              <DialogDescription>
                {editingAccount
                  ? "Update the account details below."
                  : "Enter the details for your new bank account."}
              </DialogDescription>
            </DialogHeader>

            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-4"
              >
                <FormField
                  control={form.control}
                  name="bank_name_select"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Bank Name</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a bank" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {presetBanks.map((bank) => (
                            <SelectItem key={bank} value={bank}>
                              {bank}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {form.watch("bank_name_select") === "Other" && (
                  <FormField
                    control={form.control}
                    name="bank_name_custom"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Custom Bank Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter bank name" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}

                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Account Type</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select account type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="checking">Checking</SelectItem>
                          <SelectItem value="savings">Savings</SelectItem>
                          <SelectItem value="credit_card">
                            Credit Card
                          </SelectItem>
                          <SelectItem value="cash">Cash</SelectItem>
                          <SelectItem value="investment">Investment</SelectItem>
                          <SelectItem value="other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="balance"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Current Balance</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            placeholder="0.00"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="currency"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Currency</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          value={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select currency" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {currencies.map((currency) => (
                              <SelectItem
                                key={currency.value}
                                value={currency.value}
                              >
                                {currency.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <DialogFooter>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setIsDialogOpen(false);
                      setEditingAccount(null);
                      form.reset();
                    }}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={
                      createAccountMutation.isPending ||
                      updateAccountMutation.isPending
                    }
                  >
                    {createAccountMutation.isPending ||
                    updateAccountMutation.isPending
                      ? "Saving..."
                      : editingAccount
                      ? "Update"
                      : "Create"}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Accounts Grid */}
      {accounts && accounts.length > 0 ? (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {accounts.map((account) => (
            <Card
              key={account.id}
              className="hover:shadow-lg transition-shadow"
            >
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="flex items-center space-x-2">
                  <div
                    className={`p-2 rounded-lg ${getAccountTypeColor(
                      account.type
                    )}`}
                  >
                    {getAccountTypeIcon(account.type)}
                  </div>
                  <div>
                    <CardTitle className="text-lg">{account.name}</CardTitle>
                    <CardDescription className="capitalize">
                      {account.type.replace("_", " ")}
                    </CardDescription>
                  </div>
                </div>
                <div className="flex space-x-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleEdit(account)}
                    disabled={account.created_by_user_id !== currentUserId}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDelete(account.id)}
                    disabled={account.created_by_user_id !== currentUserId}
                    className="hover:text-red-600"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">
                      Balance
                    </span>
                    <span
                      className={`text-xl font-bold ${
                        account.balance >= 0 ? "text-green-600" : "text-red-600"
                      }`}
                    >
                      {formatCurrency(account.balance)}
                    </span>
                  </div>

                  <div className="flex justify-between items-center text-sm">
                    <span className="text-muted-foreground">Created by</span>
                    <Badge variant="outline">{account.created_by_email}</Badge>
                  </div>

                  <div className="flex justify-between items-center text-sm">
                    <span className="text-muted-foreground">Created</span>
                    <span className="text-muted-foreground">
                      {new Date(account.created_at).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="p-12 text-center">
            <Building2 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No accounts yet</h3>
            <p className="text-muted-foreground mb-4">
              Start by adding your first family account to track your finances
              together.
            </p>
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Add First Account
                </Button>
              </DialogTrigger>
            </Dialog>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
