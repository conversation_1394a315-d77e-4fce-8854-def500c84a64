"use client";

import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/shared/components/ui/card";
import { Button } from "@/shared/components/ui/button";
import { Input } from "@/shared/components/ui/input";
import { Label } from "@/shared/components/ui/label";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/shared/components/ui/tabs";
import { Alert, AlertDescription } from "@/shared/components/ui/alert";
import { Badge } from "@/shared/components/ui/badge";
import { Switch } from "@/shared/components/ui/switch";
import { Separator } from "@/shared/components/ui/separator";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/shared/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/shared/components/ui/select";
import {
  Settings,
  Users,
  Shield,
  Trash2,
  Save,
  ArrowLeft,
  Crown,
  UserCheck,
  UserX,
  AlertTriangle,
  Loader2,
  Mail,
  UserPlus,
  X,
} from "lucide-react";
import {
  getFamilyGroupDetails,
  updateFamilyGroupName,
  removeMemberFromGroup,
  updateMemberRole,
  deleteFamilyGroup,
  inviteToFamilyGroup,
  getGroupPendingInvitations,
  cancelInvitation,
} from "@/features/family-groups/services/family-groups";
import {
  type FamilyGroupDetails,
  type FamilyGroupMember,
} from "@/shared/types/family";
import { createClient } from "@/shared/services/supabase/client";
import { useToast } from "@/shared/hooks/use-toast";

const supabase = createClient();

export default function GroupSettingsPage() {
  const params = useParams();
  const router = useRouter();
  const groupId = params.groupId as string;
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const [currentUserId, setCurrentUserId] = useState<string | null>(null);
  const [isCurrentUserAdmin, setIsCurrentUserAdmin] = useState(false);
  const [groupName, setGroupName] = useState("");
  const [isEditingName, setIsEditingName] = useState(false);
  const [deleteConfirmText, setDeleteConfirmText] = useState("");
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [inviteEmail, setInviteEmail] = useState("");

  useEffect(() => {
    const fetchUser = async () => {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      setCurrentUserId(user?.id || null);
    };
    fetchUser();
  }, []);

  const {
    data: groupDetails,
    isLoading,
    error,
  } = useQuery<FamilyGroupDetails | null, Error>({
    queryKey: ["familyGroupDetails", groupId],
    queryFn: () => getFamilyGroupDetails(groupId),
    enabled: !!groupId,
  });

  useEffect(() => {
    if (groupDetails && currentUserId) {
      setGroupName(groupDetails.group_name);
      const currentMember = groupDetails.members.find(
        (m) => m.user_id === currentUserId
      );
      setIsCurrentUserAdmin(currentMember?.role === "admin");
    }
  }, [groupDetails, currentUserId]);

  const updateNameMutation = useMutation({
    mutationFn: (newName: string) => updateFamilyGroupName(groupId, newName),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["familyGroupDetails", groupId],
      });
      setIsEditingName(false);
      toast({
        title: "Success",
        description: "Group name updated successfully",
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to update group name",
        variant: "destructive",
      });
    },
  });

  const removeMemberMutation = useMutation({
    mutationFn: (userId: string) => removeMemberFromGroup(groupId, userId),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["familyGroupDetails", groupId],
      });
      toast({
        title: "Success",
        description: "Member removed successfully",
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to remove member",
        variant: "destructive",
      });
    },
  });

  const updateRoleMutation = useMutation({
    mutationFn: ({
      userId,
      role,
    }: {
      userId: string;
      role: "admin" | "member";
    }) => updateMemberRole(groupId, userId, role),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["familyGroupDetails", groupId],
      });
      toast({
        title: "Success",
        description: "Member role updated successfully",
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to update member role",
        variant: "destructive",
      });
    },
  });

  const deleteGroupMutation = useMutation({
    mutationFn: () => deleteFamilyGroup(groupId),
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Family group deleted successfully",
      });
      // Redirect to family groups page after successful deletion
      router.push("/dashboard/family");
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to delete family group",
        variant: "destructive",
      });
    },
  });

  // Pending invitations query
  const { data: pendingInvitations } = useQuery({
    queryKey: ["groupPendingInvitations", groupId],
    queryFn: () => getGroupPendingInvitations(groupId),
    enabled: !!groupId && isCurrentUserAdmin,
  });

  const inviteMutation = useMutation({
    mutationFn: (email: string) => inviteToFamilyGroup(groupId, email),
    onSuccess: (data) => {
      queryClient.invalidateQueries({
        queryKey: ["groupPendingInvitations", groupId],
      });

      // Show enhanced success message based on email status
      if (data.email_sent) {
        toast({
          title: "Invitation Sent!",
          description: `Invitation email sent to ${inviteEmail}. They will receive an email with the invitation link.`,
        });
      } else {
        toast({
          title: "Invitation Created",
          description: `Invitation created but email could not be sent. You can share this link: ${data.invitation_link}`,
          variant: "default",
        });
      }
      setInviteEmail("");
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to send invitation",
        variant: "destructive",
      });
    },
  });

  const cancelInvitationMutation = useMutation({
    mutationFn: (invitationId: string) => cancelInvitation(invitationId),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["groupPendingInvitations", groupId],
      });
      toast({
        title: "Success",
        description: "Invitation cancelled successfully",
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to cancel invitation",
        variant: "destructive",
      });
    },
  });

  const handleSaveName = () => {
    if (groupName.trim() && groupName !== groupDetails?.group_name) {
      updateNameMutation.mutate(groupName.trim());
    } else {
      setIsEditingName(false);
    }
  };

  const handleRemoveMember = (userId: string) => {
    if (userId === currentUserId) {
      toast({
        title: "Error",
        description: "You cannot remove yourself from the group",
        variant: "destructive",
      });
      return;
    }
    removeMemberMutation.mutate(userId);
  };

  const handleRoleChange = (userId: string, newRole: "admin" | "member") => {
    if (userId === currentUserId) {
      toast({
        title: "Error",
        description: "You cannot change your own role",
        variant: "destructive",
      });
      return;
    }
    updateRoleMutation.mutate({ userId, role: newRole });
  };

  const handleDeleteGroup = () => {
    if (deleteConfirmText === "DELETE") {
      deleteGroupMutation.mutate();
      setIsDeleteDialogOpen(false);
      setDeleteConfirmText("");
    }
  };

  const handleInviteUser = () => {
    if (!inviteEmail.trim()) {
      toast({
        title: "Error",
        description: "Please enter an email address",
        variant: "destructive",
      });
      return;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(inviteEmail)) {
      toast({
        title: "Error",
        description: "Please enter a valid email address",
        variant: "destructive",
      });
      return;
    }

    inviteMutation.mutate(inviteEmail);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (error || !groupDetails) {
    return (
      <div className="container mx-auto p-4">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Failed to load group details. Please try again.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 sm:p-6 lg:p-8 space-y-6">
      <div className="flex items-center gap-4 mb-6">
        <Button variant="ghost" size="icon" onClick={() => router.back()}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Group Settings</h1>
          <p className="text-muted-foreground">
            Manage {groupDetails.group_name} settings and members
          </p>
        </div>
      </div>

      <Tabs defaultValue="general" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="general">
            <Settings className="h-4 w-4 mr-2" />
            General
          </TabsTrigger>
          <TabsTrigger value="members">
            <Users className="h-4 w-4 mr-2" />
            Members
          </TabsTrigger>
          <TabsTrigger value="advanced">
            <Shield className="h-4 w-4 mr-2" />
            Advanced
          </TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Group Information</CardTitle>
              <CardDescription>
                Basic information about your family group
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="groupName">Group Name</Label>
                <div className="flex gap-2">
                  <Input
                    id="groupName"
                    value={groupName}
                    onChange={(e) => setGroupName(e.target.value)}
                    disabled={!isCurrentUserAdmin || !isEditingName}
                    placeholder="Enter group name"
                  />
                  {isCurrentUserAdmin && (
                    <>
                      {!isEditingName ? (
                        <Button
                          variant="outline"
                          onClick={() => setIsEditingName(true)}
                        >
                          Edit
                        </Button>
                      ) : (
                        <div className="flex gap-2">
                          <Button
                            onClick={handleSaveName}
                            disabled={updateNameMutation.isPending}
                          >
                            {updateNameMutation.isPending ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <Save className="h-4 w-4" />
                            )}
                          </Button>
                          <Button
                            variant="outline"
                            onClick={() => {
                              setGroupName(groupDetails.group_name);
                              setIsEditingName(false);
                            }}
                          >
                            Cancel
                          </Button>
                        </div>
                      )}
                    </>
                  )}
                </div>
                {!isCurrentUserAdmin && (
                  <p className="text-sm text-muted-foreground">
                    Only group administrators can edit the group name
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label>Group ID</Label>
                <Input value={groupId} disabled />
                <p className="text-sm text-muted-foreground">
                  This is your group&apos;s unique identifier
                </p>
              </div>

              <div className="space-y-2">
                <Label>Created</Label>
                <Input
                  value={new Date(
                    groupDetails.group_created_at
                  ).toLocaleDateString()}
                  disabled
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="members" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>
                Group Members ({groupDetails.members.length})
              </CardTitle>
              <CardDescription>
                Manage member roles and permissions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {groupDetails.members.map((member: FamilyGroupMember) => (
                  <div
                    key={member.user_id}
                    className="flex items-center justify-between p-4 border rounded-lg"
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                        {member.role === "admin" ? (
                          <Crown className="h-5 w-5 text-primary" />
                        ) : (
                          <UserCheck className="h-5 w-5 text-muted-foreground" />
                        )}
                      </div>
                      <div>
                        <p className="font-medium">{member.email}</p>
                        <div className="flex items-center gap-2">
                          <Badge
                            variant={
                              member.role === "admin" ? "default" : "secondary"
                            }
                          >
                            {member.role}
                          </Badge>
                          {member.user_id === currentUserId && (
                            <Badge variant="outline">You</Badge>
                          )}
                        </div>
                      </div>
                    </div>

                    {isCurrentUserAdmin && member.user_id !== currentUserId && (
                      <div className="flex items-center gap-2">
                        <Select
                          value={member.role}
                          onValueChange={(value: "admin" | "member") =>
                            handleRoleChange(member.user_id, value)
                          }
                          disabled={updateRoleMutation.isPending}
                        >
                          <SelectTrigger className="w-32">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="admin">Admin</SelectItem>
                            <SelectItem value="member">Member</SelectItem>
                          </SelectContent>
                        </Select>

                        <Dialog>
                          <DialogTrigger asChild>
                            <Button
                              variant="outline"
                              size="icon"
                              className="text-destructive hover:text-destructive"
                            >
                              <UserX className="h-4 w-4" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent>
                            <DialogHeader>
                              <DialogTitle>Remove Member</DialogTitle>
                              <DialogDescription>
                                Are you sure you want to remove {member.email}{" "}
                                from this group? This action cannot be undone.
                              </DialogDescription>
                            </DialogHeader>
                            <DialogFooter>
                              <Button variant="outline">Cancel</Button>
                              <Button
                                variant="destructive"
                                onClick={() =>
                                  handleRemoveMember(member.user_id)
                                }
                                disabled={removeMemberMutation.isPending}
                              >
                                {removeMemberMutation.isPending ? (
                                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                ) : (
                                  <UserX className="h-4 w-4 mr-2" />
                                )}
                                Remove Member
                              </Button>
                            </DialogFooter>
                          </DialogContent>
                        </Dialog>
                      </div>
                    )}
                  </div>
                ))}
              </div>

              {/* Invite Members Section - Only for Admins */}
              {isCurrentUserAdmin && (
                <>
                  <Separator className="my-6" />
                  <div className="space-y-4">
                    <div>
                      <h3 className="text-lg font-medium">Invite New Member</h3>
                      <p className="text-sm text-muted-foreground">
                        Send an invitation to join this family group
                      </p>
                    </div>
                    <div className="flex gap-3">
                      <div className="flex-1">
                        <Label htmlFor="inviteEmail" className="sr-only">
                          Email address
                        </Label>
                        <Input
                          id="inviteEmail"
                          type="email"
                          placeholder="Enter email address"
                          value={inviteEmail}
                          onChange={(e) => setInviteEmail(e.target.value)}
                          onKeyDown={(e) => {
                            if (e.key === "Enter") {
                              handleInviteUser();
                            }
                          }}
                        />
                      </div>
                      <Button
                        onClick={handleInviteUser}
                        disabled={
                          inviteMutation.isPending || !inviteEmail.trim()
                        }
                      >
                        {inviteMutation.isPending ? (
                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        ) : (
                          <UserPlus className="h-4 w-4 mr-2" />
                        )}
                        Send Invite
                      </Button>
                    </div>
                  </div>

                  {/* Pending Invitations Section */}
                  {pendingInvitations && pendingInvitations.length > 0 && (
                    <>
                      <Separator className="my-6" />
                      <div className="space-y-4">
                        <div>
                          <h3 className="text-lg font-medium">
                            Pending Invitations ({pendingInvitations.length})
                          </h3>
                          <p className="text-sm text-muted-foreground">
                            Invitations that haven&apos;t been accepted yet
                          </p>
                        </div>
                        <div className="space-y-3">
                          {pendingInvitations.map((invitation) => (
                            <div
                              key={invitation.id}
                              className="flex items-center justify-between p-3 border rounded-lg bg-muted/20"
                            >
                              <div className="flex items-center gap-3">
                                <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                                  <Mail className="h-5 w-5 text-orange-600" />
                                </div>
                                <div>
                                  <p className="font-medium">
                                    {invitation.invitee_email ||
                                      invitation.email}
                                  </p>
                                  <p className="text-sm text-muted-foreground">
                                    Invited on{" "}
                                    {new Date(
                                      invitation.created_at
                                    ).toLocaleDateString()}
                                    {" • "}
                                    Expires{" "}
                                    {new Date(
                                      invitation.expires_at
                                    ).toLocaleDateString()}
                                  </p>
                                </div>
                              </div>
                              <div className="flex items-center gap-2">
                                <Badge variant="secondary">Pending</Badge>
                                <Button
                                  variant="outline"
                                  size="icon"
                                  onClick={() =>
                                    cancelInvitationMutation.mutate(
                                      invitation.id
                                    )
                                  }
                                  disabled={cancelInvitationMutation.isPending}
                                  className="text-destructive hover:text-destructive"
                                >
                                  {cancelInvitationMutation.isPending ? (
                                    <Loader2 className="h-4 w-4 animate-spin" />
                                  ) : (
                                    <X className="h-4 w-4" />
                                  )}
                                </Button>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </>
                  )}
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="advanced" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Notification Settings</CardTitle>
              <CardDescription>
                Configure how you receive notifications for this group
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Transaction Notifications</Label>
                  <p className="text-sm text-muted-foreground">
                    Get notified when new transactions are added
                  </p>
                </div>
                <Switch defaultChecked />
              </div>

              <Separator />

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Budget Alerts</Label>
                  <p className="text-sm text-muted-foreground">
                    Receive alerts when budgets are exceeded
                  </p>
                </div>
                <Switch defaultChecked />
              </div>

              <Separator />

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Member Activity</Label>
                  <p className="text-sm text-muted-foreground">
                    Get notified about member joins and leaves
                  </p>
                </div>
                <Switch defaultChecked />
              </div>
            </CardContent>
          </Card>

          {isCurrentUserAdmin && (
            <Card className="border-destructive">
              <CardHeader>
                <CardTitle className="text-destructive">Danger Zone</CardTitle>
                <CardDescription>
                  Irreversible and destructive actions
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Alert variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    Deleting a group will permanently remove all associated data
                    including transactions, budgets, and goals. This action
                    cannot be undone.
                  </AlertDescription>
                </Alert>

                <Dialog
                  open={isDeleteDialogOpen}
                  onOpenChange={setIsDeleteDialogOpen}
                >
                  <DialogTrigger asChild>
                    <Button variant="destructive" className="w-full">
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete Group
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Delete Group</DialogTitle>
                      <DialogDescription>
                        This action cannot be undone. This will permanently
                        delete the group &quot;{groupDetails.group_name}&quot;
                        and all associated data.
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label>
                          Type{" "}
                          <code className="bg-muted px-1 rounded">DELETE</code>{" "}
                          to confirm:
                        </Label>
                        <Input
                          value={deleteConfirmText}
                          onChange={(e) => setDeleteConfirmText(e.target.value)}
                          placeholder="Type DELETE to confirm"
                        />
                      </div>
                    </div>
                    <DialogFooter>
                      <Button
                        variant="outline"
                        onClick={() => {
                          setIsDeleteDialogOpen(false);
                          setDeleteConfirmText("");
                        }}
                      >
                        Cancel
                      </Button>
                      <Button
                        variant="destructive"
                        disabled={
                          deleteConfirmText !== "DELETE" ||
                          deleteGroupMutation.isPending
                        }
                        onClick={handleDeleteGroup}
                      >
                        {deleteGroupMutation.isPending ? (
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        ) : (
                          <Trash2 className="h-4 w-4 mr-2" />
                        )}
                        {deleteGroupMutation.isPending
                          ? "Deleting..."
                          : "Delete Group"}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
