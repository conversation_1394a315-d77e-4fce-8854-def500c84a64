"use client";

import React, { useState, useMemo } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useQuery } from "@tanstack/react-query";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/shared/components/ui/card";
import {
  Ta<PERSON>,
  <PERSON>bs<PERSON>ontent,
  TabsList,
  TabsTrigger,
} from "@/shared/components/ui/tabs";
import { <PERSON><PERSON> } from "@/shared/components/ui/button";
import { Badge } from "@/shared/components/ui/badge";
import { Progress } from "@/shared/components/ui/progress";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/shared/components/ui/select";
import {
  ArrowLeft,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Users,
  Download,
  BarChart3,
  <PERSON><PERSON><PERSON>,
  LineChart,
  Loader2,
  AlertTriangle,
} from "lucide-react";
import { format, subMonths, startOfMonth, endOfMonth } from "date-fns";
import {
  getFamilyGroupFinancialSummary,
  getFamilyGroupTransactions,
  getFamilyGroupBudgets,
  type FamilyGroupFinancialSummary,
  type FamilyGroupTransaction,
  type FamilyGroupBudget,
} from "@/features/family-groups/services/family-finances";
import { getFamilyGroupDetails } from "@/features/family-groups/services/family-groups";
import { type FamilyGroupDetails } from "@/shared/types/family";

// Chart components (we'll use simple progress bars for now, can be enhanced with recharts later)
interface ChartData {
  name: string;
  value: number;
  color?: string;
}

interface SimpleBarChartProps {
  data: ChartData[];
  title: string;
}

const SimpleBarChart: React.FC<SimpleBarChartProps> = ({ data, title }) => (
  <div className="space-y-4">
    <h4 className="font-medium">{title}</h4>
    {data.map((item, index) => (
      <div key={index} className="space-y-2">
        <div className="flex justify-between text-sm">
          <span>{item.name}</span>
          <span className="font-medium">${item.value.toFixed(2)}</span>
        </div>
        <Progress
          value={(item.value / Math.max(...data.map((d) => d.value))) * 100}
          className="h-2"
        />
      </div>
    ))}
  </div>
);

export default function GroupReportsPage() {
  const params = useParams();
  const router = useRouter();
  const groupId = params.groupId as string;
  const [selectedPeriod, setSelectedPeriod] = useState("3months");

  // Calculate date range based on selected period
  const dateRange = useMemo(() => {
    const now = new Date();
    let startDate: Date;

    switch (selectedPeriod) {
      case "1month":
        startDate = startOfMonth(subMonths(now, 1));
        break;
      case "3months":
        startDate = startOfMonth(subMonths(now, 3));
        break;
      case "6months":
        startDate = startOfMonth(subMonths(now, 6));
        break;
      case "1year":
        startDate = startOfMonth(subMonths(now, 12));
        break;
      default:
        startDate = startOfMonth(subMonths(now, 3));
    }

    return {
      start: startDate,
      end: endOfMonth(now),
    };
  }, [selectedPeriod]);

  // Fetch group details
  const { data: groupDetails, isLoading: groupLoading } =
    useQuery<FamilyGroupDetails | null>({
      queryKey: ["familyGroupDetails", groupId],
      queryFn: () => getFamilyGroupDetails(groupId),
      enabled: !!groupId,
    });

  // Fetch financial summary
  const { data: summary, isLoading: summaryLoading } =
    useQuery<FamilyGroupFinancialSummary>({
      queryKey: ["familyGroupFinancialSummary", groupId],
      queryFn: () => getFamilyGroupFinancialSummary(groupId),
      enabled: !!groupId,
    });

  // Fetch transactions for the selected period
  const { data: transactions, isLoading: transactionsLoading } = useQuery<
    FamilyGroupTransaction[]
  >({
    queryKey: ["familyGroupTransactions", groupId, selectedPeriod],
    queryFn: () => getFamilyGroupTransactions(groupId, 100, 0), // Fetch more transactions for analytics
    enabled: !!groupId,
  });

  // Fetch budgets
  const { data: budgets, isLoading: budgetsLoading } = useQuery<
    FamilyGroupBudget[]
  >({
    queryKey: ["familyGroupBudgets", groupId],
    queryFn: () => getFamilyGroupBudgets(groupId),
    enabled: !!groupId,
  });

  // Process data for charts
  const chartData = useMemo(() => {
    if (!transactions || !groupDetails) return null;

    // Filter transactions by date range
    const filteredTransactions = transactions.filter((t) => {
      const transactionDate = new Date(t.date);
      return (
        transactionDate >= dateRange.start && transactionDate <= dateRange.end
      );
    });

    // Spending by member
    const spendingByMember = groupDetails.members.reduce(
      (acc: ChartData[], member) => {
        const memberTransactions = filteredTransactions.filter(
          (t) => t.created_by_user_id === member.user_id
        );
        const totalSpent = memberTransactions
          .filter((t) => t.type === "expense")
          .reduce((sum, t) => sum + Math.abs(t.amount), 0);

        acc.push({
          name: member.email
            ? member.email.split("@")[0]
            : `User ${member.user_id.slice(0, 8)}`, // Use username part of email or fallback
          value: totalSpent,
        });
        return acc;
      },
      [] as ChartData[]
    );

    // Spending by category
    const spendingByCategory = filteredTransactions
      .filter((t) => t.type === "expense")
      .reduce((acc, transaction) => {
        const category = transaction.category_name || "Uncategorized";
        const existing = acc.find((item) => item.name === category);
        if (existing) {
          existing.value += Math.abs(transaction.amount);
        } else {
          acc.push({
            name: category,
            value: Math.abs(transaction.amount),
          });
        }
        return acc;
      }, [] as ChartData[])
      .sort((a, b) => b.value - a.value)
      .slice(0, 10); // Top 10 categories

    // Monthly trends
    const monthlyTrends = filteredTransactions.reduce((acc, transaction) => {
      const month = format(new Date(transaction.date), "MMM yyyy");
      const existing = acc.find((item) => item.name === month);
      const amount =
        transaction.type === "expense" ? Math.abs(transaction.amount) : 0;

      if (existing) {
        existing.value += amount;
      } else {
        acc.push({
          name: month,
          value: amount,
        });
      }
      return acc;
    }, [] as ChartData[]);

    return {
      spendingByMember,
      spendingByCategory,
      monthlyTrends,
    };
  }, [transactions, groupDetails, dateRange]);

  // Calculate summary statistics
  const summaryStats = useMemo(() => {
    if (!transactions || !summary) return null;

    const filteredTransactions = transactions.filter((t) => {
      const transactionDate = new Date(t.date);
      return (
        transactionDate >= dateRange.start && transactionDate <= dateRange.end
      );
    });

    const totalIncome = filteredTransactions
      .filter((t) => t.type === "income")
      .reduce((sum, t) => sum + t.amount, 0);

    const totalExpenses = filteredTransactions
      .filter((t) => t.type === "expense")
      .reduce((sum, t) => sum + Math.abs(t.amount), 0);

    const netIncome = totalIncome - totalExpenses;
    const transactionCount = filteredTransactions.length;

    return {
      totalIncome,
      totalExpenses,
      netIncome,
      transactionCount,
      averageTransaction:
        transactionCount > 0 ? totalExpenses / transactionCount : 0,
    };
  }, [transactions, summary, dateRange]);

  const isLoading =
    groupLoading || summaryLoading || transactionsLoading || budgetsLoading;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!groupDetails || !summary) {
    return (
      <div className="container mx-auto p-4">
        <div className="flex items-center gap-4 mb-6">
          <Button variant="ghost" size="icon" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-3xl font-bold">Group Reports</h1>
        </div>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2 text-muted-foreground">
              <AlertTriangle className="h-5 w-5" />
              <p>Unable to load group data. Please try again.</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 sm:p-6 lg:p-8 space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="icon" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Group Reports</h1>
            <p className="text-muted-foreground">
              Analytics for {groupDetails.group_name}
            </p>
          </div>
        </div>

        <div className="flex items-center gap-4">
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1month">Last Month</SelectItem>
              <SelectItem value="3months">Last 3 Months</SelectItem>
              <SelectItem value="6months">Last 6 Months</SelectItem>
              <SelectItem value="1year">Last Year</SelectItem>
            </SelectContent>
          </Select>

          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      {summaryStats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Income
              </CardTitle>
              <TrendingUp className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                ${summaryStats.totalIncome.toFixed(2)}
              </div>
              <p className="text-xs text-muted-foreground">
                {format(dateRange.start, "MMM d")} -{" "}
                {format(dateRange.end, "MMM d, yyyy")}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Expenses
              </CardTitle>
              <TrendingDown className="h-4 w-4 text-red-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">
                ${summaryStats.totalExpenses.toFixed(2)}
              </div>
              <p className="text-xs text-muted-foreground">
                {summaryStats.transactionCount} transactions
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Net Income</CardTitle>
              <DollarSign className="h-4 w-4 text-blue-500" />
            </CardHeader>
            <CardContent>
              <div
                className={`text-2xl font-bold ${
                  summaryStats.netIncome >= 0
                    ? "text-green-600"
                    : "text-red-600"
                }`}
              >
                ${summaryStats.netIncome.toFixed(2)}
              </div>
              <p className="text-xs text-muted-foreground">Income - Expenses</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Avg Transaction
              </CardTitle>
              <BarChart3 className="h-4 w-4 text-purple-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">
                ${summaryStats.averageTransaction.toFixed(2)}
              </div>
              <p className="text-xs text-muted-foreground">Per transaction</p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Charts and Analytics */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">
            <BarChart3 className="h-4 w-4 mr-2" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="members">
            <Users className="h-4 w-4 mr-2" />
            By Member
          </TabsTrigger>
          <TabsTrigger value="categories">
            <PieChart className="h-4 w-4 mr-2" />
            Categories
          </TabsTrigger>
          <TabsTrigger value="trends">
            <LineChart className="h-4 w-4 mr-2" />
            Trends
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Budget Performance */}
            <Card>
              <CardHeader>
                <CardTitle>Budget Performance</CardTitle>
                <CardDescription>
                  How well the group is sticking to budgets
                </CardDescription>
              </CardHeader>
              <CardContent>
                {budgets && budgets.length > 0 ? (
                  <div className="space-y-4">
                    {budgets.slice(0, 5).map((budget) => {
                      const spent = Math.abs(budget.spent_amount || 0);
                      const percentage =
                        budget.amount > 0 ? (spent / budget.amount) * 100 : 0;
                      const isOverBudget = percentage > 100;

                      return (
                        <div key={budget.id} className="space-y-2">
                          <div className="flex justify-between items-center">
                            <span className="font-medium">
                              {budget.category_name || "General Budget"}
                            </span>
                            <div className="text-right">
                              <div className="text-sm font-medium">
                                ${spent.toFixed(2)} / $
                                {budget.amount.toFixed(2)}
                              </div>
                              <Badge
                                variant={
                                  isOverBudget ? "destructive" : "secondary"
                                }
                              >
                                {percentage.toFixed(1)}%
                              </Badge>
                            </div>
                          </div>
                          <Progress
                            value={Math.min(percentage, 100)}
                            className={`h-2 ${
                              isOverBudget ? "bg-red-100" : ""
                            }`}
                          />
                          {isOverBudget && (
                            <p className="text-xs text-red-600">
                              Over budget by $
                              {(spent - budget.amount).toFixed(2)}
                            </p>
                          )}
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <p className="text-muted-foreground">
                    No budgets set up yet.
                  </p>
                )}
              </CardContent>
            </Card>

            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
                <CardDescription>
                  Latest transactions in the group
                </CardDescription>
              </CardHeader>
              <CardContent>
                {transactions && transactions.length > 0 ? (
                  <div className="space-y-3">
                    {transactions.slice(0, 8).map((transaction) => (
                      <div
                        key={transaction.id}
                        className="flex items-center justify-between"
                      >
                        <div className="flex items-center gap-3">
                          <div
                            className={`w-2 h-2 rounded-full ${
                              transaction.type === "income"
                                ? "bg-green-500"
                                : "bg-red-500"
                            }`}
                          />
                          <div>
                            <p className="font-medium text-sm">
                              {transaction.description}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {transaction.category_name || "Uncategorized"}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p
                            className={`font-medium text-sm ${
                              transaction.type === "income"
                                ? "text-green-600"
                                : "text-red-600"
                            }`}
                          >
                            {transaction.type === "income" ? "+" : "-"}$
                            {Math.abs(transaction.amount).toFixed(2)}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {format(new Date(transaction.date), "MMM d")}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-muted-foreground">
                    No transactions found.
                  </p>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="members" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Spending by Member</CardTitle>
              <CardDescription>
                Compare spending across group members
              </CardDescription>
            </CardHeader>
            <CardContent>
              {chartData?.spendingByMember &&
              chartData.spendingByMember.length > 0 ? (
                <SimpleBarChart
                  data={chartData.spendingByMember}
                  title="Total Spending by Member"
                />
              ) : (
                <p className="text-muted-foreground">
                  No spending data available for the selected period.
                </p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="categories" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Spending by Category</CardTitle>
              <CardDescription>
                Top spending categories for the group
              </CardDescription>
            </CardHeader>
            <CardContent>
              {chartData?.spendingByCategory &&
              chartData.spendingByCategory.length > 0 ? (
                <SimpleBarChart
                  data={chartData.spendingByCategory}
                  title="Top Spending Categories"
                />
              ) : (
                <p className="text-muted-foreground">
                  No category data available for the selected period.
                </p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trends" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Monthly Spending Trends</CardTitle>
              <CardDescription>
                Track spending patterns over time
              </CardDescription>
            </CardHeader>
            <CardContent>
              {chartData?.monthlyTrends &&
              chartData.monthlyTrends.length > 0 ? (
                <SimpleBarChart
                  data={chartData.monthlyTrends}
                  title="Monthly Spending"
                />
              ) : (
                <p className="text-muted-foreground">
                  No trend data available for the selected period.
                </p>
              )}
            </CardContent>
          </Card>

          {/* Group Statistics */}
          <Card>
            <CardHeader>
              <CardTitle>Group Statistics</CardTitle>
              <CardDescription>
                Key metrics for {groupDetails.group_name}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">
                    {groupDetails.members.length}
                  </div>
                  <p className="text-sm text-muted-foreground">Members</p>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">
                    {transactions?.length || 0}
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Total Transactions
                  </p>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">
                    {budgets?.length || 0}
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Active Budgets
                  </p>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">
                    ${summary.total_balance?.toFixed(2) || "0.00"}
                  </div>
                  <p className="text-sm text-muted-foreground">Total Balance</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
