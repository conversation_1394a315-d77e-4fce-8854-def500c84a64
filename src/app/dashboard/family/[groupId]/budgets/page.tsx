"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/shared/components/ui/card";
import { But<PERSON> } from "@/shared/components/ui/button";
import { Progress } from "@/shared/components/ui/progress";
import { Badge } from "@/shared/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/shared/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/shared/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/shared/components/ui/select";
import { Input } from "@/shared/components/ui/input";
import { useF<PERSON>, Submit<PERSON><PERSON><PERSON> } from "react-hook-form";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Plus,
  Edit,
  Trash2,
  AlertCircle,
  AlertTriangle,
  Target,
  Home,
} from "lucide-react";
import { CategoryIcon } from "@/shared/components/ui/category-icon";
import { createClient } from "@/shared/services/supabase/client";
import {
  getFamilyGroupBudgets,
  type FamilyGroupBudget,
} from "@/features/family-groups/services/family-finances";
import { useUserSettings } from "@/shared/contexts/UserSettingsContext";
import { useCurrencyFormatter } from "@/shared/hooks/useCurrencyFormatter";
import { toast } from "sonner";
import Link from "next/link";

// Define form schema
const budgetFormSchema = z.object({
  name: z.string().min(1, "Budget name is required"),
  amount: z
    .string()
    .refine((val) => !isNaN(parseFloat(val)) && parseFloat(val) > 0, {
      message: "Budget amount must be a valid number greater than 0",
    }),
  period: z.enum(["weekly", "monthly", "quarterly", "yearly", "one-time"]),
  category_id: z.string().optional(),
  currency: z.string().min(1, "Currency is required"),
  start_date: z.string().min(1, "Start date is required"),
  end_date: z.string().optional(),
});

// Infer type from schema
type BudgetFormValues = z.infer<typeof budgetFormSchema>;

// Category interface
interface CategoryType {
  id: string;
  name: string;
  color: string | null;
  type: string;
  icon?: string;
}

const currencies = [
  { value: "USD", label: "USD - US Dollar" },
  { value: "EUR", label: "EUR - Euro" },
  { value: "GBP", label: "GBP - British Pound" },
  { value: "CAD", label: "CAD - Canadian Dollar" },
  { value: "AUD", label: "AUD - Australian Dollar" },
  { value: "JPY", label: "JPY - Japanese Yen" },
  { value: "CHF", label: "CHF - Swiss Franc" },
  { value: "CNY", label: "CNY - Chinese Yuan" },
  { value: "SEK", label: "SEK - Swedish Krona" },
  { value: "NOK", label: "NOK - Norwegian Krone" },
  { value: "DKK", label: "DKK - Danish Krone" },
  { value: "PLN", label: "PLN - Polish Zloty" },
  { value: "CZK", label: "CZK - Czech Koruna" },
  { value: "HUF", label: "HUF - Hungarian Forint" },
  { value: "RON", label: "RON - Romanian Leu" },
  { value: "BGN", label: "BGN - Bulgarian Lev" },
];

const supabase = createClient();

export default function FamilyBudgetsPage() {
  const params = useParams();
  const groupId = params.groupId as string;
  const { settings } = useUserSettings();
  const { formatCurrency } = useCurrencyFormatter();
  const queryClient = useQueryClient();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingBudget, setEditingBudget] = useState<FamilyGroupBudget | null>(
    null
  );
  const [categories, setCategories] = useState<CategoryType[]>([]);
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);

  useEffect(() => {
    const fetchUser = async () => {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      setCurrentUserId(user?.id || null);
    };
    fetchUser();
  }, []);

  // Fetch family group budgets
  const {
    data: budgets,
    isLoading,
    error,
  } = useQuery<FamilyGroupBudget[]>({
    queryKey: ["familyGroupBudgets", groupId],
    queryFn: () => getFamilyGroupBudgets(groupId),
    enabled: !!groupId,
  });

  // Fetch categories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const { data, error } = await supabase
          .from("categories")
          .select("id, name, color, type, icon")
          .or(`user_id.eq.${currentUserId},family_group_id.eq.${groupId}`)
          .eq("type", "expense")
          .order("name");

        if (error) throw error;
        setCategories((data || []).map(cat => ({
          ...cat,
          icon: cat.icon || undefined
        })));
      } catch (error) {
        console.error("Error fetching categories:", error);
      }
    };

    if (currentUserId && groupId) {
      fetchCategories();
    }
  }, [currentUserId, groupId]);

  const form = useForm<BudgetFormValues>({
    resolver: zodResolver(budgetFormSchema),
    defaultValues: {
      name: "",
      amount: "",
      period: "monthly",
      category_id: "",
      currency: settings.currency || "USD",
      start_date: new Date().toISOString().split("T")[0],
      end_date: "",
    },
  });

  // Create budget mutation
  const createBudgetMutation = useMutation({
    mutationFn: async (data: BudgetFormValues) => {
      if (!currentUserId) throw new Error("User not authenticated");

      const { data: result, error } = await supabase
        .from("budgets")
        .insert({
          name: data.name,
          amount: parseFloat(data.amount),
          period: data.period,
          category_id:
            data.category_id && data.category_id !== "none"
              ? data.category_id
              : null,
          currency: data.currency,
          start_date: data.start_date,
          end_date: data.end_date || null,
          user_id: currentUserId,
          family_group_id: groupId,
        })
        .select()
        .single();

      if (error) throw error;
      return result;
    },
    onSuccess: () => {
      toast.success("Budget created successfully!");
      setIsDialogOpen(false);
      form.reset();
      queryClient.invalidateQueries({
        queryKey: ["familyGroupBudgets", groupId],
      });
    },
    onError: (error: Error) => {
      toast.error(`Failed to create budget: ${error.message}`);
    },
  });

  // Update budget mutation
  const updateBudgetMutation = useMutation({
    mutationFn: async (data: BudgetFormValues & { id: string }) => {
      const { data: result, error } = await supabase
        .from("budgets")
        .update({
          name: data.name,
          amount: parseFloat(data.amount),
          period: data.period,
          category_id:
            data.category_id && data.category_id !== "none"
              ? data.category_id
              : null,
          currency: data.currency,
          start_date: data.start_date,
          end_date: data.end_date || null,
        })
        .eq("id", data.id)
        .eq("family_group_id", groupId)
        .select()
        .single();

      if (error) throw error;
      return result;
    },
    onSuccess: () => {
      toast.success("Budget updated successfully!");
      setIsDialogOpen(false);
      setEditingBudget(null);
      form.reset();
      queryClient.invalidateQueries({
        queryKey: ["familyGroupBudgets", groupId],
      });
    },
    onError: (error: Error) => {
      toast.error(`Failed to update budget: ${error.message}`);
    },
  });

  // Delete budget mutation
  const deleteBudgetMutation = useMutation({
    mutationFn: async (budgetId: string) => {
      const { error } = await supabase
        .from("budgets")
        .delete()
        .eq("id", budgetId)
        .eq("family_group_id", groupId);

      if (error) throw error;
    },
    onSuccess: () => {
      toast.success("Budget deleted successfully!");
      queryClient.invalidateQueries({
        queryKey: ["familyGroupBudgets", groupId],
      });
    },
    onError: (error: Error) => {
      toast.error(`Failed to delete budget: ${error.message}`);
    },
  });

  const onSubmit: SubmitHandler<BudgetFormValues> = (data) => {
    if (editingBudget) {
      updateBudgetMutation.mutate({ ...data, id: editingBudget.id });
    } else {
      createBudgetMutation.mutate(data);
    }
  };

  const handleEdit = (budget: FamilyGroupBudget) => {
    setEditingBudget(budget);
    form.reset({
      name: budget.category_name || "General Budget",
      amount: budget.amount.toString(),
      period: budget.period as
        | "weekly"
        | "monthly"
        | "quarterly"
        | "yearly"
        | "one-time",
      category_id: budget.category_id || "",
      currency: settings.currency || "USD",
      start_date: budget.start_date,
      end_date: budget.end_date || "",
    });
    setIsDialogOpen(true);
  };

  const handleDelete = (budgetId: string) => {
    if (confirm("Are you sure you want to delete this budget?")) {
      deleteBudgetMutation.mutate(budgetId);
    }
  };


  const getBudgetStatus = (budget: FamilyGroupBudget) => {
    const spentAmount = Math.abs(budget.spent_amount || 0);
    const percentage =
      budget.amount > 0 ? (spentAmount / budget.amount) * 100 : 0;

    if (percentage >= 100) return { status: "over", color: "bg-red-500" };
    if (percentage >= 80) return { status: "warning", color: "bg-yellow-500" };
    return { status: "good", color: "bg-green-500" };
  };

  if (isLoading) {
    return (
      <div className="container mx-auto p-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading budgets...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <Card className="w-full max-w-md">
            <CardContent className="p-6 text-center">
              <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                Error Loading Budgets
              </h3>
              <p className="text-muted-foreground">
                {error instanceof Error
                  ? error.message
                  : "Failed to load budgets"}
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 py-8 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center gap-2 mb-2 text-sm text-muted-foreground">
            <Link
              href="/dashboard/family"
              className="hover:text-primary transition-colors flex items-center gap-1"
            >
              <Home className="h-4 w-4" />
              Family Finance
            </Link>
            <span>/</span>
            <Link
              href={`/dashboard/family/${groupId}`}
              className="hover:text-primary transition-colors"
            >
              Group Dashboard
            </Link>
            <span>/</span>
            <span className="text-foreground font-medium">Budgets</span>
          </div>
          <h1 className="text-3xl font-bold tracking-tight">Family Budgets</h1>
          <p className="text-muted-foreground">
            Set and track spending limits for your family group
          </p>
        </div>

        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button
              onClick={() => {
                setEditingBudget(null);
                form.reset({
                  name: "",
                  amount: "",
                  period: "monthly",
                  category_id: "",
                  currency: settings.currency || "USD",
                  start_date: new Date().toISOString().split("T")[0],
                  end_date: "",
                });
              }}
            >
              <Plus className="mr-2 h-4 w-4" />
              Create Budget
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>
                {editingBudget ? "Edit Budget" : "Create New Budget"}
              </DialogTitle>
              <DialogDescription>
                {editingBudget
                  ? "Update the budget details below."
                  : "Set up a new spending limit for your family group."}
              </DialogDescription>
            </DialogHeader>

            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-4"
              >
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Budget Name</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="e.g., Groceries, Entertainment"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="amount"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Budget Amount</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            placeholder="1000.00"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="currency"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Currency</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          value={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select currency" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {currencies.map((currency) => (
                              <SelectItem
                                key={currency.value}
                                value={currency.value}
                              >
                                {currency.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="period"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Budget Period</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select period" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="weekly">Weekly</SelectItem>
                          <SelectItem value="monthly">Monthly</SelectItem>
                          <SelectItem value="quarterly">Quarterly</SelectItem>
                          <SelectItem value="yearly">Yearly</SelectItem>
                          <SelectItem value="one-time">One-time</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="category_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Category (Optional)</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select category" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="none">No Category</SelectItem>
                          {categories.map((category) => (
                            <SelectItem key={category.id} value={category.id}>
                              <div className="flex items-center space-x-2">
                                <CategoryIcon
                                  icon={category.icon}
                                  className="h-4 w-4"
                                />
                                <span>{category.name}</span>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="start_date"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Start Date</FormLabel>
                        <FormControl>
                          <Input type="date" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="end_date"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>End Date (Optional)</FormLabel>
                        <FormControl>
                          <Input type="date" {...field} />
                        </FormControl>
                        <FormDescription>
                          Leave empty for recurring budgets
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <DialogFooter>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setIsDialogOpen(false);
                      setEditingBudget(null);
                      form.reset();
                    }}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={
                      createBudgetMutation.isPending ||
                      updateBudgetMutation.isPending
                    }
                  >
                    {createBudgetMutation.isPending ||
                    updateBudgetMutation.isPending
                      ? "Saving..."
                      : editingBudget
                      ? "Update"
                      : "Create"}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Budgets Grid */}
      {budgets && budgets.length > 0 ? (
        <div className="space-y-6">
          {budgets.map((budget) => {
            const spentAmount = Math.abs(budget.spent_amount || 0);
            const percentage =
              budget.amount > 0 ? (spentAmount / budget.amount) * 100 : 0;
            const remaining = Math.max(0, budget.amount - spentAmount);
            const budgetStatus = getBudgetStatus(budget);

            return (
              <Card
                key={budget.id}
                className="hover:shadow-lg transition-shadow"
              >
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <div className="flex items-center space-x-2">
                    <div
                      className={`w-4 h-4 rounded-full ${budgetStatus.color}`}
                    ></div>
                    <div>
                      <CardTitle className="text-lg">
                        {budget.category_name || "General Budget"}
                      </CardTitle>
                      <CardDescription className="capitalize">
                        {budget.period.replace("-", " ")} budget
                      </CardDescription>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge
                      variant={
                        budgetStatus.status === "over"
                          ? "destructive"
                          : "outline"
                      }
                    >
                      {percentage.toFixed(0)}% used
                    </Badge>
                    <div className="flex space-x-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEdit(budget)}
                        disabled={budget.created_by_user_id !== currentUserId}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDelete(budget.id)}
                        disabled={budget.created_by_user_id !== currentUserId}
                        className="hover:text-red-600"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <div className="space-y-1">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">Spent:</span>
                          <span className="font-medium">
                            {formatCurrency(spentAmount)}
                          </span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">Budget:</span>
                          <span className="font-medium">
                            {formatCurrency(budget.amount)}
                          </span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">
                            Remaining:
                          </span>
                          <span
                            className={`font-medium ${
                              remaining > 0 ? "text-green-600" : "text-red-600"
                            }`}
                          >
                            {formatCurrency(remaining)}
                          </span>
                        </div>
                      </div>
                    </div>

                    <Progress
                      value={Math.min(percentage, 100)}
                      className={`h-2 ${percentage > 100 ? "bg-red-100" : ""}`}
                    />

                    <div className="flex justify-between items-center text-sm text-muted-foreground">
                      <span>Created by {budget.created_by_email}</span>
                      <span>
                        {budget.start_date}{" "}
                        {budget.end_date && `- ${budget.end_date}`}
                      </span>
                    </div>

                    {percentage > 80 && (
                      <div
                        className={`flex items-center gap-2 p-2 rounded-lg ${
                          percentage >= 100
                            ? "bg-red-50 text-red-700"
                            : "bg-yellow-50 text-yellow-700"
                        }`}
                      >
                        <AlertTriangle className="h-4 w-4" />
                        <span className="text-sm">
                          {percentage >= 100
                            ? `Over budget by ${formatCurrency(
                                spentAmount - budget.amount
                              )}`
                            : "Approaching budget limit"}
                        </span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      ) : (
        <Card>
          <CardContent className="p-12 text-center">
            <Target className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No budgets yet</h3>
            <p className="text-muted-foreground mb-4">
              Create your first budget to start tracking your family&apos;s
              spending limits.
            </p>
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Create First Budget
                </Button>
              </DialogTrigger>
            </Dialog>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
