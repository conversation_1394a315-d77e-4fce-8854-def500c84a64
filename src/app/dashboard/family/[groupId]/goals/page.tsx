"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/shared/components/ui/card";
import { Button } from "@/shared/components/ui/button";
import { Progress } from "@/shared/components/ui/progress";
import { Badge } from "@/shared/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/shared/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/shared/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/shared/components/ui/select";
import { Input } from "@/shared/components/ui/input";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Plus,
  Target,
  Edit,
  Trash2,
  AlertTriangle,
  CheckCircle2,
  Clock,
  Home,
} from "lucide-react";
import { createClient } from "@/shared/services/supabase/client";
import {
  getFamilyGroupGoals,
  type FamilyGroupGoal,
} from "@/features/family-groups/services/family-finances";
import { toast } from "sonner";
import { useUserSettings } from "@/shared/contexts/UserSettingsContext";
import { useCurrencyFormatter } from "@/shared/hooks/useCurrencyFormatter";
import Link from "next/link";

const GOAL_COLORS = [
  "#3b82f6", // Blue
  "#10b981", // Emerald
  "#f59e0b", // Amber
  "#ef4444", // Red
  "#8b5cf6", // Violet
  "#ec4899", // Pink
];

const goalFormSchema = z.object({
  name: z.string().min(1, "Goal name is required"),
  target_amount: z
    .string()
    .refine((val) => !isNaN(parseFloat(val)) && parseFloat(val) > 0, {
      message: "Target amount must be a valid number greater than 0",
    }),
  current_amount: z.string().refine((val) => !isNaN(parseFloat(val)), {
    message: "Current amount must be a valid number",
  }),
  currency: z.string().min(1, "Currency is required"),
  deadline: z.string().optional(),
  color: z.string().min(1, "Color is required"),
});

type GoalFormValues = z.infer<typeof goalFormSchema>;

interface DisplayGoal extends FamilyGroupGoal {
  progress: number;
  daysRemaining?: number;
  monthlyRequired?: number;
}

const currencies = [
  { value: "USD", label: "USD - US Dollar" },
  { value: "EUR", label: "EUR - Euro" },
  { value: "GBP", label: "GBP - British Pound" },
  { value: "CAD", label: "CAD - Canadian Dollar" },
  { value: "AUD", label: "AUD - Australian Dollar" },
  { value: "JPY", label: "JPY - Japanese Yen" },
  { value: "CHF", label: "CHF - Swiss Franc" },
  { value: "CNY", label: "CNY - Chinese Yuan" },
  { value: "SEK", label: "SEK - Swedish Krona" },
  { value: "NOK", label: "NOK - Norwegian Krone" },
  { value: "DKK", label: "DKK - Danish Krone" },
  { value: "PLN", label: "PLN - Polish Zloty" },
  { value: "CZK", label: "CZK - Czech Koruna" },
  { value: "HUF", label: "HUF - Hungarian Forint" },
  { value: "RON", label: "RON - Romanian Leu" },
  { value: "BGN", label: "BGN - Bulgarian Lev" },
];

const supabase = createClient();

export default function FamilyGoalsPage() {
  const params = useParams();
  const groupId = params.groupId as string;
  const [goals, setGoals] = useState<DisplayGoal[]>([]);
  const [loading, setLoading] = useState(true);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingGoal, setEditingGoal] = useState<DisplayGoal | null>(null);
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);
  const queryClient = useQueryClient();
  const { settings } = useUserSettings();
  const defaultCurrency = settings.currency || "USD";

  useEffect(() => {
    const fetchUser = async () => {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      setCurrentUserId(user?.id || null);
    };
    fetchUser();
  }, []);

  // Fetch family group goals
  const {
    data: goalsData,
    isLoading,
    error,
  } = useQuery<FamilyGroupGoal[]>({
    queryKey: ["familyGroupGoals", groupId],
    queryFn: () => getFamilyGroupGoals(groupId),
    enabled: !!groupId,
  });

  useEffect(() => {
    if (goalsData) {
      const processedGoals = goalsData.map((goal) => {
        const progress =
          goal.target_amount > 0
            ? (goal.current_amount / goal.target_amount) * 100
            : 0;

        const daysRemaining = goal.target_date
          ? Math.ceil(
              (new Date(goal.target_date).getTime() - new Date().getTime()) /
                (1000 * 60 * 60 * 24)
            )
          : undefined;

        const monthlyRequired =
          goal.target_date && daysRemaining && daysRemaining > 0
            ? Math.max(
                0,
                (goal.target_amount - goal.current_amount) /
                  (daysRemaining / 30)
              )
            : undefined;

        return {
          ...goal,
          progress,
          daysRemaining,
          monthlyRequired,
        };
      });
      setGoals(processedGoals);
    }
    setLoading(isLoading);
  }, [goalsData, isLoading]);

  const form = useForm<GoalFormValues>({
    resolver: zodResolver(goalFormSchema),
    defaultValues: {
      name: "",
      target_amount: "",
      current_amount: "0",
      currency: defaultCurrency,
      deadline: "",
      color: GOAL_COLORS[0],
    },
  });

  // Create goal mutation
  const createGoalMutation = useMutation({
    mutationFn: async (data: GoalFormValues) => {
      if (!currentUserId) throw new Error("User not authenticated");

      const { data: result, error } = await supabase
        .from("financial_goals")
        .insert({
          name: data.name,
          target_amount: parseFloat(data.target_amount),
          current_amount: parseFloat(data.current_amount),
          currency: data.currency,
          target_date: data.deadline || null,
          color: data.color,
          status: "active",
          user_id: currentUserId,
          family_group_id: groupId,
        })
        .select()
        .single();

      if (error) throw error;
      return result;
    },
    onSuccess: () => {
      toast.success("Goal created successfully!");
      setDialogOpen(false);
      form.reset();
      queryClient.invalidateQueries({
        queryKey: ["familyGroupGoals", groupId],
      });
    },
    onError: (error: Error) => {
      toast.error(`Failed to create goal: ${error.message}`);
    },
  });

  // Update goal mutation
  const updateGoalMutation = useMutation({
    mutationFn: async (data: GoalFormValues & { id: string }) => {
      const { data: result, error } = await supabase
        .from("financial_goals")
        .update({
          name: data.name,
          target_amount: parseFloat(data.target_amount),
          current_amount: parseFloat(data.current_amount),
          currency: data.currency,
          target_date: data.deadline || null,
          color: data.color,
        })
        .eq("id", data.id)
        .eq("family_group_id", groupId)
        .select()
        .single();

      if (error) throw error;
      return result;
    },
    onSuccess: () => {
      toast.success("Goal updated successfully!");
      setDialogOpen(false);
      setEditingGoal(null);
      form.reset();
      queryClient.invalidateQueries({
        queryKey: ["familyGroupGoals", groupId],
      });
    },
    onError: (error: Error) => {
      toast.error(`Failed to update goal: ${error.message}`);
    },
  });

  // Delete goal mutation
  const deleteGoalMutation = useMutation({
    mutationFn: async (goalId: string) => {
      const { error } = await supabase
        .from("financial_goals")
        .delete()
        .eq("id", goalId)
        .eq("family_group_id", groupId);

      if (error) throw error;
    },
    onSuccess: () => {
      toast.success("Goal deleted successfully!");
      queryClient.invalidateQueries({
        queryKey: ["familyGroupGoals", groupId],
      });
    },
    onError: (error: Error) => {
      toast.error(`Failed to delete goal: ${error.message}`);
    },
  });

  const onSubmit = (data: GoalFormValues) => {
    if (editingGoal) {
      updateGoalMutation.mutate({ ...data, id: editingGoal.id });
    } else {
      createGoalMutation.mutate(data);
    }
  };

  const handleEdit = (goal: DisplayGoal) => {
    setEditingGoal(goal);
    form.reset({
      name: goal.name,
      target_amount: goal.target_amount.toString(),
      current_amount: goal.current_amount.toString(),
      currency: defaultCurrency,
      deadline: goal.target_date || "",
      color: goal.color || GOAL_COLORS[0],
    });
    setDialogOpen(true);
  };

  const handleDelete = (goalId: string) => {
    if (confirm("Are you sure you want to delete this goal?")) {
      deleteGoalMutation.mutate(goalId);
    }
  };

  const { formatCurrency } = useCurrencyFormatter();

  const getGoalStatusBadge = (goal: DisplayGoal) => {
    if (goal.progress >= 100) {
      return (
        <Badge className="bg-green-500">
          <CheckCircle2 className="w-3 h-3 mr-1" />
          Completed
        </Badge>
      );
    }

    if (goal.daysRemaining !== undefined) {
      if (goal.daysRemaining < 0) {
        return (
          <Badge variant="destructive">
            <AlertTriangle className="w-3 h-3 mr-1" />
            Overdue
          </Badge>
        );
      } else if (goal.daysRemaining <= 30) {
        return (
          <Badge variant="secondary">
            <Clock className="w-3 h-3 mr-1" />
            Urgent
          </Badge>
        );
      }
    }

    return (
      <Badge variant="outline">
        <Target className="w-3 h-3 mr-1" />
        Active
      </Badge>
    );
  };

  if (loading) {
    return (
      <div className="container mx-auto p-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading goals...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <Card className="w-full max-w-md">
            <CardContent className="p-6 text-center">
              <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                Error Loading Goals
              </h3>
              <p className="text-muted-foreground">
                {error instanceof Error
                  ? error.message
                  : "Failed to load goals"}
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 py-8 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center gap-2 mb-2 text-sm text-muted-foreground">
            <Link
              href="/dashboard/family"
              className="hover:text-primary transition-colors flex items-center gap-1"
            >
              <Home className="h-4 w-4" />
              Family Finance
            </Link>
            <span>/</span>
            <Link
              href={`/dashboard/family/${groupId}`}
              className="hover:text-primary transition-colors"
            >
              Group Dashboard
            </Link>
            <span>/</span>
            <span className="text-foreground font-medium">Goals</span>
          </div>
          <h1 className="text-3xl font-bold tracking-tight">Family Goals</h1>
          <p className="text-muted-foreground">
            Set and track financial goals for your family group
          </p>
        </div>

        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogTrigger asChild>
            <Button
              onClick={() => {
                setEditingGoal(null);
                form.reset({
                  name: "",
                  target_amount: "",
                  current_amount: "0",
                  currency: defaultCurrency,
                  deadline: "",
                  color: GOAL_COLORS[0],
                });
              }}
            >
              <Plus className="mr-2 h-4 w-4" />
              Create Goal
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>
                {editingGoal ? "Edit Goal" : "Create New Goal"}
              </DialogTitle>
              <DialogDescription>
                {editingGoal
                  ? "Update the goal details below."
                  : "Set up a new financial goal for your family group."}
              </DialogDescription>
            </DialogHeader>

            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-4"
              >
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Goal Name</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="e.g., Emergency Fund, Vacation"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="target_amount"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Target Amount</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            placeholder="10000.00"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="current_amount"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Current Amount</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            placeholder="0.00"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="currency"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Currency</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select currency" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {currencies.map((currency) => (
                            <SelectItem
                              key={currency.value}
                              value={currency.value}
                            >
                              {currency.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="deadline"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Target Date (Optional)</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormDescription>
                        Set a deadline to track progress more effectively
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="color"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Color</FormLabel>
                      <FormControl>
                        <div className="flex gap-2">
                          {GOAL_COLORS.map((color) => (
                            <button
                              key={color}
                              type="button"
                              className={`w-8 h-8 rounded-full border-2 ${
                                field.value === color
                                  ? "border-gray-800"
                                  : "border-gray-300"
                              }`}
                              style={{ backgroundColor: color }}
                              onClick={() => field.onChange(color)}
                            />
                          ))}
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <DialogFooter>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setDialogOpen(false);
                      setEditingGoal(null);
                      form.reset();
                    }}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={
                      createGoalMutation.isPending ||
                      updateGoalMutation.isPending
                    }
                  >
                    {createGoalMutation.isPending ||
                    updateGoalMutation.isPending
                      ? "Saving..."
                      : editingGoal
                      ? "Update"
                      : "Create"}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Goals Grid */}
      {goals && goals.length > 0 ? (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {goals.map((goal) => (
            <Card
              key={goal.id}
              className="hover:shadow-lg transition-shadow"
              style={{ borderLeftColor: goal.color, borderLeftWidth: "4px" }}
            >
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="flex items-center space-x-2">
                  <div
                    className="w-4 h-4 rounded-full"
                    style={{ backgroundColor: goal.color }}
                  ></div>
                  <div>
                    <CardTitle className="text-lg">{goal.name}</CardTitle>
                    {goal.daysRemaining !== undefined && (
                      <p className="text-sm text-muted-foreground">
                        {goal.daysRemaining > 0
                          ? `${goal.daysRemaining} days remaining`
                          : goal.daysRemaining === 0
                          ? "Due today"
                          : `${Math.abs(goal.daysRemaining)} days overdue`}
                      </p>
                    )}
                  </div>
                </div>
                <div className="flex items-center space-x-1">
                  {getGoalStatusBadge(goal)}
                  <div className="flex space-x-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEdit(goal)}
                      disabled={goal.created_by_user_id !== currentUserId}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDelete(goal.id)}
                      disabled={goal.created_by_user_id !== currentUserId}
                      className="hover:text-red-600"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Progress</span>
                      <span className="font-medium">
                        {goal.progress.toFixed(1)}%
                      </span>
                    </div>
                    <Progress
                      value={Math.min(goal.progress, 100)}
                      className="h-2"
                    />
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Current:</span>
                      <span className="font-medium">
                        {formatCurrency(goal.current_amount)}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Target:</span>
                      <span className="font-medium">
                        {formatCurrency(goal.target_amount)}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">Remaining:</span>
                      <span
                        className={`font-medium ${
                          goal.current_amount >= goal.target_amount
                            ? "text-green-600"
                            : "text-red-600"
                        }`}
                      >
                        {formatCurrency(
                          Math.max(0, goal.target_amount - goal.current_amount)
                        )}
                      </span>
                    </div>
                  </div>

                  {goal.monthlyRequired && goal.monthlyRequired > 0 && (
                    <div className="p-2 bg-blue-50 rounded-lg">
                      <p className="text-sm text-blue-700">
                        Save ~
                        {formatCurrency(goal.monthlyRequired)}
                        /month to reach your goal
                      </p>
                    </div>
                  )}

                  <div className="flex justify-between items-center text-xs text-muted-foreground pt-2 border-t">
                    <span>Created by {goal.created_by_email}</span>
                    {goal.target_date && (
                      <span>
                        Due {new Date(goal.target_date).toLocaleDateString()}
                      </span>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="p-12 text-center">
            <Target className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No goals yet</h3>
            <p className="text-muted-foreground mb-4">
              Create your first financial goal to start tracking your
              family&apos;s savings progress.
            </p>
            <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Create First Goal
                </Button>
              </DialogTrigger>
            </Dialog>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
