"use client";

import React, { useEffect, useState, useCallback } from "react";
import { useParams } from "next/navigation";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { createClient } from "@/shared/services/supabase/client";
import {
  getFamilyGroupTransactions,
  getFamilyGroupAccounts,
  type FamilyGroupTransaction,
  type FamilyGroupAccount,
} from "@/features/family-groups/services/family-finances";
import { Button } from "@/shared/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogClose,
} from "@/shared/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/shared/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/shared/components/ui/select";
import { Input } from "@/shared/components/ui/input";
import { Label } from "@/shared/components/ui/label";
import { useForm, Controller, SubmitHandler } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { toast } from "sonner";
import { Edit, Trash2, PlusCircle, Home } from "lucide-react";
import { DatePicker } from "@/shared/components/ui/date-picker";
import { useUserSettings } from "@/shared/contexts/UserSettingsContext";
import { useCurrencyFormatter } from "@/shared/hooks/useCurrencyFormatter";
import { ScrollArea } from "@/shared/components/ui/scroll-area";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/shared/components/ui/card";
import { DollarSign } from "lucide-react";
import { CategoryIcon } from "@/shared/components/ui/category-icon";
import Link from "next/link";

const supabase = createClient();

// Zod schema for transaction form validation
const transactionFormSchema = z.object({
  description: z.string().min(1, "Description is required"),
  amount: z.coerce.number().positive("Amount must be positive"),
  type: z.enum(["income", "expense"], { required_error: "Type is required" }),
  date: z.date({ required_error: "Date is required" }),
  category_id: z.string().optional(),
  account_id: z.string({ required_error: "Account is required" }),
  currency: z
    .string()
    .min(3, "Currency is required")
    .max(3, "Currency code must be 3 letters"),
});

type TransactionFormData = z.infer<typeof transactionFormSchema>;

interface CategoryType {
  id: string;
  name: string;
  color: string | null;
  type: string;
  icon?: string;
}


export default function FamilyTransactionsPage() {
  const params = useParams();
  const groupId = params.groupId as string;
  const [transactions, setTransactions] = useState<FamilyGroupTransaction[]>(
    []
  );
  const [accounts, setAccounts] = useState<FamilyGroupAccount[]>([]);
  const [categories, setCategories] = useState<CategoryType[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingTransaction, setEditingTransaction] =
    useState<FamilyGroupTransaction | null>(null);
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);
  const queryClient = useQueryClient();

  const { settings, loading: settingsLoading } = useUserSettings();
  const { formatCurrency } = useCurrencyFormatter();
  const defaultCurrency = settingsLoading ? "USD" : settings?.currency || "USD";

  useEffect(() => {
    const fetchUser = async () => {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      setCurrentUserId(user?.id || null);
    };
    fetchUser();
  }, []);

  // Fetch family group transactions
  const { data: transactionsData, isLoading: transactionsLoading } = useQuery<
    FamilyGroupTransaction[]
  >({
    queryKey: ["familyGroupTransactions", groupId],
    queryFn: () => getFamilyGroupTransactions(groupId, 100, 0),
    enabled: !!groupId,
  });

  // Fetch family group accounts
  const { data: accountsData, isLoading: accountsLoading } = useQuery<
    FamilyGroupAccount[]
  >({
    queryKey: ["familyGroupAccounts", groupId],
    queryFn: () => getFamilyGroupAccounts(groupId),
    enabled: !!groupId,
  });

  // Fetch categories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const { data, error } = await supabase
          .from("categories")
          .select("id, name, color, type")
          .or(`user_id.eq.${currentUserId},family_group_id.eq.${groupId}`)
          .order("name");

        if (error) throw error;
        setCategories(data || []);
      } catch (error) {
        console.error("Error fetching categories:", error);
      }
    };

    if (currentUserId && groupId) {
      fetchCategories();
    }
  }, [currentUserId, groupId]);

  useEffect(() => {
    if (transactionsData) {
      setTransactions(transactionsData);
    }
  }, [transactionsData]);

  useEffect(() => {
    if (accountsData) {
      setAccounts(accountsData);
    }
  }, [accountsData]);

  useEffect(() => {
    setIsLoading(transactionsLoading || accountsLoading);
  }, [transactionsLoading, accountsLoading]);

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
    watch,
  } = useForm<TransactionFormData>({
    resolver: zodResolver(transactionFormSchema),
    defaultValues: {
      description: "",
      amount: 0,
      type: "expense",
      date: new Date(),
      category_id: "none",
      account_id: "",
      currency: defaultCurrency,
    },
  });

  // Create transaction mutation
  const createTransactionMutation = useMutation({
    mutationFn: async (data: TransactionFormData) => {
      if (!currentUserId) throw new Error("User not authenticated");

      const { data: result, error } = await supabase
        .from("transactions")
        .insert({
          user_id: currentUserId,
          family_group_id: groupId,
          account_id: data.account_id,
          category_id:
            data.category_id && data.category_id !== "none"
              ? data.category_id
              : null,
          description: data.description,
          amount:
            data.type === "expense"
              ? -Math.abs(data.amount)
              : Math.abs(data.amount),
          currency: data.currency,
          type: data.type,
          date: data.date.toISOString().split("T")[0],
        })
        .select()
        .single();

      if (error) throw error;
      return result;
    },
    onSuccess: () => {
      toast.success("Transaction created successfully!");
      setIsFormOpen(false);
      reset();
      queryClient.invalidateQueries({
        queryKey: ["familyGroupTransactions", groupId],
      });
      queryClient.invalidateQueries({
        queryKey: ["familyGroupAccounts", groupId],
      });
      queryClient.invalidateQueries({
        queryKey: ["familyGroupFinancialSummary", groupId],
      });
    },
    onError: (error: Error) => {
      toast.error(`Failed to create transaction: ${error.message}`);
    },
  });

  // Update transaction mutation
  const updateTransactionMutation = useMutation({
    mutationFn: async (data: TransactionFormData & { id: string }) => {
      const { data: result, error } = await supabase
        .from("transactions")
        .update({
          account_id: data.account_id,
          category_id:
            data.category_id && data.category_id !== "none"
              ? data.category_id
              : null,
          description: data.description,
          amount:
            data.type === "expense"
              ? -Math.abs(data.amount)
              : Math.abs(data.amount),
          currency: data.currency,
          type: data.type,
          date: data.date.toISOString().split("T")[0],
        })
        .eq("id", data.id)
        .eq("family_group_id", groupId)
        .select()
        .single();

      if (error) throw error;
      return result;
    },
    onSuccess: () => {
      toast.success("Transaction updated successfully!");
      setIsFormOpen(false);
      setEditingTransaction(null);
      reset();
      queryClient.invalidateQueries({
        queryKey: ["familyGroupTransactions", groupId],
      });
      queryClient.invalidateQueries({
        queryKey: ["familyGroupAccounts", groupId],
      });
      queryClient.invalidateQueries({
        queryKey: ["familyGroupFinancialSummary", groupId],
      });
    },
    onError: (error: Error) => {
      toast.error(`Failed to update transaction: ${error.message}`);
    },
  });

  // Delete transaction mutation
  const deleteTransactionMutation = useMutation({
    mutationFn: async (transactionId: string) => {
      const { error } = await supabase
        .from("transactions")
        .delete()
        .eq("id", transactionId)
        .eq("family_group_id", groupId);

      if (error) throw error;
    },
    onSuccess: () => {
      toast.success("Transaction deleted successfully!");
      queryClient.invalidateQueries({
        queryKey: ["familyGroupTransactions", groupId],
      });
      queryClient.invalidateQueries({
        queryKey: ["familyGroupAccounts", groupId],
      });
      queryClient.invalidateQueries({
        queryKey: ["familyGroupFinancialSummary", groupId],
      });
    },
    onError: (error: Error) => {
      toast.error(`Failed to delete transaction: ${error.message}`);
    },
  });

  const onSubmit: SubmitHandler<TransactionFormData> = useCallback(
    (data) => {
      if (editingTransaction) {
        updateTransactionMutation.mutate({
          ...data,
          id: editingTransaction.id,
        });
      } else {
        createTransactionMutation.mutate(data);
      }
    },
    [editingTransaction, createTransactionMutation, updateTransactionMutation]
  );

  const handleEdit = useCallback(
    (transaction: FamilyGroupTransaction) => {
      setEditingTransaction(transaction);
      reset({
        description: transaction.description || "",
        amount: Math.abs(transaction.amount),
        type: transaction.type as "income" | "expense",
        date: new Date(transaction.date),
        category_id: transaction.category_id || "none",
        account_id: transaction.account_id,
        currency: defaultCurrency,
      });
      setIsFormOpen(true);
    },
    [reset, defaultCurrency]
  );

  const handleDelete = useCallback(
    (transactionId: string) => {
      if (confirm("Are you sure you want to delete this transaction?")) {
        deleteTransactionMutation.mutate(transactionId);
      }
    },
    [deleteTransactionMutation]
  );

  const openCreateDialog = useCallback(() => {
    setEditingTransaction(null);
    reset({
      description: "",
      amount: 0,
      type: "expense",
      date: new Date(),
      category_id: "none",
      account_id: "",
      currency: defaultCurrency,
    });
    setIsFormOpen(true);
  }, [reset, defaultCurrency]);

  if (isLoading) {
    return (
      <div className="container mx-auto p-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading transactions...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 py-8 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center gap-2 mb-2 text-sm text-muted-foreground">
            <Link
              href="/dashboard/family"
              className="hover:text-primary transition-colors flex items-center gap-1"
            >
              <Home className="h-4 w-4" />
              Family Finance
            </Link>
            <span>/</span>
            <Link
              href={`/dashboard/family/${groupId}`}
              className="hover:text-primary transition-colors"
            >
              Group Dashboard
            </Link>
            <span>/</span>
            <span className="text-foreground font-medium">Transactions</span>
          </div>
          <h1 className="text-3xl font-bold tracking-tight">
            Family Transactions
          </h1>
          <p className="text-muted-foreground">
            View and manage all transactions for your family group
          </p>
        </div>

        <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
          <DialogTrigger asChild>
            <Button onClick={openCreateDialog}>
              <PlusCircle className="mr-2 h-4 w-4" />
              Add Transaction
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>
                {editingTransaction
                  ? "Edit Transaction"
                  : "Add New Transaction"}
              </DialogTitle>
              <DialogDescription>
                {editingTransaction
                  ? "Update the transaction details below."
                  : "Enter the details for your new transaction."}
              </DialogDescription>
            </DialogHeader>

            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              <div>
                <Label htmlFor="description">Description</Label>
                <Controller
                  name="description"
                  control={control}
                  render={({ field }) => (
                    <Input
                      id="description"
                      placeholder="Enter transaction description"
                      {...field}
                    />
                  )}
                />
                {errors.description && (
                  <p className="text-sm text-red-500 mt-1">
                    {errors.description.message}
                  </p>
                )}
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="amount">Amount</Label>
                  <Controller
                    name="amount"
                    control={control}
                    render={({ field }) => (
                      <Input
                        id="amount"
                        type="number"
                        step="0.01"
                        placeholder="0.00"
                        {...field}
                      />
                    )}
                  />
                  {errors.amount && (
                    <p className="text-sm text-red-500 mt-1">
                      {errors.amount.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label htmlFor="type">Type</Label>
                  <Controller
                    name="type"
                    control={control}
                    render={({ field }) => (
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="income">Income</SelectItem>
                          <SelectItem value="expense">Expense</SelectItem>
                        </SelectContent>
                      </Select>
                    )}
                  />
                  {errors.type && (
                    <p className="text-sm text-red-500 mt-1">
                      {errors.type.message}
                    </p>
                  )}
                </div>
              </div>

              <div>
                <Label htmlFor="date">Date</Label>
                <Controller
                  name="date"
                  control={control}
                  render={({ field }) => (
                    <DatePicker date={field.value} setDate={field.onChange} />
                  )}
                />
                {errors.date && (
                  <p className="text-sm text-red-500 mt-1">
                    {errors.date.message}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="account_id">Account</Label>
                <Controller
                  name="account_id"
                  control={control}
                  render={({ field }) => (
                    <Select onValueChange={field.onChange} value={field.value}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select account" />
                      </SelectTrigger>
                      <SelectContent>
                        {accounts.map((account) => (
                          <SelectItem key={account.id} value={account.id}>
                            {account.name} ({account.type})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
                {errors.account_id && (
                  <p className="text-sm text-red-500 mt-1">
                    {errors.account_id.message}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="category_id">Category (Optional)</Label>
                <Controller
                  name="category_id"
                  control={control}
                  render={({ field }) => (
                    <Select onValueChange={field.onChange} value={field.value}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">No Category</SelectItem>
                        {categories
                          .filter((cat) => cat.type === watch("type"))
                          .map((category) => (
                            <SelectItem key={category.id} value={category.id}>
                              <div className="flex items-center space-x-2">
                                <CategoryIcon
                                  icon={category.icon}
                                  className="h-4 w-4"
                                />
                                <span>{category.name}</span>
                              </div>
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                  )}
                />
              </div>

              <DialogFooter>
                <DialogClose asChild>
                  <Button type="button" variant="outline">
                    Cancel
                  </Button>
                </DialogClose>
                <Button
                  type="submit"
                  disabled={
                    createTransactionMutation.isPending ||
                    updateTransactionMutation.isPending
                  }
                >
                  {createTransactionMutation.isPending ||
                  updateTransactionMutation.isPending
                    ? "Saving..."
                    : editingTransaction
                    ? "Update"
                    : "Create"}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Transactions Table */}
      <Card>
        <CardHeader>
          <CardTitle>All Transactions</CardTitle>
        </CardHeader>
        <CardContent>
          {transactions && transactions.length > 0 ? (
            <ScrollArea className="h-[600px]">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Account</TableHead>
                    <TableHead>Created By</TableHead>
                    <TableHead className="text-right">Amount</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {transactions.map((transaction) => (
                    <TableRow key={transaction.id}>
                      <TableCell>
                        {new Date(transaction.date).toLocaleDateString()}
                      </TableCell>
                      <TableCell className="font-medium">
                        {transaction.description}
                      </TableCell>
                      <TableCell>
                        {transaction.category_name || "Uncategorized"}
                      </TableCell>
                      <TableCell>{transaction.account_name}</TableCell>
                      <TableCell>{transaction.created_by_email}</TableCell>
                      <TableCell
                        className={`text-right font-semibold ${
                          transaction.type === "income"
                            ? "text-green-600"
                            : "text-red-600"
                        }`}
                      >
                        {transaction.type === "income" ? "+" : "-"}
                        {formatCurrency(
                          Math.abs(transaction.amount)
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEdit(transaction)}
                            disabled={
                              transaction.created_by_user_id !== currentUserId
                            }
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDelete(transaction.id)}
                            disabled={
                              transaction.created_by_user_id !== currentUserId
                            }
                            className="hover:text-red-600"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </ScrollArea>
          ) : (
            <div className="text-center py-8">
              <DollarSign className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                No transactions yet
              </h3>
              <p className="text-muted-foreground mb-4">
                Start by adding your first transaction to track your
                family&apos;s finances.
              </p>
              <Button onClick={openCreateDialog}>
                <PlusCircle className="mr-2 h-4 w-4" />
                Add First Transaction
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
