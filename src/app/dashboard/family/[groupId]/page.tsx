"use client";

import React, { useState, useEffect, useMemo } from "react";
import { useParams } from "next/navigation";
import { useQuery } from "@tanstack/react-query";
import { getFamilyGroupDetails } from "@/features/family-groups/services/family-groups";
import {
  type FamilyGroupDetails,
  type FamilyGroupMember,
} from "@/shared/types/family";
import {
  getFamilyGroupFinancialSummary,
  getFamilyGroupTransactions,
  getFamilyGroupAccounts,
  getFamilyGroupBudgets,
  getFamilyGroupGoals,
  type FamilyGroupFinancialSummary,
  type FamilyGroupTransaction,
  type FamilyGroupAccount,
  type FamilyGroupBudget,
  type FamilyGroupGoal,
} from "@/features/family-groups/services/family-finances";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/shared/components/ui/card";
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from "@/shared/components/ui/alert";
import { But<PERSON> } from "@/shared/components/ui/button";
import { Badge } from "@/shared/components/ui/badge";
import { Progress } from "@/shared/components/ui/progress";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/shared/components/ui/collapsible";
import {
  Loader2,
  Users,
  AlertCircle,
  Wallet,
  DollarSign,
  PiggyBank,
  Building2,
  CreditCard,
  Target,
  Home,
  ArrowRight,
  Banknote,
  ArrowUpRight,
  Settings,
  UserPlus,
} from "lucide-react";
import { format } from "date-fns";
import { createClient } from "@/shared/services/supabase/client";
import Link from "next/link";
import {
  Table,
  TableBody,
  TableCell,
  TableRow,
} from "@/shared/components/ui/table";
import { useCurrencyFormatter } from "@/shared/hooks/useCurrencyFormatter";

const supabase = createClient();

export default function FamilyGroupDashboard() {
  const params = useParams();
  const groupId = params.groupId as string;
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);
  const [showMemberManagement, setShowMemberManagement] = useState(false);
  const { formatCurrency } = useCurrencyFormatter();

  useEffect(() => {
    const fetchUser = async () => {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      setCurrentUserId(user?.id || null);
    };
    fetchUser();
  }, []);

  // Fetch group details
  const {
    data: groupDetails,
    isLoading: groupLoading,
    error: groupError,
  } = useQuery<FamilyGroupDetails | null, Error>({
    queryKey: ["familyGroupDetails", groupId],
    queryFn: () => getFamilyGroupDetails(groupId),
    enabled: !!groupId,
  });

  // Fetch financial summary
  const { data: summary, isLoading: summaryLoading } =
    useQuery<FamilyGroupFinancialSummary>({
      queryKey: ["familyGroupFinancialSummary", groupId],
      queryFn: () => getFamilyGroupFinancialSummary(groupId),
      enabled: !!groupId,
    });

  // Fetch accounts
  const { data: accounts } = useQuery<FamilyGroupAccount[]>({
    queryKey: ["familyGroupAccounts", groupId],
    queryFn: () => getFamilyGroupAccounts(groupId),
    enabled: !!groupId,
  });

  // Fetch recent transactions
  const { data: transactions } = useQuery<FamilyGroupTransaction[]>({
    queryKey: ["familyGroupTransactions", groupId],
    queryFn: () => getFamilyGroupTransactions(groupId, 10, 0),
    enabled: !!groupId,
  });

  // Fetch budgets
  useQuery<FamilyGroupBudget[]>({
    queryKey: ["familyGroupBudgets", groupId],
    queryFn: () => getFamilyGroupBudgets(groupId),
    enabled: !!groupId,
  });

  // Fetch goals
  const { data: goals } = useQuery<FamilyGroupGoal[]>({
    queryKey: ["familyGroupGoals", groupId],
    queryFn: () => getFamilyGroupGoals(groupId),
    enabled: !!groupId,
  });

  const getAccountTypeIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case "checking":
      case "savings":
        return <Building2 className="h-6 w-6 text-blue-600" />;
      case "credit card":
        return <CreditCard className="h-6 w-6 text-purple-600" />;
      case "cash":
        return <Banknote className="h-6 w-6 text-green-600" />;
      default:
        return <Wallet className="h-6 w-6 text-gray-600" />;
    }
  };

  // Calculate additional metrics similar to personal dashboard
  const additionalMetrics = useMemo(() => {
    if (!summary) return null;

    const netCashFlow = summary.monthly_income - summary.monthly_expenses;
    const incomeToExpenseRatio =
      summary.monthly_expenses > 0
        ? summary.monthly_income / summary.monthly_expenses
        : 0;

    return {
      netCashFlow,
      incomeToExpenseRatio,
    };
  }, [summary]);

  // Transform goals data for display
  const financialGoalsPreview = useMemo(() => {
    if (!goals) return [];

    return goals.slice(0, 3).map((goal) => {
      const progress =
        goal.target_amount > 0
          ? (goal.current_amount / goal.target_amount) * 100
          : 0;

      const daysRemaining = goal.target_date
        ? Math.ceil(
            (new Date(goal.target_date).getTime() - new Date().getTime()) /
              (1000 * 60 * 60 * 24)
          )
        : 0;

      const monthlyRequired =
        goal.target_date && daysRemaining > 0
          ? Math.max(
              0,
              (goal.target_amount - goal.current_amount) / (daysRemaining / 30)
            )
          : 0;

      return {
        ...goal,
        progress,
        daysRemaining,
        monthlyRequired,
      };
    });
  }, [goals]);

  if (groupLoading || summaryLoading || !currentUserId) {
    return (
      <div className="flex flex-col justify-center items-center h-screen">
        <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
        <p className="text-lg text-muted-foreground">
          Loading family finance dashboard...
        </p>
      </div>
    );
  }

  if (groupError || !groupDetails) {
    const errorMessage =
      groupError?.message ||
      "Group not found or you do not have permission to view it.";
    return (
      <div className="container mx-auto p-4 py-8 flex justify-center items-center min-h-[calc(100vh-10rem)]">
        <Alert variant="destructive" className="max-w-lg w-full">
          <AlertCircle className="h-5 w-5" />
          <AlertTitle>Error Loading Group</AlertTitle>
          <AlertDescription>
            {errorMessage}
            <br />
            Please check the group ID or ensure you are a member of this group.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <div className="flex items-center gap-2 mb-4 text-sm text-muted-foreground">
          <Link
            href="/dashboard/family"
            className="hover:text-primary transition-colors flex items-center gap-1"
          >
            <Home className="h-4 w-4" />
            Family Finance
          </Link>
          <span>/</span>
          <span className="text-foreground font-medium">
            {groupDetails.group_name}
          </span>
        </div>

        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="flex-1">
            <h1 className="text-3xl font-bold tracking-tight">
              {groupDetails.group_name}
            </h1>
            <p className="text-muted-foreground">
              Collaborative financial management for your family group
            </p>
          </div>

          <div className="flex items-center gap-3">
            <Collapsible
              open={showMemberManagement}
              onOpenChange={setShowMemberManagement}
            >
              <CollapsibleTrigger asChild>
                <Button variant="outline" size="sm">
                  <Users className="h-4 w-4 mr-2" />
                  Members ({groupDetails.members.length})
                </Button>
              </CollapsibleTrigger>
            </Collapsible>
            <Button variant="outline" size="sm" asChild>
              <Link href={`/dashboard/family/${groupId}/settings`}>
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </Link>
            </Button>
          </div>
        </div>

        {/* Member Management Collapsible */}
        <Collapsible
          open={showMemberManagement}
          onOpenChange={setShowMemberManagement}
        >
          <CollapsibleContent className="space-y-4 mt-4">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center text-lg">
                    <Users className="h-5 w-5 mr-2" />
                    Active Members ({groupDetails.members.length})
                  </CardTitle>
                  <Button variant="outline" size="sm" asChild>
                    <Link
                      href={`/dashboard/family/${groupId}/settings?tab=members`}
                    >
                      <UserPlus className="h-4 w-4 mr-2" />
                      Invite Members
                    </Link>
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {groupDetails.members.map((member: FamilyGroupMember) => (
                    <div
                      key={member.user_id}
                      className="flex items-center justify-between p-3 bg-muted/50 rounded-lg"
                    >
                      <div>
                        <p className="font-medium">{member.email}</p>
                        <p className="text-xs text-muted-foreground">
                          Joined{" "}
                          {format(new Date(member.joined_at), "MMM d, yyyy")}
                        </p>
                      </div>
                      <Badge
                        variant={
                          member.role === "admin" ? "default" : "secondary"
                        }
                      >
                        {member.role}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </CollapsibleContent>
        </Collapsible>
      </div>

      {/* Stats Grid - Match Personal Dashboard */}
      {summary && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card className="border-l-4 border-green-500 overflow-hidden relative group hover:shadow-lg transition-shadow duration-300">
            <div className="absolute top-0 right-0 w-16 h-16 bg-green-500/10 rounded-bl-full"></div>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Balance
              </CardTitle>
              <Wallet className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(summary.total_balance)}
              </div>
              <p className="text-xs text-muted-foreground mt-2">
                Across {summary.accounts_count} accounts
              </p>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-blue-500 overflow-hidden relative group hover:shadow-lg transition-shadow duration-300">
            <div className="absolute top-0 right-0 w-16 h-16 bg-blue-500/10 rounded-bl-full"></div>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Monthly Income
              </CardTitle>
              <DollarSign className="h-4 w-4 text-blue-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(summary.monthly_income)}
              </div>
              <p className="text-xs text-muted-foreground mt-2">This month</p>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-red-500 overflow-hidden relative group hover:shadow-lg transition-shadow duration-300">
            <div className="absolute top-0 right-0 w-16 h-16 bg-red-500/10 rounded-bl-full"></div>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Monthly Expenses
              </CardTitle>
              <DollarSign className="h-4 w-4 text-red-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(summary.monthly_expenses)}
              </div>
              <p className="text-xs text-muted-foreground mt-2">This month</p>
            </CardContent>
          </Card>

          <Card className="border-l-4 border-purple-500 overflow-hidden relative group hover:shadow-lg transition-shadow duration-300">
            <div className="absolute top-0 right-0 w-16 h-16 bg-purple-500/10 rounded-bl-full"></div>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Savings Rate
              </CardTitle>
              <PiggyBank className="h-4 w-4 text-purple-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {summary.monthly_income > 0
                  ? `${(
                      ((summary.monthly_income - summary.monthly_expenses) /
                        summary.monthly_income) *
                      100
                    ).toFixed(1)}%`
                  : "0%"}
              </div>
              <p className="text-xs text-muted-foreground mt-2">
                of income saved this month
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Additional Financial Metrics - Match Personal Dashboard */}
      {additionalMetrics && summary && (
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Additional Metrics</CardTitle>
              <CardDescription>Financial health indicators</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableBody>
                  <TableRow>
                    <TableCell className="font-medium">Net Cash Flow</TableCell>
                    <TableCell className="text-right">
                      <span
                        className={
                          additionalMetrics.netCashFlow >= 0
                            ? "text-green-500"
                            : "text-red-500"
                        }
                      >
                        {formatCurrency(additionalMetrics.netCashFlow)}
                      </span>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">
                      Income to Expense Ratio
                    </TableCell>
                    <TableCell className="text-right">
                      {additionalMetrics.incomeToExpenseRatio.toFixed(2)} : 1
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          {/* Goals & Budgets Card */}
          <Card>
            <CardHeader>
              <CardTitle>Goals & Budgets</CardTitle>
              <CardDescription>
                Track your active financial plans
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center">
                  <Target className="h-6 w-6 mr-3 text-primary" />
                  <div>
                    <p className="text-sm font-medium">
                      Active Financial Goals
                    </p>
                    <p className="text-xs text-muted-foreground">
                      Your current aspirations
                    </p>
                  </div>
                </div>
                <p className="text-2xl font-semibold">{summary.active_goals}</p>
              </div>
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center">
                  <Target className="h-6 w-6 mr-3 text-primary" />
                  <div>
                    <p className="text-sm font-medium">Active Budgets</p>
                    <p className="text-xs text-muted-foreground">
                      Your current spending plans
                    </p>
                  </div>
                </div>
                <p className="text-2xl font-semibold">
                  {summary.active_budgets}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Overview Content */}
      <div className="space-y-6">
        <div className="grid gap-6 md:grid-cols-2">
          {/* Financial Goals Card */}
          <Card>
            <CardHeader>
              <CardTitle>Financial Goals</CardTitle>
              <CardDescription>Your top financial aspirations</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {financialGoalsPreview && financialGoalsPreview.length > 0 ? (
                financialGoalsPreview.map((goal) => (
                  <div key={goal.id} className="p-3 border rounded-lg">
                    <div className="flex justify-between items-center mb-1">
                      <h4 className="text-sm font-medium flex items-center">
                        {goal.icon && <span className="mr-2">{goal.icon}</span>}
                        {goal.name}
                      </h4>
                      <span className="text-xs text-muted-foreground">
                        {goal.daysRemaining > 0
                          ? `${goal.daysRemaining} days left`
                          : "Target date passed"}
                      </span>
                    </div>
                    <Progress value={goal.progress} className="h-2 mb-1" />
                    <div className="flex justify-between text-xs">
                      <span>{formatCurrency(goal.current_amount)}</span>
                      <span className="text-muted-foreground">
                        Target: {formatCurrency(goal.target_amount)}
                      </span>
                    </div>
                    {goal.monthlyRequired > 0 && (
                      <p className="text-xs text-muted-foreground mt-1">
                        Save ~{formatCurrency(goal.monthlyRequired)}
                        /month to reach by{" "}
                        {goal.target_date
                          ? new Date(goal.target_date).toLocaleDateString()
                          : "No date set"}
                      </p>
                    )}
                  </div>
                ))
              ) : (
                <div className="flex items-center justify-center h-full min-h-[100px]">
                  <p className="text-muted-foreground">
                    No active goals to display.
                  </p>
                </div>
              )}
              {financialGoalsPreview && financialGoalsPreview.length > 0 && (
                <Link
                  href={`/dashboard/family/${groupId}/goals`}
                  className="text-sm text-primary hover:underline flex items-center mt-2"
                >
                  View All Goals <ArrowRight className="ml-1 h-4 w-4" />
                </Link>
              )}
            </CardContent>
          </Card>

          {/* Accounts Card */}
          <Card>
            <CardHeader>
              <CardTitle>Accounts</CardTitle>
              <CardDescription>Manage your money</CardDescription>
            </CardHeader>
            <CardContent>
              {accounts && accounts.length > 0 ? (
                <div className="space-y-4">
                  {accounts.slice(0, 5).map((account) => (
                    <div
                      key={account.id}
                      className="flex items-center justify-between border-b pb-2"
                    >
                      <div className="flex items-center">
                        {getAccountTypeIcon(account.type)}
                        <div className="ml-3">
                          <p className="font-medium">{account.name}</p>
                          <p className="text-sm text-muted-foreground capitalize">
                            {account.type} • {account.created_by_email}
                          </p>
                        </div>
                      </div>
                      <p
                        className={
                          account.balance >= 0
                            ? "text-green-500"
                            : "text-red-500"
                        }
                      >
                        {formatCurrency(account.balance)}
                      </p>
                    </div>
                  ))}
                  <Link
                    href={`/dashboard/family/${groupId}/accounts`}
                    className="text-sm text-primary hover:underline flex items-center mt-2"
                  >
                    View All Accounts <ArrowRight className="ml-1 h-4 w-4" />
                  </Link>
                </div>
              ) : (
                <div className="text-center py-4 text-muted-foreground">
                  No accounts added yet
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Recent Transactions Card */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>Your latest financial activities</CardDescription>
            <Button variant="ghost" size="sm" asChild>
              <Link href={`/dashboard/family/${groupId}/transactions`}>
                View All <ArrowUpRight className="ml-1 h-4 w-4" />
              </Link>
            </Button>
          </CardHeader>
          <CardContent>
            {transactions && transactions.length > 0 ? (
              <Table>
                <TableBody>
                  {transactions.map((transaction) => (
                    <TableRow key={transaction.id}>
                      <TableCell>
                        <div className="font-medium">
                          {transaction.description}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {new Date(transaction.date).toLocaleDateString(
                            undefined,
                            {
                              day: "numeric",
                              month: "short",
                              year: "numeric",
                            }
                          )}{" "}
                          • {transaction.created_by_email}
                        </div>
                      </TableCell>
                      <TableCell
                        className={`text-right font-semibold ${
                          transaction.type === "income"
                            ? "text-green-500"
                            : "text-red-500"
                        }`}
                      >
                        {transaction.type === "income" ? "+" : "-"}
                        {formatCurrency(Math.abs(transaction.amount || 0))}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <div className="flex items-center justify-center h-40">
                <p className="text-muted-foreground">No recent transactions.</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
