"use client";

import { useState, useEffect, useCallback } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/shared/components/ui/card";
import { <PERSON><PERSON> } from "@/shared/components/ui/button";
import { Progress } from "@/shared/components/ui/progress";
import { Badge } from "@/shared/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/shared/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/shared/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/shared/components/ui/select";
import { Input } from "@/shared/components/ui/input";
import { useForm, SubmitHandler } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Plus,
  Wallet,
  CreditCard,
  DollarSign,
  Edit,
  Trash2,
  AlertCircle,
  CheckCircle2,
  TrendingUp,
  Bell,
  AlertTriangle,
  Target,
} from "lucide-react";
import { createClient } from "@/shared/services/supabase/client";
import { useUserSettings } from "@/shared/contexts/UserSettingsContext";
import { useAppMode } from "@/shared/contexts/AppModeContext";
import { useCurrencyFormatter } from "@/shared/hooks/useCurrencyFormatter";
import { toast } from "sonner";
import { parseISO, isValid } from "date-fns";

// Define form schema
const budgetFormSchema = z.object({
  name: z.string().min(1, "Budget name is required"),
  amount: z
    .string()
    .refine((val) => !isNaN(parseFloat(val)) && parseFloat(val) > 0, {
      message: "Budget amount must be a valid number greater than 0",
    }),
  period: z.enum(["weekly", "monthly", "quarterly", "yearly", "one-time"]),
  category_id: z.string().optional(),
  currency: z.string().min(1, "Currency is required"),
  start_date: z.string().min(1, "Start date is required"),
  end_date: z.string().optional(),
});

// Infer type from schema
type BudgetFormValues = z.infer<typeof budgetFormSchema>;

// Import Budget and BudgetPeriod from the centralized supabase types
import {
  Budget,
  BudgetPeriod,
  Category as SupabaseCategory,
} from "@/shared/types";

// Predefined colors for categories
const CATEGORY_COLORS = [
  "#0ea5e9", // Sky
  "#10b981", // Emerald
  "#f59e0b", // Amber
  "#ef4444", // Red
  "#8b5cf6", // Violet
  "#ec4899", // Pink
  "#f97316", // Orange
  "#14b8a6", // Teal
  "#6366f1", // Indigo
  "#84cc16", // Lime
];

export default function BudgetsPage() {
  const [budgets, setBudgets] = useState<Budget[]>([]);
  const [categories, setCategories] = useState<SupabaseCategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingBudget, setEditingBudget] = useState<Budget | null>(null);
  const {
    settings: { currency: defaultCurrency },
  } = useUserSettings();
  const { isPersonalMode } = useAppMode();
  const { formatCurrency } = useCurrencyFormatter();

  const form = useForm({
    resolver: zodResolver(budgetFormSchema),
    defaultValues: {
      name: "",
      amount: "0",
      period: "monthly",
      category_id: undefined,
      currency: defaultCurrency,
      start_date: new Date().toISOString().split("T")[0],
      end_date: "",
    },
  });

  const fetchBudgets = useCallback(async () => {
    try {
      setLoading(true);
      const supabase = createClient();
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) return;

      const { data, error } = await supabase
        .from("budgets")
        .select("*, categories(name, color)") // categories(name, color) is joined
        .eq("user_id", user.id);

      if (error) throw error;

      // Assuming 'data' items largely conform to the imported Budget type + a nested 'categories' object from the join
      // The 'currency', 'category_id', 'end_date', and 'categories.color' fields from Supabase might be null, so we type them accordingly here.
      const enrichedBudgets = data.map(
        (budget: {
          id: string;
          name: string;
          amount: number;
          period: string;
          currency: string | null;
          user_id: string;
          created_at: string;
          updated_at?: string;
          category_id?: string | null;
          start_date: string;
          end_date?: string | null;
          spent?: number;
          categories: { name: string; color: string | null } | null;
        }) => {
          const spent = budget.spent || 0;
          const remaining = budget.amount - spent;
          const progress =
            budget.amount > 0 ? (spent / budget.amount) * 100 : 0;
          return {
            ...budget,
            period: budget.period as BudgetPeriod,
            currency: budget.currency || defaultCurrency,
            category_name: budget.categories?.name,
            category_color: budget.categories?.color,
            spent,
            remaining,
            progress,
          };
        }
      );

      setBudgets(enrichedBudgets as Budget[]);
    } catch (error) {
      console.error("Error fetching budgets:", error);
      toast.error("Failed to load budgets");
    } finally {
      setLoading(false);
    }
  }, [defaultCurrency]);

  const fetchCategories = useCallback(async () => {
    try {
      const supabase = createClient();
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        console.warn("No user found, cannot fetch categories (budgets page).");
        toast.info("Please log in to see categories.");
        setCategories([]);
        return;
      }

      const { data, error } = await supabase
        .from("categories")
        .select("*")
        .eq("user_id", user.id)
        .eq("type", "expense"); // Only expense categories for budgets

      if (error) {
        console.error(
          "Supabase error fetching categories (budgets page):",
          error
        );
        toast.error("Failed to load categories for budget form.");
        setCategories([]); // Clear categories on error
        return; // Stop execution if there's an error
      }

      if (!data) {
        console.warn(
          "No categories data array returned from Supabase (budgets page)."
        );
        setCategories([]);
        return;
      }

      if (data.length === 0) {
        console.info(
          "No expense categories found for this user (budgets page)."
        );
      } else {
        console.log("Fetched categories for budget form (budgets page):", data);
      }

      // Cast the data to our Category interface
      setCategories(
        (data || []).map((category) => ({
          ...category,
          type: category.type as "income" | "expense",
          color: category.color || CATEGORY_COLORS[0],
          icon: category.icon || undefined,
          parent_id: category.parent_id || undefined,
        }))
      );
    } catch (error) {
      console.error(
        "An unexpected error occurred while fetching categories:",
        error
      );
      toast.error("An unexpected error occurred while fetching categories.");
    }
  }, []);

  useEffect(() => {
    fetchBudgets();
    fetchCategories();
  }, [fetchBudgets, fetchCategories]);

  const onSubmit: SubmitHandler<BudgetFormValues> = async (values) => {
    try {
      const supabase = createClient();
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        toast.error("Please login to create budgets");
        return;
      }

      const budgetData = {
        user_id: user.id,
        name: values.name,
        amount: parseFloat(values.amount),
        period: values.period as BudgetPeriod,
        category_id: values.category_id || null,
        currency: values.currency || defaultCurrency,
        // Convert to Date object (if not already) and then to ISO string YYYY-MM-DD
        start_date: new Date(values.start_date).toISOString().split("T")[0],
        end_date: values.end_date
          ? new Date(values.end_date).toISOString().split("T")[0]
          : null,
      };

      if (editingBudget) {
        // Update existing budget
        const { error } = await supabase
          .from("budgets")
          .update(budgetData) // budgetData already has dates formatted as ISO strings
          .eq("id", editingBudget.id);

        if (error) throw error;
        toast.success("Budget updated successfully");
      } else {
        const { error } = await supabase.from("budgets").insert([budgetData]);

        if (error) throw error;
        toast.success("Budget created successfully");
      }

      setDialogOpen(false);
      form.reset();
      setEditingBudget(null);
      fetchBudgets();
    } catch (error) {
      console.error("Error saving budget:", error);
      toast.error("Failed to save budget");
    }
  };

  const handleEdit = (budget: Budget) => {
    const defaultValuesForForm: Partial<BudgetFormValues> = {
      name: budget.name,
      amount: budget.amount.toString(), // Form expects string for amount
      period: budget.period, // Should now correctly map from updated Supabase BudgetPeriod to form's Zod enum
      category_id: budget.category_id || "",
      currency: budget.currency || defaultCurrency,
      start_date:
        budget.start_date && isValid(parseISO(budget.start_date))
          ? budget.start_date
          : new Date().toISOString().split("T")[0],
      end_date:
        budget.end_date && isValid(parseISO(budget.end_date))
          ? budget.end_date
          : undefined,
    };
    form.reset(defaultValuesForForm);
    // setSelectedAccount(accounts.find(acc => acc.id === editingBudget?.account_id) || null); // Commented out
    setEditingBudget(budget);
    setDialogOpen(true);
  };

  const handleDelete = async (budgetId: string) => {
    if (!confirm("Are you sure you want to delete this budget?")) return;

    try {
      const supabase = createClient();
      const { error } = await supabase
        .from("budgets")
        .delete()
        .eq("id", budgetId);

      if (error) throw error;
      toast.success("Budget deleted successfully");
      fetchBudgets();
    } catch (error) {
      console.error("Error deleting budget:", error);
      toast.error("Failed to delete budget");
    }
  };


  const formatPeriod = (period: string) => {
    return period.charAt(0).toUpperCase() + period.slice(1);
  };

  // Budget alert system
  const getBudgetAlerts = (budget: Budget) => {
    const spent = budget.spent || 0;
    const percentage = budget.amount > 0 ? (spent / budget.amount) * 100 : 0;
    
    if (spent > budget.amount) {
      return {
        type: 'over' as const,
        message: `Over budget by ${formatCurrency(spent - budget.amount)}`,
        icon: AlertTriangle,
        color: 'text-red-600',
        bgColor: 'bg-red-50 border-red-200',
      };
    } else if (percentage >= 90) {
      return {
        type: 'warning' as const,
        message: `Near budget limit (${percentage.toFixed(0)}% used)`,
        icon: AlertCircle,
        color: 'text-amber-600',
        bgColor: 'bg-amber-50 border-amber-200',
      };
    } else if (percentage >= 75) {
      return {
        type: 'caution' as const,
        message: `Approaching budget limit (${percentage.toFixed(0)}% used)`,
        icon: Bell,
        color: 'text-blue-600',
        bgColor: 'bg-blue-50 border-blue-200',
      };
    }
    return null;
  };

  // For now, just show all budgets since account linkage isn't implemented
  const activeBudgets = budgets;

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Use activeBudgets instead of budgets for display
  // For now, we'll use budgets directly as the linkage isn't clear in the code
  const budgetsToDisplay = activeBudgets;

  const totalBudgeted = budgetsToDisplay.reduce(
    (sum, budget) => sum + budget.amount,
    0
  );
  const totalSpent = budgetsToDisplay.reduce(
    (sum, budget) => sum + (budget.spent || 0),
    0
  );
  const totalRemaining = budgetsToDisplay.reduce(
    (sum, budget) => sum + (budget.remaining || 0),
    0
  );
  const onTrackCount = budgetsToDisplay.filter(
    (budget) => {
      const spent = budget.spent || 0;
      const percentage = budget.amount > 0 ? (spent / budget.amount) * 100 : 0;
      return spent <= budget.amount && percentage < 75;
    }
  ).length;
  
  // Get budget alerts for the alerts section
  const budgetAlerts = budgetsToDisplay
    .map(budget => {
      const alert = getBudgetAlerts(budget);
      return alert ? { budget, alert } : null;
    })
    .filter((item): item is { budget: Budget; alert: NonNullable<ReturnType<typeof getBudgetAlerts>> } => item !== null)
    .sort((a, b) => {
      // Sort by severity: over > warning > caution
      const severity = { over: 3, warning: 2, caution: 1 };
      return severity[b.alert.type] - severity[a.alert.type];
    });

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {isPersonalMode ? "Personal Budgets" : "Family Budgets"}
          </h1>
          <p className="text-muted-foreground">
            {isPersonalMode 
              ? "Track your personal spending and manage your budget with smart alerts"
              : "Manage family group budgets and shared financial goals"
            }
          </p>
        </div>
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              New Budget
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>
                {editingBudget ? "Edit Budget" : "Create New Budget"}
              </DialogTitle>
              <DialogDescription>
                Set a spending limit for a specific category or overall
              </DialogDescription>
            </DialogHeader>
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-4"
              >
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Budget Name</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="e.g., Groceries Budget"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="amount"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Budget Amount</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          placeholder="0.00"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="currency"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Currency</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select currency" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="USD">USD ($)</SelectItem>
                          <SelectItem value="EUR">EUR (€)</SelectItem>
                          <SelectItem value="GBP">GBP (£)</SelectItem>
                          <SelectItem value="JPY">JPY (¥)</SelectItem>
                          <SelectItem value="CAD">CAD ($)</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="period"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Budget Period</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select period" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="weekly">Weekly</SelectItem>
                          <SelectItem value="monthly">Monthly</SelectItem>
                          <SelectItem value="quarterly">Quarterly</SelectItem>
                          <SelectItem value="yearly">Yearly</SelectItem>
                          <SelectItem value="one-time">One-time</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="category_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Category (Optional)</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="All Categories" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {categories.map((category) => (
                            <SelectItem key={category.id} value={category.id}>
                              {category.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Leave blank to track all spending
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="start_date"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Start Date</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="end_date"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>End Date (Optional)</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormDescription>
                        Leave blank for recurring budgets
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <DialogFooter>
                  <Button type="submit">
                    {editingBudget ? "Update" : "Create"} Budget
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Budget Alerts */}
      {budgetAlerts.length > 0 && (
        <div className="space-y-3">
          <h2 className="text-lg font-semibold flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Budget Alerts
          </h2>
          <div className="space-y-2">
            {budgetAlerts.slice(0, 3).map(({ budget, alert }) => {
              const IconComponent = alert.icon;
              return (
                <Card key={budget.id} className={`${alert.bgColor} border-l-4`}>
                  <CardContent className="py-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <IconComponent className={`h-4 w-4 ${alert.color}`} />
                        <div>
                          <p className="font-medium">{budget.name}</p>
                          <p className={`text-sm ${alert.color}`}>{alert.message}</p>
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEdit(budget)}
                        className="h-8"
                      >
                        <Edit className="h-3 w-3 mr-1" />
                        Edit
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      )}

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="border-l-4 border-blue-500 overflow-hidden relative group hover:shadow-lg transition-shadow duration-300">
          <div className="absolute top-0 right-0 w-16 h-16 bg-blue-500/10 rounded-bl-full"></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Budgeted
            </CardTitle>
            <Wallet className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-500">
              {formatCurrency(totalBudgeted)}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Across {budgetsToDisplay.length} budgets
            </p>
          </CardContent>
        </Card>
        <Card className="border-l-4 border-orange-500 overflow-hidden relative group hover:shadow-lg transition-shadow duration-300">
          <div className="absolute top-0 right-0 w-16 h-16 bg-orange-500/10 rounded-bl-full"></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Spent</CardTitle>
            <CreditCard className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-500">
              {formatCurrency(totalSpent)}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              {totalBudgeted > 0
                ? ((totalSpent / totalBudgeted) * 100).toFixed(1)
                : "0.0"}
              % of total budget
            </p>
          </CardContent>
        </Card>
        <Card className="border-l-4 border-green-500 overflow-hidden relative group hover:shadow-lg transition-shadow duration-300">
          <div className="absolute top-0 right-0 w-16 h-16 bg-green-500/10 rounded-bl-full"></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Remaining</CardTitle>
            <DollarSign className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-500">
              {formatCurrency(totalRemaining)}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Available to spend
            </p>
          </CardContent>
        </Card>
        <Card className="border-l-4 border-emerald-500 overflow-hidden relative group hover:shadow-lg transition-shadow duration-300">
          <div className="absolute top-0 right-0 w-16 h-16 bg-emerald-500/10 rounded-bl-full"></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">On Track</CardTitle>
            <Target className="h-4 w-4 text-emerald-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-emerald-500">
              {onTrackCount}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Budgets under 75% used
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Budgets List */}
      {budgetsToDisplay.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-16">
            <Wallet className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No budgets yet</h3>
            <p className="text-muted-foreground text-center mb-4">
              Create your first budget to start tracking your spending
            </p>
            <Button onClick={() => setDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Create Budget
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {budgetsToDisplay.map((budget) => {
            const alert = getBudgetAlerts(budget);
            return (
              <Card key={budget.id} className={alert ? `${alert.bgColor} border-l-4` : ''}>
                <CardHeader className="pb-2">
                  <div className="flex items-start justify-between">
                    <div>
                      <div className="flex items-center gap-2">
                        <CardTitle>{budget.name}</CardTitle>
                        {alert && (
                          <alert.icon className={`h-4 w-4 ${alert.color}`} />
                        )}
                      </div>
                      <CardDescription>
                        {formatPeriod(budget.period)}
                        {budget.category_name && (
                          <span className="ml-2">• {budget.category_name}</span>
                        )}
                      </CardDescription>
                    </div>
                    <div className="flex gap-1">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8"
                        onClick={() => handleEdit(budget)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8"
                        onClick={() => handleDelete(budget.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">
                    {formatCurrency(budget.spent || 0)} of{" "}
                    {formatCurrency(budget.amount)}
                  </span>
                  <Badge
                    variant={
                      (budget.spent || 0) > budget.amount
                        ? "destructive"
                        : "outline"
                    }
                    className="ml-auto"
                  >
                    {(budget.spent || 0) > budget.amount ? (
                      <>
                        <AlertCircle className="h-3 w-3 mr-1" />
                        Over Budget
                      </>
                    ) : (budget.spent || 0) === 0 ? (
                      <>
                        <CheckCircle2 className="h-3 w-3 mr-1" />
                        Not Started
                      </>
                    ) : (
                      <>
                        <TrendingUp className="h-3 w-3 mr-1" />
                        {(((budget.spent || 0) / budget.amount) * 100).toFixed(
                          0
                        )}
                        % Used
                      </>
                    )}
                  </Badge>
                </div>
                <Progress
                  value={budget.progress || 0}
                  className="h-2"
                  indicatorClassName={
                    (budget.spent || 0) > budget.amount ? "bg-destructive" : ""
                  }
                />
                <div className="flex items-center justify-between text-sm">
                  <div>
                    <span className="font-medium">
                      {formatCurrency(budget.remaining || 0)}
                    </span>
                    <span className="text-muted-foreground ml-1">
                      {(budget.spent || 0) > budget.amount
                        ? "overspent"
                        : "remaining"}
                    </span>
                  </div>
                  <div className="text-muted-foreground">
                    {new Date(budget.start_date).toLocaleDateString("en-US", {
                      month: "short",
                      day: "numeric",
                    })}
                    {budget.end_date && (
                      <>
                        {" "}
                        -{" "}
                        {new Date(budget.end_date).toLocaleDateString("en-US", {
                          month: "short",
                          day: "numeric",
                        })}
                      </>
                    )}
                  </div>
                </div>
                {budget.category_id && (
                  <Badge
                    variant="outline"
                    className="text-xs px-1.5 py-0.5 ml-2"
                    style={{
                      backgroundColor: budget.category_color
                        ? `${budget.category_color}20`
                        : undefined,
                      borderColor: budget.category_color || undefined,
                      color: budget.category_color || undefined,
                    }}
                  >
                    {budget.category_name || "Uncategorized"}
                  </Badge>
                )}
              </CardContent>
            </Card>
            );
          })}
        </div>
      )}
    </div>
  );
}
