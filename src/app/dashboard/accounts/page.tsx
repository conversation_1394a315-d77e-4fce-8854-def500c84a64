"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/shared/components/ui/card";
import { Button } from "@/shared/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/shared/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/shared/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/shared/components/ui/select";
import { Badge } from "@/shared/components/ui/badge";
import { Input } from "@/shared/components/ui/input";
import { useForm, UseFormReturn, SubmitHandler } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Plus,
  Wallet,
  CreditCard,
  PiggyBank,
  DollarSign,
  Edit,
  Trash2,
  CheckCircle2,
  XCircle,
  TrendingUp,
  AlertTriangle,
  AlertCircle,
  Bell,
  Target,
  Banknote,
} from "lucide-react";
import { createClient } from "@/shared/services/supabase/client";
import { Account, AccountType } from "@/shared/types";
import { AccountData } from "@/shared/types/types";
import { toast } from "sonner";
import { useUserSettings } from "@/shared/contexts/UserSettingsContext";
import { useAppMode } from "@/shared/contexts/AppModeContext";
import { useCurrencyFormatter } from "@/shared/hooks/useCurrencyFormatter";

// Define form schema
const accountFormSchema = z
  .object({
    bank_name_select: z.string().min(1, "Bank name is required"),
    bank_name_custom: z.string().optional(),
    type: z.enum([
      "checking",
      "savings",
      "credit_card",
      "cash",
      "investment",
      "other",
    ]),
    balance: z.string().refine((val) => !isNaN(parseFloat(val)), {
      message: "Balance must be a valid number",
    }),
    currency: z.string().min(1, "Currency is required"),
    is_active: z.boolean(),
  })
  .superRefine((data, ctx) => {
    if (
      data.bank_name_select === "Other" &&
      (!data.bank_name_custom || data.bank_name_custom.trim() === "")
    ) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ["bank_name_custom"],
        message: 'Custom bank name is required when "Other" is selected',
      });
    }
  });

// Infer type from schema
type AccountFormValues = z.infer<typeof accountFormSchema>;

const presetBanks = [
  "BNP Paribas Fortis",
  "KBC Bank",
  "Belfius Bank",
  "ING Belgium",
  "Argenta",
  "AXA Bank Belgium",
  "Beobank",
  "Crelan",
  "Hello Bank!",
  "Keytrade Bank",
  "Triodos Bank",
  "vdk bank",
  "Bank Van Breda",
  "ING Group",
  "Rabobank",
  "ABN AMRO",
  "De Volksbank",
  "Van Lanschot Kempen",
  "Bunq",
  "Knab",
  "NIBC Bank",
  "Achmea Bank",
  "FMO",
  "Banque et Caisse d'Épargne de l'État (Spuerkeess)",
  "BGL BNP Paribas",
  "Banque Internationale à Luxembourg (BIL)",
  "Banque Raiffeisen",
  "Advanzia Bank",
  "Banque Degroof Petercam",
  "Quintet Private Bank",
  "East-West United Bank",
  "Société Générale Luxembourg",
  "ING Luxembourg",
];

export default function AccountsPage() {
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [loading, setLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingAccount, setEditingAccount] = useState<Account | null>(null);
  const [showCustomBankInput, setShowCustomBankInput] = useState(false);
  const {
    settings: { currency: defaultCurrency },
  } = useUserSettings();
  const { isPersonalMode } = useAppMode();
  const { formatCurrency } = useCurrencyFormatter();

  // Initialize the form with explicit type casting to fix TypeScript errors
  const form: UseFormReturn<AccountFormValues> = useForm({
    resolver: zodResolver(accountFormSchema),
    defaultValues: {
      bank_name_select: "",
      bank_name_custom: "",
      type: "checking",
      balance: "0",
      currency: "USD",
      is_active: true,
    },
  });

  useEffect(() => {
    fetchAccounts();
  }, []);

  // Log accounts state whenever it changes
  useEffect(() => {
    console.log("Accounts state changed (useEffect):", accounts);
  }, [accounts]);

  // Reset form when editing account changes
  useEffect(() => {
    if (editingAccount) {
      const isPresetBank = presetBanks.includes(editingAccount.name);
      if (isPresetBank) {
        form.reset({
          bank_name_select: editingAccount.name,
          bank_name_custom: "",
          type: editingAccount.type,
          balance: editingAccount.balance.toString(),
          currency: editingAccount.currency,
          is_active:
            typeof editingAccount.is_active === "boolean"
              ? editingAccount.is_active
              : true,
        });
        setShowCustomBankInput(false);
      } else {
        form.reset({
          bank_name_select: "Other",
          bank_name_custom: editingAccount.name,
          type: editingAccount.type,
          balance: editingAccount.balance.toString(),
          currency: editingAccount.currency,
          is_active:
            typeof editingAccount.is_active === "boolean"
              ? editingAccount.is_active
              : true,
        });
        setShowCustomBankInput(true);
      }
    } else {
      form.reset({
        bank_name_select: "", // Or a default placeholder value like "Select a bank"
        bank_name_custom: "",
        type: "checking",
        balance: "0",
        currency: defaultCurrency || "USD", // Use default currency from settings
        is_active: true,
      });
      setShowCustomBankInput(false);
    }
  }, [editingAccount, form, defaultCurrency]);

  const fetchAccounts = async () => {
    try {
      setLoading(true);
      const supabase = createClient();
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        toast.error("Please login to view accounts");
        setLoading(false); // Ensure loading is set to false
        return;
      }

      // Use a direct select query instead of RPC
      const { data, error } = await supabase
        .from("accounts")
        .select("*")
        .eq("user_id", user.id)
        .order("created_at", { ascending: false });

      if (error) throw error;

      console.log("Data fetched by fetchAccounts:", data);
      if (data) {
        const mappedAccounts: Account[] = data.map((dbAccount) => ({
          id: dbAccount.id,
          name: dbAccount.name,
          type: dbAccount.type as AccountType, // Explicitly cast to AccountType
          balance: dbAccount.balance,
          currency: dbAccount.currency,
          is_active: dbAccount.is_active ?? false, // Provide default for potentially null is_active
          created_at: dbAccount.created_at,
          updated_at: dbAccount.updated_at,
          user_id: dbAccount.user_id,
          // Supabase 'accounts' table might not have 'color' and 'icon' directly from select '*'
          // If your src/lib/supabase/types.ts Account interface includes them, ensure they are handled
          // For now, assuming they are not part of the direct fetch or are optional in the type
        }));
        setAccounts(mappedAccounts);
      } else {
        setAccounts([]);
      }
    } catch (error) {
      console.error("Error fetching accounts:", error);
      toast.error("Failed to load accounts");
    } finally {
      setLoading(false);
    }
  };

  const onSubmit: SubmitHandler<AccountFormValues> = async (values) => {
    try {
      const supabase = createClient();
      const {
        bank_name_select,
        bank_name_custom,
        type,
        balance,
        currency,
        is_active,
      } = values;

      console.log("Submitting is_active:", is_active);

      const {
        data: { user },
      } = await supabase.auth.getUser();
      if (!user) {
        toast.error("You must be logged in to save an account.");
        return;
      }

      const accountName =
        bank_name_select === "Other"
          ? bank_name_custom || ""
          : bank_name_select;

      const accountData: AccountData = {
        name: accountName,
        type,
        balance: parseFloat(balance),
        currency,
        is_active,
        user_id: user.id,
      };

      if (editingAccount) {
        // Update existing account and get the updated row
        const { data: updatedAccountArray, error } = await supabase
          .from("accounts")
          .update(accountData)
          .eq("id", editingAccount.id)
          .select();

        if (error) throw error;

        if (updatedAccountArray && updatedAccountArray.length > 0) {
          const dbUpdatedAccount = updatedAccountArray[0];
          const updatedFrontendAccount: Account = {
            id: dbUpdatedAccount.id,
            name: dbUpdatedAccount.name,
            type: dbUpdatedAccount.type as AccountType, // Explicitly cast
            balance: dbUpdatedAccount.balance,
            currency: dbUpdatedAccount.currency,
            is_active: dbUpdatedAccount.is_active ?? false,
            created_at: dbUpdatedAccount.created_at,
            updated_at: dbUpdatedAccount.updated_at,
            user_id: dbUpdatedAccount.user_id,
          };
          console.log(
            "Updated account mapped for frontend (onSubmit):",
            updatedFrontendAccount
          );
          setAccounts((prevAccounts) => {
            const newAccounts = prevAccounts.map((acc) =>
              acc.id === updatedFrontendAccount.id
                ? { ...acc, ...updatedFrontendAccount }
                : acc
            );
            console.log(
              "Accounts state after local update (onSubmit):",
              newAccounts
            );
            return newAccounts;
          });
          toast.success("Account updated successfully");
        } else {
          // Fallback if select doesn't return data, though it should
          toast.info("Account update submitted, refreshing list...");
        }
      } else {
        // Add new account and get the new row
        const { data: newAccountArray, error } = await supabase
          .from("accounts")
          .insert([accountData])
          .select();

        if (error) throw error;

        if (newAccountArray && newAccountArray.length > 0) {
          const newAccount = newAccountArray[0];
          console.log("New account from Supabase (onSubmit):", newAccount);
          // Add to local state, or rely on fetchAccounts
          // For simplicity and to avoid duplicates if fetchAccounts is fast,
          // we can just let fetchAccounts handle adding it to the list for now,
          // or prepend it if confident.
          // setAccounts(prevAccounts => [newAccount, ...prevAccounts]);
          toast.success("Account added successfully");
        } else {
          toast.info("Account creation submitted, refreshing list...");
        }
      }

      await fetchAccounts(); // Refresh the accounts list
      setIsDialogOpen(false); // Close the dialog
      form.reset(); // Reset the form
      setEditingAccount(null); // Clear editing state
    } catch (error) {
      console.error("Error saving account:", error);
      toast.error("Failed to save account");
    }
  };

  const handleEdit = (account: Account) => {
    setEditingAccount(account);
    setIsDialogOpen(true);
  };

  const handleDelete = async (accountId: string) => {
    if (
      !confirm(
        "Are you sure you want to delete this account? This may affect your transactions."
      )
    )
      return;

    try {
      const supabase = createClient();
      const { error } = await supabase.rpc("delete_account", {
        p_id: accountId,
      });

      if (error) throw error;
      toast.success("Account deleted successfully");
      fetchAccounts();
    } catch (error) {
      console.error("Error deleting account:", error);
      toast.error("Failed to delete account");
    }
  };

  // Helper function to get account type icon
  const getAccountTypeIcon = (type: AccountType) => {
    switch (type) {
      case "checking":
        return <Wallet className="h-4 w-4" />;
      case "savings":
        return <PiggyBank className="h-4 w-4" />;
      case "credit_card":
        return <CreditCard className="h-4 w-4" />;
      case "investment":
        return <TrendingUp className="h-4 w-4" />;
      default:
        return <DollarSign className="h-4 w-4" />;
    }
  };

  // Helper function to format account type for display
  const formatAccountType = (type: AccountType) => {
    return type
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  };

  // Helper function to format currency

  // Account alert system
  const getAccountAlerts = (account: Account) => {
    if (!account.is_active) return null;

    if (account.type === "credit_card" && account.balance > 5000) {
      return {
        type: "debt_warning" as const,
        message: `High credit card balance: ${formatCurrency(
          account.balance
        )}`,
        icon: AlertTriangle,
        color: "text-red-600",
        bgColor: "bg-red-50 border-red-200",
      };
    } else if (account.type === "checking" && account.balance < 500) {
      return {
        type: "low_balance" as const,
        message: `Low checking account balance`,
        icon: AlertCircle,
        color: "text-amber-600",
        bgColor: "bg-amber-50 border-amber-200",
      };
    } else if (account.type === "savings" && account.balance < 1000) {
      return {
        type: "low_savings" as const,
        message: `Consider building your emergency fund`,
        icon: Target,
        color: "text-blue-600",
        bgColor: "bg-blue-50 border-blue-200",
      };
    }
    return null;
  };

  // Calculate summary metrics
  const activeAccountsCount = accounts.filter((acc) => acc.is_active).length;
  const totalNetWorth = accounts.reduce((sum, acc) => {
    if (!acc.is_active) return sum;
    if (acc.type === "credit_card") {
      return sum - acc.balance;
    }
    return sum + acc.balance;
  }, 0);
  const totalCreditCardDebt = accounts.reduce((sum, acc) => {
    if (!acc.is_active) return sum;
    if (acc.type === "credit_card") {
      return sum + acc.balance;
    }
    return sum;
  }, 0);
  const totalAssets = accounts.reduce((sum, acc) => {
    if (!acc.is_active || acc.type === "credit_card") return sum;
    return sum + acc.balance;
  }, 0);

  // Get account alerts for the alerts section
  const accountAlerts = accounts
    .map((account) => {
      const alert = getAccountAlerts(account);
      return alert ? { account, alert } : null;
    })
    .filter(
      (
        item
      ): item is {
        account: Account;
        alert: NonNullable<ReturnType<typeof getAccountAlerts>>;
      } => item !== null
    )
    .sort((a, b) => {
      // Sort by severity: debt_warning > low_balance > low_savings
      const severity = { debt_warning: 3, low_balance: 2, low_savings: 1 };
      return severity[b.alert.type] - severity[a.alert.type];
    });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {isPersonalMode ? "Personal Accounts" : "Family Accounts"}
          </h1>
          <p className="text-muted-foreground">
            {isPersonalMode
              ? "Manage your personal bank accounts and track your finances"
              : "Manage family group accounts and shared financial resources"}
          </p>
        </div>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => setEditingAccount(null)}>
              <Plus className="mr-2 h-4 w-4" />
              Add Account
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                {editingAccount ? "Edit Account" : "Add New Account"}
              </DialogTitle>
              <DialogDescription>
                {editingAccount
                  ? "Update the details for this account."
                  : "Enter the details for the new account."}
              </DialogDescription>
            </DialogHeader>

            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-4"
              >
                <FormField
                  control={form.control}
                  name="bank_name_select"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Bank Name</FormLabel>
                      <Select
                        onValueChange={(value) => {
                          field.onChange(value);
                          if (value === "Other") {
                            setShowCustomBankInput(true);
                          } else {
                            setShowCustomBankInput(false);
                            form.setValue("bank_name_custom", "");
                          }
                        }}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a bank" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {presetBanks.map((bank) => (
                            <SelectItem key={bank} value={bank}>
                              {bank}
                            </SelectItem>
                          ))}
                          <SelectItem value="Other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                {showCustomBankInput && (
                  <FormField
                    control={form.control}
                    name="bank_name_custom"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Custom Bank Name</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g., My Local Bank" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Account Type</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select account type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="checking">Checking</SelectItem>
                          <SelectItem value="savings">Savings</SelectItem>
                          <SelectItem value="credit_card">
                            Credit Card
                          </SelectItem>
                          <SelectItem value="cash">Cash</SelectItem>
                          <SelectItem value="investment">Investment</SelectItem>
                          <SelectItem value="other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="balance"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Initial Balance</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          placeholder="0.00"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Enter the current balance for accurate tracking
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="currency"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Currency</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select currency" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="USD">USD ($)</SelectItem>
                          <SelectItem value="EUR">EUR (€)</SelectItem>
                          <SelectItem value="GBP">GBP (£)</SelectItem>
                          <SelectItem value="JPY">JPY (¥)</SelectItem>
                          <SelectItem value="CAD">CAD ($)</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="is_active"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                      <FormControl>
                        <input
                          type="checkbox"
                          checked={field.value}
                          onChange={(e) => field.onChange(e.target.checked)}
                          className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Active Account</FormLabel>
                        <FormDescription>
                          Uncheck to hide this account from dashboards and
                          reports
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />
                <DialogFooter>
                  <Button type="submit">
                    {editingAccount ? "Update" : "Add"} Account
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Account Alerts */}
      {accountAlerts.length > 0 && (
        <div className="space-y-3">
          <h2 className="text-lg font-semibold flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Account Alerts
          </h2>
          <div className="space-y-2">
            {accountAlerts.slice(0, 3).map(({ account, alert }) => {
              const IconComponent = alert.icon;
              return (
                <Card
                  key={account.id}
                  className={`${alert.bgColor} border-l-4`}
                >
                  <CardContent className="py-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <IconComponent className={`h-4 w-4 ${alert.color}`} />
                        <div>
                          <p className="font-medium">{account.name}</p>
                          <p className={`text-sm ${alert.color}`}>
                            {alert.message}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-muted-foreground">
                          {formatCurrency(account.balance)}
                        </span>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEdit(account)}
                          className="h-8"
                        >
                          <Edit className="h-3 w-3 mr-1" />
                          Edit
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      )}

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-8">
        <Card
          className={`border-l-4 ${
            totalNetWorth >= 0 ? "border-blue-500" : "border-red-500"
          } overflow-hidden relative group hover:shadow-lg transition-shadow duration-300`}
        >
          <div
            className={`absolute top-0 right-0 w-16 h-16 ${
              totalNetWorth >= 0 ? "bg-blue-500/10" : "bg-red-500/10"
            } rounded-bl-full`}
          ></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Net Worth
            </CardTitle>
            <TrendingUp
              className={`h-4 w-4 ${
                totalNetWorth >= 0 ? "text-blue-500" : "text-red-500"
              }`}
            />
          </CardHeader>
          <CardContent>
            <div
              className={`text-2xl font-bold ${
                totalNetWorth >= 0 ? "text-blue-500" : "text-red-500"
              }`}
            >
              {formatCurrency(totalNetWorth)}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Assets minus liabilities
            </p>
          </CardContent>
        </Card>
        <Card className="border-l-4 border-purple-500 overflow-hidden relative group hover:shadow-lg transition-shadow duration-300">
          <div className="absolute top-0 right-0 w-16 h-16 bg-purple-500/10 rounded-bl-full"></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Active Accounts
            </CardTitle>
            <Wallet className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-500">
              {activeAccountsCount}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Currently active accounts
            </p>
          </CardContent>
        </Card>
        <Card className="border-l-4 border-emerald-500 overflow-hidden relative group hover:shadow-lg transition-shadow duration-300">
          <div className="absolute top-0 right-0 w-16 h-16 bg-emerald-500/10 rounded-bl-full"></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Assets</CardTitle>
            <Banknote className="h-4 w-4 text-emerald-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-emerald-500">
              {formatCurrency(totalAssets)}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Excluding credit card debt
            </p>
          </CardContent>
        </Card>
        <Card className="border-l-4 border-red-500 overflow-hidden relative group hover:shadow-lg transition-shadow duration-300">
          <div className="absolute top-0 right-0 w-16 h-16 bg-red-500/10 rounded-bl-full"></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Credit Card Debt
            </CardTitle>
            <CreditCard className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-500">
              {formatCurrency(totalCreditCardDebt)}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Total outstanding on credit cards
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Accounts List */}
      {accounts.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center p-6 text-center">
            <Wallet className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium">No accounts yet</h3>
            <p className="text-sm text-muted-foreground mt-2 mb-4">
              Add your first account to start tracking your finances
            </p>
            <Button onClick={() => setIsDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Add Account
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {accounts.map((account) => {
            const alert = getAccountAlerts(account);
            return (
              <Card
                key={account.id}
                className={alert ? `${alert.bgColor} border-l-4` : ""}
              >
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <div className="flex items-center space-x-2">
                      <div className="p-2 bg-primary/10 rounded-full">
                        {getAccountTypeIcon(account.type)}
                      </div>
                      <div>
                        <CardTitle className="text-lg flex items-center gap-2">
                          {account.name}
                          {alert && (
                            <alert.icon className={`h-4 w-4 ${alert.color}`} />
                          )}
                        </CardTitle>
                        <CardDescription>
                          {formatAccountType(account.type)}
                        </CardDescription>
                      </div>
                    </div>
                    <div className="flex flex-col gap-1 items-end">
                      <Badge
                        variant={account.is_active ? "default" : "outline"}
                      >
                        {account.is_active ? (
                          <span className="flex items-center">
                            <CheckCircle2 className="h-3 w-3 mr-1" />
                            Active
                          </span>
                        ) : (
                          <span className="flex items-center">
                            <XCircle className="h-3 w-3 mr-1" />
                            Inactive
                          </span>
                        )}
                      </Badge>
                      {alert && (
                        <Badge
                          variant="outline"
                          className={`text-xs ${alert.color}`}
                        >
                          {alert.type === "debt_warning" && "High Debt"}
                          {alert.type === "low_balance" && "Low Balance"}
                          {alert.type === "low_savings" && "Low Savings"}
                        </Badge>
                      )}
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between items-center">
                    <div className="text-2xl font-bold">
                      {formatCurrency(account.balance)}
                    </div>
                    <div className="flex space-x-1">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleEdit(account)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleDelete(account.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  {alert && (
                    <div className="pt-2 border-t">
                      <p
                        className={`text-sm ${alert.color} flex items-center gap-2`}
                      >
                        <alert.icon className="h-3 w-3" />
                        {alert.message}
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}
    </div>
  );
}
