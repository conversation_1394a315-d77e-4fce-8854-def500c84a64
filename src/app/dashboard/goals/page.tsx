"use client";

import { useState, useEffect, useCallback } from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle,
} from "@/shared/components/ui/card";
import { Button } from "@/shared/components/ui/button";
import { Progress } from "@/shared/components/ui/progress";
import { Badge } from "@/shared/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/shared/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/shared/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/shared/components/ui/select";
import { Input } from "@/shared/components/ui/input";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Plus,
  Target,
  TrendingUp,
  DollarSign,
  Edit,
  Trash2,
  Pause,
  Play,
  Trophy,
  Calendar,
  AlertTriangle,
  CheckCircle2,
  Clock,
  TrendingDown,
} from "lucide-react";
import { createClient } from "@/shared/services/supabase/client";
import { toast } from "sonner";
import { FinancialGoal } from "@/shared/types";
import { useUserSettings } from "@/shared/contexts/UserSettingsContext";
import { useAppMode } from "@/shared/contexts/AppModeContext";
import { useCurrencyFormatter } from "@/shared/hooks/useCurrencyFormatter";

const GOAL_COLORS = [
  "#3b82f6", // Blue
  "#10b981", // Emerald
  "#f59e0b", // Amber
  "#ef4444", // Red
  "#8b5cf6", // Violet
  "#ec4899", // Pink
];

const goalFormSchema = z.object({
  name: z.string().min(1, "Goal name is required"),
  target_amount: z
    .string()
    .refine((val) => !isNaN(parseFloat(val)) && parseFloat(val) > 0, {
      message: "Target amount must be a valid number greater than 0",
    }),
  current_amount: z.string().refine((val) => !isNaN(parseFloat(val)), {
    message: "Current amount must be a valid number",
  }),
  currency: z.string().min(1, "Currency is required"),
  deadline: z.string().optional(),
  color: z.string().default(GOAL_COLORS[0]),
});

type GoalFormValues = z.infer<typeof goalFormSchema>;

interface DisplayGoal extends FinancialGoal {
  progress: number;
  daysRemaining?: number; // Can be undefined if no deadline
  monthlyRequired?: number; // Can be undefined if no deadline or already met
}

export default function GoalsPage() {
  const [goals, setGoals] = useState<DisplayGoal[]>([]);
  const [loading, setLoading] = useState(true);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingGoal, setEditingGoal] = useState<DisplayGoal | null>(null);
  const {
    settings: { currency: defaultCurrency },
  } = useUserSettings();
  const { isPersonalMode } = useAppMode();
  const { formatCurrency } = useCurrencyFormatter();

  const form = useForm({
    resolver: zodResolver(goalFormSchema),
    defaultValues: {
      name: "",
      target_amount: "0",
      current_amount: "0",
      currency: defaultCurrency,
      deadline: "",
      color: GOAL_COLORS[0],
    },
  });

  const fetchGoals = useCallback(async () => {
    try {
      const supabase = createClient();
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        toast.error("Please login to view goals");
        return;
      }

      const { data, error } = await supabase
        .from("financial_goals")
        .select("*")
        .eq("user_id", user.id)
        .order("created_at", { ascending: false });

      if (error) throw error;

      const goalsWithCalculations = (data || []).map(
        (goalData): DisplayGoal => {
          const goal = goalData as FinancialGoal; // Cast to ensure type safety
          const progress =
            goal.target_amount > 0
              ? (goal.current_amount / goal.target_amount) * 100
              : 0;
          let daysRemaining: number | undefined = undefined;
          let monthlyRequired: number | undefined = undefined;

          if (goal.deadline) {
            const today = new Date();
            today.setHours(0, 0, 0, 0); // Normalize today to start of day
            const targetDate = new Date(goal.deadline);
            targetDate.setHours(0, 0, 0, 0); // Normalize targetDate to start of day

            if (targetDate >= today) {
              daysRemaining = Math.ceil(
                (targetDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)
              );
              const monthsRemaining = Math.max(1, daysRemaining / 30.4375); // Average days in month
              const amountRemaining = Math.max(
                0,
                goal.target_amount - goal.current_amount
              );
              if (amountRemaining > 0) {
                monthlyRequired = amountRemaining / monthsRemaining;
              }
            } else {
              daysRemaining = 0; // Deadline has passed
            }
          }

          return {
            ...goal,
            currency: goal.currency || defaultCurrency,
            progress: Math.min(progress, 100),
            daysRemaining,
            monthlyRequired: monthlyRequired
              ? Math.max(0, monthlyRequired)
              : undefined,
          };
        }
      );

      setGoals(goalsWithCalculations);
    } catch (error) {
      console.error("Error fetching goals:", error);
      toast.error("Failed to load goals");
    } finally {
      setLoading(false);
    }
  }, [defaultCurrency]);

  useEffect(() => {
    fetchGoals();
  }, [fetchGoals]);

  const onSubmit = async (values: GoalFormValues) => {
    try {
      const supabase = createClient();
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        toast.error("Please login to create goals");
        return;
      }

      const goalDataToSave: Omit<
        FinancialGoal,
        "id" | "user_id" | "created_at" | "updated_at"
      > = {
        name: values.name,
        target_amount: parseFloat(values.target_amount),
        current_amount: parseFloat(values.current_amount),
        currency: values.currency,
        deadline: values.deadline || null,
        color: values.color,
        is_active: true,
      };

      if (editingGoal) {
        const { error } = await supabase
          .from("financial_goals")
          .update(goalDataToSave)
          .eq("id", editingGoal.id);

        if (error) throw error;
        toast.success("Goal updated successfully");
      } else {
        const { error } = await supabase
          .from("financial_goals")
          .insert([{ ...goalDataToSave, user_id: user.id }])
          .select() // Select to get the newly created goal back if needed
          .single(); // Assuming one insert

        if (error) throw error;
        toast.success("Goal created successfully");
      }

      setDialogOpen(false);
      form.reset();
      setEditingGoal(null);
      fetchGoals();
    } catch (error) {
      console.error("Error saving goal:", error);
      toast.error("Failed to save goal");
    }
  };

  const handleAddProgress = async (goalId: string, amountToAdd: number) => {
    try {
      const supabase = createClient();
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        toast.error("Please login to update progress");
        return;
      }

      // Fetch the current goal to get its current_amount
      const { data: currentGoalData, error: fetchError } = await supabase
        .from("financial_goals")
        .select("current_amount")
        .eq("id", goalId)
        .single();

      if (fetchError) throw fetchError;
      if (!currentGoalData) throw new Error("Goal not found");

      const newCurrentAmount = currentGoalData.current_amount + amountToAdd;

      const { error: updateError } = await supabase
        .from("financial_goals")
        .update({ current_amount: newCurrentAmount })
        .eq("id", goalId);

      if (updateError) throw updateError;

      toast.success("Progress added successfully");
      fetchGoals(); // Refresh the goals list
      // Dialog closing is handled in the onSubmit for now
    } catch (error) {
      console.error("Error adding progress:", error);
      toast.error("Failed to add progress");
    }
  };

  const handleEdit = (goal: DisplayGoal) => {
    setEditingGoal(goal);
    form.reset({
      name: goal.name,
      target_amount: goal.target_amount.toString(),
      current_amount: goal.current_amount.toString(),
      currency: goal.currency || defaultCurrency,
      deadline: goal.deadline || "",
      color: goal.color || GOAL_COLORS[0],
    });
    setDialogOpen(true);
  };

  const handleDelete = async (goalId: string) => {
    if (!confirm("Are you sure you want to delete this goal?")) return;

    try {
      const supabase = createClient();
      const { error } = await supabase
        .from("financial_goals")
        .delete()
        .eq("id", goalId);

      if (error) throw error;
      toast.success("Goal deleted successfully");
      fetchGoals();
    } catch (error) {
      console.error("Error deleting goal:", error);
      toast.error("Failed to delete goal");
    }
  };


  const getStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return (
          <Badge className="bg-green-500/10 text-green-500 hover:bg-green-500/20">
            <CheckCircle2 className="h-3 w-3 mr-1" />
            Completed
          </Badge>
        );
      case "paused":
        return (
          <Badge className="bg-yellow-500/10 text-yellow-500 hover:bg-yellow-500/20">
            <Pause className="h-3 w-3 mr-1" />
            Paused
          </Badge>
        );
      default:
        return (
          <Badge className="bg-blue-500/10 text-blue-500 hover:bg-blue-500/20">
            <Target className="h-3 w-3 mr-1" />
            Active
          </Badge>
        );
    }
  };

  // Get goal urgency and status indicators
  const getGoalIndicators = (goal: DisplayGoal) => {
    const indicators = [];

    if (goal.progress >= 100) {
      indicators.push({
        type: "completed",
        message: "Goal achieved!",
        icon: Trophy,
        color: "text-green-600",
        bgColor: "bg-green-50 border-green-200",
      });
    } else if (goal.daysRemaining !== undefined) {
      if (goal.daysRemaining <= 7 && goal.daysRemaining > 0) {
        indicators.push({
          type: "urgent",
          message: `Only ${goal.daysRemaining} days left`,
          icon: AlertTriangle,
          color: "text-red-600",
          bgColor: "bg-red-50 border-red-200",
        });
      } else if (goal.daysRemaining <= 30 && goal.daysRemaining > 7) {
        indicators.push({
          type: "warning",
          message: `${goal.daysRemaining} days remaining`,
          icon: Clock,
          color: "text-amber-600",
          bgColor: "bg-amber-50 border-amber-200",
        });
      } else if (goal.daysRemaining === 0) {
        indicators.push({
          type: "overdue",
          message: "Deadline passed",
          icon: TrendingDown,
          color: "text-red-600",
          bgColor: "bg-red-50 border-red-200",
        });
      }
    }

    if (goal.progress > 0 && goal.progress < 25) {
      indicators.push({
        type: "slow",
        message: "Consider increasing contributions",
        icon: TrendingUp,
        color: "text-blue-600",
        bgColor: "bg-blue-50 border-blue-200",
      });
    }

    return indicators[0]; // Return the most important indicator
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  const activeGoals = goals.filter((g) => g.is_active);
  const completedGoals = goals.filter((g) => !g.is_active);
  const totalSaved = goals.reduce((sum, g) => sum + g.current_amount, 0);
  const totalTarget = goals.reduce((sum, g) => sum + g.target_amount, 0);

  // Get goal alerts for urgent goals
  const goalAlerts = activeGoals
    .map((goal) => {
      const indicator = getGoalIndicators(goal);
      return indicator &&
        ["urgent", "overdue", "warning"].includes(indicator.type)
        ? { goal, indicator }
        : null;
    })
    .filter(
      (
        item
      ): item is {
        goal: DisplayGoal;
        indicator: NonNullable<ReturnType<typeof getGoalIndicators>>;
      } => item !== null
    )
    .sort((a, b) => {
      // Sort by urgency: overdue > urgent > warning
      const urgency: Record<string, number> = {
        overdue: 3,
        urgent: 2,
        warning: 1,
      };
      return urgency[b.indicator.type] - urgency[a.indicator.type];
    });

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {isPersonalMode ? "Personal Goals" : "Family Goals"}
          </h1>
          <p className="text-muted-foreground">
            {isPersonalMode
              ? "Track your personal progress towards financial milestones"
              : "Manage family financial goals and shared targets"}
          </p>
        </div>
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              New Goal
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>
                {editingGoal ? "Edit Goal" : "Create New Goal"}
              </DialogTitle>
              <DialogDescription>
                Set a financial target to work towards
              </DialogDescription>
            </DialogHeader>
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-4"
              >
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Goal Name</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., Emergency Fund" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="target_amount"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Target Amount</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          placeholder="1000"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="current_amount"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Current Amount</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          placeholder="0"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        How much you&apos;ve already saved
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="currency"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Currency</FormLabel>
                      <FormControl>
                        <Select
                          onValueChange={field.onChange}
                          value={field.value}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select currency" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="USD">USD ($)</SelectItem>
                            <SelectItem value="EUR">EUR (€)</SelectItem>
                            <SelectItem value="GBP">GBP (£)</SelectItem>
                            <SelectItem value="JPY">JPY (¥)</SelectItem>
                            <SelectItem value="CAD">CAD ($)</SelectItem>
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="deadline"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Deadline</FormLabel>
                      <FormControl>
                        <Input type="date" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="color"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Color</FormLabel>
                      <FormControl>
                        <div className="flex flex-wrap gap-2">
                          {GOAL_COLORS.map((color) => (
                            <button
                              key={color}
                              type="button"
                              className={`w-8 h-8 rounded-full border-2 border-white dark:border-slate-800 ${
                                field.value === color
                                  ? "ring-2 ring-offset-2 ring-primary"
                                  : ""
                              }`}
                              style={{ backgroundColor: color }}
                              onClick={() => field.onChange(color)}
                            />
                          ))}
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <DialogFooter>
                  <Button type="submit">
                    {editingGoal ? "Update" : "Create"} Goal
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Goal Alerts */}
      {goalAlerts.length > 0 && (
        <div className="space-y-3">
          <h2 className="text-lg font-semibold flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            Goal Alerts
          </h2>
          <div className="space-y-2">
            {goalAlerts.slice(0, 3).map(({ goal, indicator }) => {
              const IconComponent = indicator.icon;
              return (
                <Card
                  key={goal.id}
                  className={`${indicator.bgColor} border-l-4`}
                >
                  <CardContent className="py-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <IconComponent
                          className={`h-4 w-4 ${indicator.color}`}
                        />
                        <div>
                          <p className="font-medium">{goal.name}</p>
                          <p className={`text-sm ${indicator.color}`}>
                            {indicator.message}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-muted-foreground">
                          {goal.progress.toFixed(0)}% complete
                        </span>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEdit(goal)}
                          className="h-8"
                        >
                          <Edit className="h-3 w-3 mr-1" />
                          Edit
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      )}

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="border-l-4 border-blue-500 overflow-hidden relative group hover:shadow-lg transition-shadow duration-300">
          <div className="absolute top-0 right-0 w-16 h-16 bg-blue-500/10 rounded-bl-full"></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Goals</CardTitle>
            <Target className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-500">
              {activeGoals.length}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Currently working on
            </p>
          </CardContent>
        </Card>
        <Card className="border-l-4 border-green-500 overflow-hidden relative group hover:shadow-lg transition-shadow duration-300">
          <div className="absolute top-0 right-0 w-16 h-16 bg-green-500/10 rounded-bl-full"></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Completed Goals
            </CardTitle>
            <Trophy className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-500">
              {completedGoals.length}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Successfully achieved
            </p>
          </CardContent>
        </Card>
        <Card className="border-l-4 border-purple-500 overflow-hidden relative group hover:shadow-lg transition-shadow duration-300">
          <div className="absolute top-0 right-0 w-16 h-16 bg-purple-500/10 rounded-bl-full"></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Saved</CardTitle>
            <DollarSign className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-500">
              {formatCurrency(totalSaved)}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Across all goals
            </p>
          </CardContent>
        </Card>
        <Card className="border-l-4 border-amber-500 overflow-hidden relative group hover:shadow-lg transition-shadow duration-300">
          <div className="absolute top-0 right-0 w-16 h-16 bg-amber-500/10 rounded-bl-full"></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Overall Progress
            </CardTitle>
            <TrendingUp className="h-4 w-4 text-amber-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-amber-500">
              {totalTarget > 0
                ? ((totalSaved / totalTarget) * 100).toFixed(0)
                : 0}
              %
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Of total target
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Goals List */}
      {goals.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-16">
            <Target className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No goals yet</h3>
            <p className="text-muted-foreground text-center mb-4">
              Create your first financial goal to start tracking progress
            </p>
            <Button onClick={() => setDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Create Goal
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {goals.map((goal) => {
            const indicator = getGoalIndicators(goal);
            return (
              <Card
                key={goal.id}
                className={`relative ${
                  indicator ? `${indicator.bgColor} border-l-4` : ""
                }`}
              >
                <CardHeader className="pb-2">
                  <div className="flex items-start justify-between">
                    <div className="space-y-1">
                      <CardTitle className="text-lg flex items-center gap-2">
                        {goal.name}
                        {indicator && (
                          <indicator.icon
                            className={`h-4 w-4 ${indicator.color}`}
                          />
                        )}
                      </CardTitle>
                      <div className="flex items-center gap-2 flex-wrap">
                        {getStatusBadge(
                          goal.is_active ? "active" : "completed"
                        )}
                        {goal.deadline && (
                          <Badge variant="outline" className="text-xs">
                            <Calendar className="h-3 w-3 mr-1" />
                            {new Date(goal.deadline).toLocaleDateString()}
                          </Badge>
                        )}
                        {(goal.daysRemaining ?? 0) > 0 && goal.is_active && (
                          <span className="text-xs text-muted-foreground">
                            {goal.daysRemaining} days left
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="flex gap-1">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8"
                        onClick={() => handleEdit(goal)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8"
                        onClick={() => handleDelete(goal.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Progress</span>
                      <span className="font-medium">
                        {formatCurrency(goal.current_amount)} /{" "}
                        {formatCurrency(goal.target_amount)}
                      </span>
                    </div>
                    <Progress
                      value={goal.progress}
                      className="h-2"
                      indicatorClassName={goal.is_active ? "" : "bg-green-500"}
                    />
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">
                        {goal.progress.toFixed(0)}% complete
                      </span>
                      {goal.is_active && (goal.monthlyRequired ?? 0) > 0 && (
                        <span className="text-muted-foreground">
                          {goal.monthlyRequired
                            ? formatCurrency(
                                goal.monthlyRequired
                              )
                            : "-"}
                          /month needed
                        </span>
                      )}
                    </div>
                  </div>

                  {goal.is_active && (
                    <div className="flex gap-2">
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex-1"
                          >
                            <Plus className="h-4 w-4 mr-1" />
                            Add Progress
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Add Progress</DialogTitle>
                            <DialogDescription>
                              How much would you like to add to this goal?
                            </DialogDescription>
                          </DialogHeader>
                          <form
                            onSubmit={(e) => {
                              e.preventDefault();
                              const formData = new FormData(e.currentTarget);
                              const amount = parseFloat(
                                formData.get("amount") as string
                              );
                              if (!isNaN(amount) && amount > 0) {
                                handleAddProgress(goal.id, amount);
                                // Close the dialog after submission
                                // This might need to be managed by state for robustness
                                const dialogElement =
                                  e.currentTarget.closest('div[role="dialog"]');
                                if (
                                  dialogElement &&
                                  dialogElement.parentElement
                                ) {
                                  // Find the button that triggered this dialog and click it to close
                                  // This is a workaround as shadcn dialogs controlled by internal state are hard to close programmatically from outside
                                  // A better way is to control 'open' prop with useState
                                  const triggerButton = document.querySelector(
                                    `[aria-controls='${dialogElement.id}']`
                                  );
                                  if (triggerButton instanceof HTMLElement) {
                                    // This is still not ideal, direct DOM manipulation for closing is tricky.
                                    // For now, we'll rely on the user clicking outside or pressing Esc.
                                    // Or, we could manage open state for each dialog individually.
                                  }
                                }
                                // A simpler approach for now: let the user close it or it auto-closes if not controlled by 'open'
                                // Or, if the Dialog component from shadcn/ui closes on submit by default when not controlled, this might be fine.
                                // For now, let's assume the dialog closes itself or user closes it.
                              }
                            }}
                            className="space-y-4"
                          >
                            <Input
                              name="amount"
                              type="number"
                              step="0.01"
                              placeholder="0.00"
                              required
                            />
                            <DialogFooter>
                              <Button type="submit">Add Amount</Button>
                            </DialogFooter>
                          </form>
                        </DialogContent>
                      </Dialog>
                      <Button variant="outline" size="sm" onClick={() => {}}>
                        <Pause className="h-4 w-4" />
                      </Button>
                    </div>
                  )}

                  {!goal.is_active && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full"
                      onClick={() => {}}
                    >
                      <Play className="h-4 w-4 mr-1" />
                      Resume Goal
                    </Button>
                  )}

                  {!goal.is_active && (
                    <div className="flex items-center justify-center gap-2 text-green-500">
                      <Trophy className="h-4 w-4" />
                      <span className="text-sm font-medium">
                        Goal Achieved!
                      </span>
                    </div>
                  )}
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}
    </div>
  );
}
