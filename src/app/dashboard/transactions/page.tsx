"use client";

import React, { useEffect, useState, useCallback } from "react";
import { createClient } from "@/shared/services/supabase/client";
import { Transaction as SupabaseTransaction } from "@/shared/types";
import { Account, Category as CategoryType } from "@/shared/types/types";
import { Button } from "@/shared/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogClose,
} from "@/shared/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/shared/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/shared/components/ui/select";
import { Input } from "@/shared/components/ui/input";
import { Label } from "@/shared/components/ui/label";
import { useForm, Controller, SubmitHandler } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { toast } from "sonner";
import { Edit, Trash2, PlusCircle } from "lucide-react";
import { DatePicker } from "@/shared/components/ui/date-picker";
import { useUserSettings } from "@/shared/contexts/UserSettingsContext";
import { useCurrencyFormatter } from "@/shared/hooks/useCurrencyFormatter";
import { ScrollArea } from "@/shared/components/ui/scroll-area";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/shared/components/ui/card";
import { Wallet, CreditCard, DollarSign } from "lucide-react";
import { CategoryIcon } from "@/shared/components/ui/category-icon";

const supabase = createClient();

// Zod schema for transaction form validation
const transactionFormSchema = z.object({
  description: z.string().min(1, "Description is required"),
  amount: z.coerce.number().positive("Amount must be positive"),
  type: z.enum(["income", "expense"], { required_error: "Type is required" }),
  date: z.date({ required_error: "Date is required" }),
  category_id: z.string().optional(),
  account_id: z.string({ required_error: "Account is required" }),
  currency: z
    .string()
    .min(3, "Currency is required")
    .max(3, "Currency code must be 3 letters"),
});

type TransactionFormData = z.infer<typeof transactionFormSchema>;

interface TransactionDataFromQuery {
  id: string;
  user_id: string | null;
  account_id: string;
  category_id?: string | null;
  budget_id?: string | null;
  description: string | null;
  amount: number | string;
  currency: string | null;
  type: string;
  date: string;
  created_at: string | null;
  updated_at: string | null;
  categories: { name: string; color: string | null } | null;
}


export default function TransactionsPage() {
  const [transactions, setTransactions] = useState<SupabaseTransaction[]>([]);
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [categories, setCategories] = useState<CategoryType[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingTransaction, setEditingTransaction] =
    useState<SupabaseTransaction | null>(null);

  const { settings, loading: settingsLoading } = useUserSettings();
  const { formatCurrency } = useCurrencyFormatter();
  const defaultCurrency = settingsLoading ? "USD" : settings?.currency || "USD";

  const form = useForm<TransactionFormData>({
    resolver: zodResolver(transactionFormSchema),
    defaultValues: {
      description: "",
      amount: 0,
      type: "expense",
      date: new Date(),
      category_id: undefined,
      account_id: undefined,
      currency: defaultCurrency,
    },
  });

  useEffect(() => {
    if (!editingTransaction) {
      form.reset({ ...form.getValues(), currency: defaultCurrency });
    }
  }, [defaultCurrency, form, editingTransaction]);

  const mapTransactionToFormData = useCallback(
    (transaction: SupabaseTransaction): TransactionFormData => {
      return {
        description: transaction.description,
        amount: Number(transaction.amount),
        type: transaction.type as "income" | "expense",
        date: new Date(transaction.date),
        category_id: transaction.category_id || undefined,
        account_id: transaction.account_id || "",
        currency: transaction.currency || defaultCurrency,
      };
    },
    [defaultCurrency]
  );

  const fetchData = useCallback(async () => {
    if (settingsLoading) {
      return;
    }
    setIsLoading(true);
    try {
      const { data: transactionsData, error: transactionsError } =
        await supabase
          .from("transactions")
          .select("*, categories(name, color)")
          .order("date", { ascending: false });
      if (transactionsError) throw transactionsError;

      const mappedTransactions = (transactionsData || [])
        .filter(
          (t: TransactionDataFromQuery) =>
            t.type === "income" || t.type === "expense"
        ) // Filter out transfer types
        .map((t: TransactionDataFromQuery): SupabaseTransaction => {
          const userId = t.user_id || "unknown_user";

          return {
            id: t.id,
            user_id: userId,
            account_id: t.account_id,
            category_id: t.category_id || undefined,
            budget_id: t.budget_id || undefined,
            description: t.description || "",
            amount: parseFloat(t.amount as string),
            currency: t.currency || defaultCurrency,
            type:
              t.type === "income" || t.type === "expense"
                ? (t.type as "income" | "expense")
                : "expense",
            date: t.date
              ? new Date(t.date).toISOString().split("T")[0]
              : new Date().toISOString().split("T")[0],
            created_at: t.created_at || new Date().toISOString(),
            updated_at: t.updated_at || new Date().toISOString(),
            category_name: t.categories?.name,
            category_color: t.categories?.color ?? undefined,
          };
        });
      setTransactions(mappedTransactions);

      const { data: accountsData, error: accountsError } = await supabase
        .from("accounts")
        .select("*")
        .eq("is_active", true);
      if (accountsError) throw accountsError;

      // Map Supabase data to Account type
      const mappedAccounts: Account[] = (accountsData || []).map((acc) => ({
        id: acc.id,
        name: acc.name,
        type: acc.type as Account["type"],
        balance: acc.balance,
        currency: acc.currency,
        is_active: acc.is_active ?? true,
        created_at: acc.created_at,
        updated_at: acc.updated_at,
        user_id: acc.user_id,
        icon: acc.icon || undefined,
        color: acc.color || undefined,
      }));
      setAccounts(mappedAccounts);

      const { data: categoriesData, error: categoriesError } = await supabase
        .from("categories")
        .select("*");
      if (categoriesError) throw categoriesError;

      // Map Supabase data to Category type
      const mappedCategories: CategoryType[] = (categoriesData || []).map(
        (cat) => ({
          id: cat.id,
          name: cat.name,
          type: cat.type as "income" | "expense",
          color: cat.color || "#0ea5e9",
          icon: cat.icon || undefined,
          parent_id: cat.parent_id || undefined,
          user_id: cat.user_id,
          created_at: cat.created_at,
          updated_at: cat.updated_at,
        })
      );
      setCategories(mappedCategories);
    } catch (error) {
      console.error("Error fetching data:", error);
      toast.error("Failed to load data. Please try again.");
    } finally {
      setIsLoading(false);
    }
  }, [defaultCurrency, settingsLoading]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const onSubmitTransaction: SubmitHandler<TransactionFormData> = async (
    data
  ) => {
    try {
      const {
        data: { user },
        error: userError,
      } = await supabase.auth.getUser();
      if (userError) {
        console.error("Error getting current user:", userError);
        toast.error("Authentication error. Please sign in again.");
        return;
      }

      if (!user) {
        console.error("No authenticated user found");
        toast.error("You must be signed in to add transactions.");
        return;
      }

      const processedData = {
        ...data,
        amount: Number(data.amount),
        date: data.date.toISOString().split("T")[0],
        user_id: user.id,
        category_id:
          data.category_id === "NO_CATEGORY_SELECTED"
            ? undefined
            : data.category_id,
      };

      let response;

      if (editingTransaction) {
        response = await supabase
          .from("transactions")
          .update(processedData)
          .eq("id", editingTransaction.id)
          .select("*, categories(name, color)")
          .single();

        if (response.error) {
          console.error("Error updating transaction:", response.error);
          toast.error(
            `Failed to update transaction: ${response.error.message}`
          );
          return;
        }

        const updatedTx = response.data as TransactionDataFromQuery;
        const mappedTx: SupabaseTransaction = {
          id: updatedTx.id,
          user_id: updatedTx.user_id || user.id,
          account_id: updatedTx.account_id,
          category_id: updatedTx.category_id || undefined,
          budget_id: updatedTx.budget_id || undefined,
          description: updatedTx.description || "",
          amount: parseFloat(updatedTx.amount as string),
          currency: updatedTx.currency || defaultCurrency,
          type:
            updatedTx.type === "income" || updatedTx.type === "expense"
              ? (updatedTx.type as "income" | "expense")
              : "expense",
          date: updatedTx.date || new Date().toISOString().split("T")[0],
          created_at: updatedTx.created_at || new Date().toISOString(),
          updated_at: updatedTx.updated_at || new Date().toISOString(),
          category_name: updatedTx.categories?.name,
          category_color: updatedTx.categories?.color ?? undefined,
        };

        setTransactions((prev) =>
          prev.map((t) => (t.id === mappedTx.id ? mappedTx : t))
        );

        toast.success("Transaction updated successfully!");
      } else {
        response = await supabase
          .from("transactions")
          .insert([processedData])
          .select("*, categories(name, color)")
          .single();

        if (response.error) {
          console.error("Error adding transaction:", response.error);
          if (response.error.code === "42501") {
            toast.error(
              "Permission denied. Check if you have access to add transactions."
            );
          } else {
            toast.error(`Failed to add transaction: ${response.error.message}`);
          }
          return;
        }

        const newTx = response.data as TransactionDataFromQuery;
        const mappedTx: SupabaseTransaction = {
          id: newTx.id,
          user_id: newTx.user_id || user.id,
          account_id: newTx.account_id,
          category_id: newTx.category_id || undefined,
          budget_id: newTx.budget_id || undefined,
          description: newTx.description || "",
          amount: parseFloat(newTx.amount as string),
          currency: newTx.currency || defaultCurrency,
          type:
            newTx.type === "income" || newTx.type === "expense"
              ? (newTx.type as "income" | "expense")
              : "expense",
          date: newTx.date || new Date().toISOString().split("T")[0],
          created_at: newTx.created_at || new Date().toISOString(),
          updated_at: newTx.updated_at || new Date().toISOString(),
          category_name: newTx.categories?.name,
          category_color: newTx.categories?.color ?? undefined,
        };

        setTransactions((prev) => [mappedTx, ...prev]);
        toast.success("Transaction added successfully!");
      }

      form.reset({
        description: "",
        amount: 0,
        type: "expense",
        date: new Date(),
        category_id: undefined,
        account_id: undefined,
        currency: defaultCurrency,
      });
      setEditingTransaction(null);
      setIsFormOpen(false);
    } catch (error) {
      console.error("Error submitting transaction:", error);
      toast.error("Failed to process transaction.");
    }
  };

  const handleEditTransaction = (transaction: SupabaseTransaction) => {
    setEditingTransaction(transaction);
    form.reset(mapTransactionToFormData(transaction));
    setIsFormOpen(true);
  };

  const handleDeleteTransaction = async (transactionId: string) => {
    if (!transactionId) {
      toast.error("Transaction ID is missing.");
      return;
    }
    if (confirm("Are you sure you want to delete this transaction?")) {
      try {
        const { error } = await supabase
          .from("transactions")
          .delete()
          .eq("id", transactionId);

        if (error) throw error;

        setTransactions((prev) => prev.filter((t) => t.id !== transactionId));
        toast.success("Transaction deleted successfully!");
      } catch (error) {
        console.error("Error deleting transaction:", error);
        toast.error("Failed to delete transaction.");
      }
    }
  };

  const calculateSummary = useCallback(() => {
    if (settingsLoading || !transactions.length) {
      return { income: 0, expenses: 0, balance: 0 };
    }

    const income = transactions
      .filter((t) => t.type === "income" && t.currency === defaultCurrency)
      .reduce((sum, t) => sum + t.amount, 0);
    const expenses = transactions
      .filter((t) => t.type === "expense" && t.currency === defaultCurrency)
      .reduce((sum, t) => sum + t.amount, 0);

    return { income, expenses, balance: income - expenses };
  }, [transactions, defaultCurrency, settingsLoading]);

  const summary = calculateSummary();

  if (isLoading || settingsLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="text-center text-muted-foreground">
          <h1 className="text-3xl font-bold">Loading transactions...</h1>
          <p className="text-lg">
            Please wait while we load your transactions.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-6 p-6 max-w-7xl mx-auto">
      <h1 className="text-3xl font-bold">Transactions</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
        <Card className="border-l-4 border-green-500 overflow-hidden relative group hover:shadow-lg transition-shadow duration-300">
          <div className="absolute top-0 right-0 w-16 h-16 bg-green-500/10 rounded-bl-full"></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Income</CardTitle>
            <Wallet className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-500">
              {formatCurrency(summary.income)}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Total income received
            </p>
          </CardContent>
        </Card>
        <Card className="border-l-4 border-red-500 overflow-hidden relative group hover:shadow-lg transition-shadow duration-300">
          <div className="absolute top-0 right-0 w-16 h-16 bg-red-500/10 rounded-bl-full"></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Expenses
            </CardTitle>
            <CreditCard className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-500">
              {formatCurrency(summary.expenses)}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Total expenses paid
            </p>
          </CardContent>
        </Card>
        <Card className="border-l-4 border-blue-500 overflow-hidden relative group hover:shadow-lg transition-shadow duration-300">
          <div className="absolute top-0 right-0 w-16 h-16 bg-blue-500/10 rounded-bl-full"></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Net Balance</CardTitle>
            <DollarSign className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-500">
              {formatCurrency(summary.balance)}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Current account balance
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Transactions</h1>
        <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
          <DialogTrigger asChild>
            <Button
              onClick={() => {
                setEditingTransaction(null);
                form.reset({
                  date: new Date(),
                  currency: defaultCurrency,
                  type: "expense",
                });
              }}
            >
              <PlusCircle className="mr-2 h-4 w-4" /> Add New Transaction
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[525px]">
            <DialogHeader>
              <DialogTitle>
                {editingTransaction ? "Edit" : "Add New"} Transaction
              </DialogTitle>
              <DialogDescription>
                {editingTransaction
                  ? "Update the details of your transaction."
                  : "Enter the details of your new transaction."}
              </DialogDescription>
            </DialogHeader>
            <form
              onSubmit={form.handleSubmit(onSubmitTransaction)}
              className="space-y-4"
            >
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="description">Description</Label>
                  <Input id="description" {...form.register("description")} />
                  {form.formState.errors.description && (
                    <p className="text-red-500 text-sm">
                      {form.formState.errors.description.message}
                    </p>
                  )}
                </div>
                <div>
                  <Label htmlFor="amount">Amount</Label>
                  <Input
                    id="amount"
                    type="number"
                    step="0.01"
                    {...form.register("amount")}
                  />
                  {form.formState.errors.amount && (
                    <p className="text-red-500 text-sm">
                      {form.formState.errors.amount.message}
                    </p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="type">Type</Label>
                  <Controller
                    name="type"
                    control={form.control}
                    render={({ field }) => (
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="income">Income</SelectItem>
                          <SelectItem value="expense">Expense</SelectItem>
                        </SelectContent>
                      </Select>
                    )}
                  />
                  {form.formState.errors.type && (
                    <p className="text-red-500 text-sm">
                      {form.formState.errors.type.message}
                    </p>
                  )}
                </div>
                <div>
                  <Label htmlFor="date">Date</Label>
                  <Controller
                    name="date"
                    control={form.control}
                    render={({ field }) => (
                      <DatePicker date={field.value} setDate={field.onChange} />
                    )}
                  />
                  {form.formState.errors.date && (
                    <p className="text-red-500 text-sm">
                      {form.formState.errors.date.message}
                    </p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="account_id">Account</Label>
                  <Controller
                    name="account_id"
                    control={form.control}
                    render={({ field }) => (
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select account" />
                        </SelectTrigger>
                        <SelectContent>
                          {accounts.map((acc) => (
                            <SelectItem key={acc.id} value={acc.id}>
                              {acc.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    )}
                  />
                  {form.formState.errors.account_id && (
                    <p className="text-red-500 text-sm">
                      {form.formState.errors.account_id.message}
                    </p>
                  )}
                </div>
                <div>
                  <Label htmlFor="category_id">Category (Optional)</Label>
                  <Controller
                    name="category_id"
                    control={form.control}
                    render={({ field }) => (
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="NO_CATEGORY_SELECTED">
                            Uncategorized
                          </SelectItem>
                          {categories.map((cat) => (
                            <SelectItem key={cat.id} value={cat.id}>
                              <div className="flex items-center space-x-2">
                                <CategoryIcon
                                  icon={cat.icon}
                                  className="h-4 w-4"
                                />
                                <span>{cat.name}</span>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    )}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="currency">Currency</Label>
                <Controller
                  name="currency"
                  control={form.control}
                  render={({ field }) => (
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select currency" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="USD">USD - US Dollar</SelectItem>
                        <SelectItem value="EUR">EUR - Euro</SelectItem>
                        <SelectItem value="GBP">GBP - British Pound</SelectItem>
                        <SelectItem value="JPY">JPY - Japanese Yen</SelectItem>
                        <SelectItem value="CAD">
                          CAD - Canadian Dollar
                        </SelectItem>
                        <SelectItem value="AUD">
                          AUD - Australian Dollar
                        </SelectItem>
                        <SelectItem value="CHF">CHF - Swiss Franc</SelectItem>
                        <SelectItem value="CNY">CNY - Chinese Yuan</SelectItem>
                        <SelectItem value="INR">INR - Indian Rupee</SelectItem>
                        <SelectItem value="BRL">
                          BRL - Brazilian Real
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  )}
                />
                {form.formState.errors.currency && (
                  <p className="text-red-500 text-sm">
                    {form.formState.errors.currency.message}
                  </p>
                )}
              </div>

              <DialogFooter>
                <DialogClose asChild>
                  <Button type="button" variant="outline">
                    Cancel
                  </Button>
                </DialogClose>
                <Button type="submit" disabled={form.formState.isSubmitting}>
                  {form.formState.isSubmitting
                    ? "Saving..."
                    : editingTransaction
                    ? "Save Changes"
                    : "Add Transaction"}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Recent Transactions</CardTitle>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-[calc(100vh-20rem)]">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Account</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {transactions.length === 0 ? (
                  <TableRow>
                    <TableCell
                      colSpan={7}
                      className="text-center text-muted-foreground h-24"
                    >
                      No transactions found. Add one to get started.
                    </TableCell>
                  </TableRow>
                ) : (
                  transactions.map((transaction) => {
                    const account = accounts.find(
                      (acc) => acc.id === transaction.account_id
                    );
                    return (
                      <TableRow key={transaction.id}>
                        <TableCell>
                          {new Date(transaction.date).toLocaleDateString()}
                        </TableCell>
                        <TableCell>{transaction.description}</TableCell>
                        <TableCell
                          className={`font-medium ${
                            transaction.type === "income"
                              ? "text-green-600"
                              : "text-red-600"
                          }`}
                        >
                          {transaction.type === "income" ? "+" : "-"}
                          {formatCurrency(
                            transaction.amount
                          )}
                        </TableCell>
                        <TableCell>
                          {transaction.category_name && (
                            <span
                              className="px-2 py-1 text-xs font-semibold rounded-full"
                              style={{
                                backgroundColor: transaction.category_color
                                  ? `${transaction.category_color}30`
                                  : "transparent",
                                color: transaction.category_color || "inherit",
                                border: transaction.category_color
                                  ? `1px solid ${transaction.category_color}`
                                  : "1px solid transparent",
                              }}
                            >
                              {transaction.category_name}
                            </span>
                          )}
                        </TableCell>
                        <TableCell>{account ? account.name : "N/A"}</TableCell>
                        <TableCell>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleEditTransaction(transaction)}
                            className="mr-2"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Dialog>
                            <DialogTrigger asChild>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="text-red-500 hover:text-red-700"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </DialogTrigger>
                            <DialogContent>
                              <DialogHeader>
                                <DialogTitle>Delete Transaction</DialogTitle>
                                <DialogDescription>
                                  Are you sure you want to delete this
                                  transaction? This action cannot be undone.
                                </DialogDescription>
                              </DialogHeader>
                              <DialogFooter>
                                <DialogClose asChild>
                                  <Button variant="outline">Cancel</Button>
                                </DialogClose>
                                <Button
                                  variant="destructive"
                                  onClick={() =>
                                    handleDeleteTransaction(transaction.id)
                                  }
                                >
                                  Delete
                                </Button>
                              </DialogFooter>
                            </DialogContent>
                          </Dialog>
                        </TableCell>
                      </TableRow>
                    );
                  })
                )}
              </TableBody>
            </Table>
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  );
}
