"use client";

import { useState, useEffect } from "react";
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/shared/components/ui/card";
import { <PERSON><PERSON> } from "@/shared/components/ui/button";
import { Badge } from "@/shared/components/ui/badge";
import { CategoryIcon } from "@/shared/components/ui/category-icon";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/shared/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/shared/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/shared/components/ui/select";
import { Input } from "@/shared/components/ui/input";
import { useForm, SubmitHandler } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Plus,
  Pie<PERSON>hart,
  ArrowUpRight,
  ArrowDownRight,
  Edit,
  Trash2,
  Tag,
  TrendingUp,
  Lightbulb,
  Activity,
  AlertCircle,
} from "lucide-react";
import { createClient } from "@/shared/services/supabase/client";
import { toast } from "sonner";
import { useAppMode } from "@/shared/contexts/AppModeContext";

// Define category types
type CategoryType = "income" | "expense";

// Define form schema
const categoryFormSchema = z.object({
  name: z.string().min(1, "Category name is required"),
  type: z.enum(["income", "expense"]),
  color: z.string().regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, {
    message: "Color must be a valid hex code",
  }),
  icon: z.string().optional(),
  parent_id: z.string().optional(),
});

// Infer type from schema
type CategoryFormValues = z.infer<typeof categoryFormSchema>;

// Category interface
interface Category {
  id: string;
  name: string;
  type: CategoryType;
  color: string;
  icon?: string;
  parent_id?: string;
  created_at: string;
  updated_at: string;
  usage_count?: number;
  total_amount?: number;
}

// Interface for predefined categories from database
interface PredefinedCategory {
  id: string;
  name: string;
  type: CategoryType;
  icon?: string;
  color: string;
  sort_order: number;
}

// Predefined colors for categories
const CATEGORY_COLORS = [
  "#0ea5e9", // Sky
  "#10b981", // Emerald
  "#f59e0b", // Amber
  "#ef4444", // Red
  "#8b5cf6", // Violet
  "#ec4899", // Pink
  "#f97316", // Orange
  "#14b8a6", // Teal
  "#6366f1", // Indigo
  "#84cc16", // Lime
];

export default function CategoriesPage() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [filter, setFilter] = useState<"all" | "income" | "expense">("all");
  const [predefinedCategories, setPredefinedCategories] = useState<
    PredefinedCategory[]
  >([]);
  const { isPersonalMode } = useAppMode();

  const form = useForm<CategoryFormValues>({
    resolver: zodResolver(categoryFormSchema),
    defaultValues: {
      name: "",
      type: "expense",
      color: CATEGORY_COLORS[0],
      icon: "",
      parent_id: "",
    },
  });

  useEffect(() => {
    fetchCategories();
    fetchPredefinedCategories();
  }, []);

  const fetchCategories = async () => {
    try {
      setLoading(true);
      const supabase = createClient();
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        toast.error("Please login to view categories");
        return;
      }

      // Fetch categories with usage statistics
      const { data: categoriesData, error: categoriesError } = await supabase
        .from("categories")
        .select("*")
        .eq("user_id", user.id)
        .order("name");

      if (categoriesError) throw categoriesError;

      // Fetch transaction counts and amounts per category
      const { data: usageData, error: usageError } = await supabase
        .from("transactions")
        .select("category_id, amount")
        .eq("user_id", user.id);

      if (usageError) throw usageError;

      // Calculate usage statistics
      const usageStats = new Map();
      (usageData || []).forEach((transaction) => {
        if (transaction.category_id) {
          const existing = usageStats.get(transaction.category_id) || {
            count: 0,
            total: 0,
          };
          existing.count += 1;
          existing.total += transaction.amount;
          usageStats.set(transaction.category_id, existing);
        }
      });

      // Cast the data to our Category interface with usage stats
      setCategories(
        (categoriesData || []).map((category) => {
          const usage = usageStats.get(category.id) || { count: 0, total: 0 };
          return {
            ...category,
            type: category.type as CategoryType,
            color: category.color || CATEGORY_COLORS[0],
            icon: category.icon || undefined,
            parent_id: category.parent_id || undefined,
            usage_count: usage.count,
            total_amount: usage.total,
          };
        })
      );
    } catch (error) {
      console.error("Error fetching categories:", error);
      toast.error("Failed to load categories");
    } finally {
      setLoading(false);
    }
  };

  const onSubmit: SubmitHandler<CategoryFormValues> = async (values) => {
    try {
      const supabase = createClient();
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        toast.error("Please login to create categories");
        return;
      }

      const categoryData = {
        user_id: user.id,
        name: values.name,
        type: values.type,
        color: values.color,
        icon: values.icon || null,
        parent_id: values.parent_id || null,
      };

      if (editingCategory) {
        const { error } = await supabase
          .from("categories")
          .update(categoryData)
          .eq("id", editingCategory.id);

        if (error) throw error;
        toast.success("Category updated successfully");
      } else {
        const { error } = await supabase
          .from("categories")
          .insert([categoryData]);

        if (error) throw error;
        toast.success("Category created successfully");
      }

      setDialogOpen(false);
      form.reset();
      setEditingCategory(null);
      fetchCategories();
    } catch (error) {
      console.error("Error saving category:", error);
      toast.error("Failed to save category");
    }
  };

  const handleEdit = (category: Category) => {
    setEditingCategory(category);
    form.reset({
      name: category.name,
      type: category.type,
      color: category.color || CATEGORY_COLORS[0],
      icon: category.icon || "",
      parent_id: category.parent_id || "",
    });
    setDialogOpen(true);
  };

  const handleDelete = async (categoryId: string) => {
    if (
      !confirm(
        "Are you sure you want to delete this category? This may affect your transactions."
      )
    )
      return;

    try {
      const supabase = createClient();
      const { error } = await supabase
        .from("categories")
        .delete()
        .eq("id", categoryId);

      if (error) throw error;
      toast.success("Category deleted successfully");
      fetchCategories();
    } catch (error) {
      console.error("Error deleting category:", error);
      toast.error("Failed to delete category");
    }
  };

  const getFilteredCategories = () => {
    if (filter === "all") return categories;
    return categories.filter((category) => category.type === filter);
  };

  const getCategoryIcon = (type: CategoryType) => {
    return type === "income" ? (
      <ArrowUpRight className="h-4 w-4 text-green-500" />
    ) : (
      <ArrowDownRight className="h-4 w-4 text-red-500" />
    );
  };

  const fetchPredefinedCategories = async () => {
    try {
      const supabase = createClient();
      const { data, error } = await supabase.rpc("get_predefined_categories");

      if (error) throw error;

      if (data && Array.isArray(data)) {
        setPredefinedCategories(
          data.map(
            (item: {
              id: string;
              name: string;
              type: string;
              icon: string;
              color: string;
              sort_order: number;
            }) => ({
              ...item,
              type: item.type as CategoryType,
            })
          )
        );
      }
    } catch (error) {
      console.error("Error fetching predefined categories:", error);
      // Don't show error toast as this is not critical
    }
  };

  const createPredefinedCategory = async (
    predefinedCategory: PredefinedCategory
  ) => {
    try {
      const supabase = createClient();
      const {
        data: { user },
      } = await supabase.auth.getUser();

      if (!user) {
        toast.error("Please login to create categories");
        return;
      }

      const categoryData = {
        user_id: user.id,
        name: predefinedCategory.name,
        type: predefinedCategory.type,
        color: predefinedCategory.color,
        icon: predefinedCategory.icon,
        parent_id: null,
      };

      const { error } = await supabase
        .from("categories")
        .insert([categoryData]);

      if (error) throw error;

      toast.success(`${predefinedCategory.name} category created successfully`);
      fetchCategories();
    } catch (error) {
      console.error("Error creating predefined category:", error);
      toast.error("Failed to create category");
    }
  };

  const addMissingDefaultCategories = async () => {
    try {
      const supabase = createClient();
      const { data, error } = await supabase.rpc(
        "add_missing_categories_for_current_user"
      );

      if (error) throw error;

      if (data && Array.isArray(data) && data.length > 0) {
        const result = data[0];
        if (result.categories_added > 0) {
          toast.success(result.message);
          fetchCategories();
        } else {
          toast.info(result.message);
        }
      }
    } catch (error) {
      console.error("Error adding missing categories:", error);
      toast.error("Failed to add missing categories");
    }
  };

  const getMostUsedCategories = () => {
    return categories
      .filter((cat) => (cat.usage_count || 0) > 0)
      .sort((a, b) => (b.usage_count || 0) - (a.usage_count || 0))
      .slice(0, 3);
  };

  const getUnusedCategories = () => {
    return categories.filter((cat) => (cat.usage_count || 0) === 0);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  const filteredCategories = getFilteredCategories();
  const incomeCategories = categories.filter((c) => c.type === "income").length;
  const expenseCategories = categories.filter(
    (c) => c.type === "expense"
  ).length;
  const mostUsedCategories = getMostUsedCategories();
  const unusedCategories = getUnusedCategories();

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {isPersonalMode ? "Personal Categories" : "Family Categories"}
          </h1>
          <p className="text-muted-foreground">
            {isPersonalMode
              ? "Organize your personal transactions with custom categories"
              : "Manage family group categories for shared expense tracking"}
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={addMissingDefaultCategories} variant="outline">
            <Lightbulb className="mr-2 h-4 w-4" />
            Add Default Categories
          </Button>
          <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                New Category
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
              <DialogHeader>
                <DialogTitle>
                  {editingCategory ? "Edit Category" : "Create New Category"}
                </DialogTitle>
                <DialogDescription>
                  Organize your transactions with custom categories
                </DialogDescription>
              </DialogHeader>
              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSubmit)}
                  className="space-y-4"
                >
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Category Name</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g., Groceries" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Type</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="income">Income</SelectItem>
                            <SelectItem value="expense">Expense</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="color"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Color</FormLabel>
                        <div className="grid grid-cols-5 gap-2">
                          {CATEGORY_COLORS.map((color) => (
                            <div
                              key={color}
                              className={`h-8 w-8 rounded-full cursor-pointer transition-all ${
                                field.value === color
                                  ? "ring-2 ring-primary ring-offset-2"
                                  : ""
                              }`}
                              style={{ backgroundColor: color }}
                              onClick={() => form.setValue("color", color)}
                            />
                          ))}
                        </div>
                        <FormControl>
                          <Input
                            type="text"
                            placeholder="#000000"
                            {...field}
                            className="mt-2"
                          />
                        </FormControl>
                        <FormDescription>
                          Select a color or enter a custom hex code
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="parent_id"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Parent Category (Optional)</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="No parent category" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {categories
                              .filter(
                                (c) =>
                                  c.type === form.getValues("type") &&
                                  (!editingCategory ||
                                    c.id !== editingCategory.id)
                              )
                              .map((category) => (
                                <SelectItem
                                  key={category.id}
                                  value={category.id}
                                >
                                  <div className="flex items-center space-x-2">
                                    <CategoryIcon icon={category.icon} className="h-4 w-4" />
                                    <span>{category.name}</span>
                                  </div>
                                </SelectItem>
                              ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Optionally nest this under another category
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="icon"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Icon (Optional)</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="e.g., shopping-cart, home, car"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Use Lucide icon names like: shopping-cart, home, car,
                          utensils, briefcase, etc.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <DialogFooter>
                    <Button type="submit">
                      {editingCategory ? "Update" : "Create"} Category
                    </Button>
                  </DialogFooter>
                </form>
              </Form>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Enhanced Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="border-l-4 border-blue-500 overflow-hidden relative group hover:shadow-lg transition-shadow duration-300">
          <div className="absolute top-0 right-0 w-16 h-16 bg-blue-500/10 rounded-bl-full"></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Categories
            </CardTitle>
            <Tag className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-500">
              {categories.length}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              {incomeCategories} income, {expenseCategories} expense
            </p>
          </CardContent>
        </Card>
        <Card className="border-l-4 border-green-500 overflow-hidden relative group hover:shadow-lg transition-shadow duration-300">
          <div className="absolute top-0 right-0 w-16 h-16 bg-green-500/10 rounded-bl-full"></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Income Categories
            </CardTitle>
            <ArrowUpRight className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-500">
              {incomeCategories}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Categories for tracking income
            </p>
          </CardContent>
        </Card>
        <Card className="border-l-4 border-red-500 overflow-hidden relative group hover:shadow-lg transition-shadow duration-300">
          <div className="absolute top-0 right-0 w-16 h-16 bg-red-500/10 rounded-bl-full"></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Expense Categories
            </CardTitle>
            <ArrowDownRight className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-500">
              {expenseCategories}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Categories for tracking expenses
            </p>
          </CardContent>
        </Card>
        <Card className="border-l-4 border-emerald-500 overflow-hidden relative group hover:shadow-lg transition-shadow duration-300">
          <div className="absolute top-0 right-0 w-16 h-16 bg-emerald-500/10 rounded-bl-full"></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Most Active</CardTitle>
            <Activity className="h-4 w-4 text-emerald-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-emerald-500">
              {mostUsedCategories.length > 0
                ? mostUsedCategories[0].usage_count
                : 0}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              {mostUsedCategories.length > 0
                ? `${mostUsedCategories[0].name} transactions`
                : "No activity yet"}
            </p>
          </CardContent>
        </Card>
        <Card className="border-l-4 border-amber-500 overflow-hidden relative group hover:shadow-lg transition-shadow duration-300">
          <div className="absolute top-0 right-0 w-16 h-16 bg-amber-500/10 rounded-bl-full"></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Unused Categories
            </CardTitle>
            <AlertCircle className="h-4 w-4 text-amber-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-amber-500">
              {unusedCategories.length}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Categories with no transactions
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Category Insights */}
      {categories.length > 0 && (
        <div className="grid gap-4 md:grid-cols-2">
          {mostUsedCategories.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Most Used Categories
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {mostUsedCategories.map((category, index) => (
                  <div
                    key={category.id}
                    className="flex items-center justify-between"
                  >
                    <div className="flex items-center gap-3">
                      <div className="flex items-center gap-2">
                        <span className="text-lg font-bold text-muted-foreground">
                          #{index + 1}
                        </span>
                        <div
                          className="h-3 w-3 rounded-full"
                          style={{ backgroundColor: category.color }}
                        />
                        <span className="font-medium">{category.name}</span>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold">
                        {category.usage_count} uses
                      </div>
                      <div className="text-xs text-muted-foreground">
                        ${(category.total_amount || 0).toLocaleString()}
                      </div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          )}

          {/* Only show if user has fewer categories than predefined available */}
          {predefinedCategories.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Lightbulb className="h-5 w-5" />
                  Add More Categories
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <p className="text-sm text-muted-foreground">
                    {categories.length === 0
                      ? "You already have default categories! Create custom ones or add more from our suggestions."
                      : "Add more categories to better organize your transactions"}
                  </p>

                  {/* Show available predefined categories that user doesn't have */}
                  {(() => {
                    const availableCategories = predefinedCategories.filter(
                      (predefined) =>
                        !categories.some(
                          (userCat) =>
                            userCat.name.toLowerCase() ===
                              predefined.name.toLowerCase() &&
                            userCat.type === predefined.type
                        )
                    );

                    if (availableCategories.length === 0) {
                      return (
                        <div className="text-center py-4">
                          <p className="text-sm text-muted-foreground">
                            You have all available predefined categories! Create
                            custom ones using the &quot;New Category&quot;
                            button.
                          </p>
                        </div>
                      );
                    }

                    const expenseCategories = availableCategories
                      .filter((cat) => cat.type === "expense")
                      .slice(0, 6);
                    const incomeCategories = availableCategories
                      .filter((cat) => cat.type === "income")
                      .slice(0, 4);

                    return (
                      <div className="space-y-3">
                        {expenseCategories.length > 0 && (
                          <div>
                            <h4 className="text-sm font-medium mb-2 text-red-600">
                              Expense Categories
                            </h4>
                            <div className="grid grid-cols-2 gap-2">
                              {expenseCategories.map((predefined) => (
                                <Button
                                  key={`${predefined.type}-${predefined.name}`}
                                  variant="outline"
                                  size="sm"
                                  className="justify-start h-auto py-2"
                                  onClick={() =>
                                    createPredefinedCategory(predefined)
                                  }
                                >
                                  <CategoryIcon
                                    icon={predefined.icon}
                                    className="mr-2 h-4 w-4"
                                  />
                                  {predefined.name}
                                </Button>
                              ))}
                            </div>
                          </div>
                        )}

                        {incomeCategories.length > 0 && (
                          <div>
                            <h4 className="text-sm font-medium mb-2 text-green-600">
                              Income Categories
                            </h4>
                            <div className="grid grid-cols-2 gap-2">
                              {incomeCategories.map((predefined) => (
                                <Button
                                  key={`${predefined.type}-${predefined.name}`}
                                  variant="outline"
                                  size="sm"
                                  className="justify-start h-auto py-2"
                                  onClick={() =>
                                    createPredefinedCategory(predefined)
                                  }
                                >
                                  <CategoryIcon
                                    icon={predefined.icon}
                                    className="mr-2 h-4 w-4"
                                  />
                                  {predefined.name}
                                </Button>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    );
                  })()}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* Filter Controls */}
      <div className="flex items-center space-x-2">
        <Button
          variant={filter === "all" ? "default" : "outline"}
          size="sm"
          onClick={() => setFilter("all")}
        >
          All
        </Button>
        <Button
          variant={filter === "income" ? "default" : "outline"}
          size="sm"
          onClick={() => setFilter("income")}
          className="text-green-500"
        >
          <ArrowUpRight className="mr-1 h-4 w-4" />
          Income
        </Button>
        <Button
          variant={filter === "expense" ? "default" : "outline"}
          size="sm"
          onClick={() => setFilter("expense")}
          className="text-red-500"
        >
          <ArrowDownRight className="mr-1 h-4 w-4" />
          Expense
        </Button>
      </div>

      {/* Categories List */}
      {filteredCategories.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-16">
            <PieChart className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No categories found</h3>
            <p className="text-muted-foreground text-center mb-4">
              {filter !== "all"
                ? `You don't have any ${filter} categories yet`
                : "Create your first category to organize your transactions"}
            </p>
            <Button onClick={() => setDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Create Category
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {filteredCategories.map((category) => (
            <Card
              key={category.id}
              className={`${
                (category.usage_count || 0) === 0 ? "opacity-60" : ""
              }`}
            >
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <div className="flex items-center space-x-2">
                  <div
                    className="h-4 w-4 rounded-full"
                    style={{ backgroundColor: category.color || "#cbd5e1" }}
                  />
                  <CardTitle className="text-base flex items-center gap-2">
                    <CategoryIcon icon={category.icon} />
                    {category.name}
                  </CardTitle>
                </div>
                <div className="flex gap-1">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8"
                    onClick={() => handleEdit(category)}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8"
                    onClick={() => handleDelete(category.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center justify-between">
                  <Badge
                    variant="outline"
                    className={
                      category.type === "income"
                        ? "text-green-500 border-green-200"
                        : "text-red-500 border-red-200"
                    }
                  >
                    {getCategoryIcon(category.type)}
                    <span className="ml-1 capitalize">{category.type}</span>
                  </Badge>
                  {category.parent_id && (
                    <Badge variant="secondary" className="text-xs">
                      Sub-category
                    </Badge>
                  )}
                </div>

                {/* Usage Statistics */}
                <div className="pt-2 border-t">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Usage:</span>
                    <span className="font-medium">
                      {category.usage_count || 0} transactions
                    </span>
                  </div>
                  {(category.total_amount || 0) > 0 && (
                    <div className="flex items-center justify-between text-sm mt-1">
                      <span className="text-muted-foreground">Total:</span>
                      <span className="font-medium">
                        ${(category.total_amount || 0).toLocaleString()}
                      </span>
                    </div>
                  )}
                  {(category.usage_count || 0) === 0 && (
                    <div className="flex items-center gap-1 text-xs text-amber-600 mt-1">
                      <AlertCircle className="h-3 w-3" />
                      Unused category
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
