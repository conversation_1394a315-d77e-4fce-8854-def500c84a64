"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/shared/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/shared/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/shared/components/ui/select";
import { Button } from "@/shared/components/ui/button";
import { Input } from "@/shared/components/ui/input";
import {
  Loader2,
  Tag,
  ShoppingCart,
  Home,
  Car,
  Utensils,
  GraduationCap,
  Briefcase,
  Plane,
  Zap,
  HeartPulse,
  Clapperboard,
  ShoppingBag,
  Smartphone,
  Sparkles,
  Shield,
  Calculator,
  CreditCard,
  Gift,
  Package,
  Laptop,
  Building2,
  TrendingUp,
  Landmark,
  Coins,
  Target,
  Undo2,
  Banknote,
} from "lucide-react";
import { toast } from "sonner";
import {
  createFamilyGroupCategory,
  getFamilyGroupCategories,
  type FamilyGroupCategory,
} from "@/features/family-groups/services/family-finances";

const categorySchema = z.object({
  name: z.string().min(1, "Category name is required"),
  type: z.enum(["income", "expense"]),
  icon: z.string().optional(),
  color: z.string().optional(),
  parent_id: z.string().optional(),
});

type CategoryFormValues = z.infer<typeof categorySchema>;

interface AddCategoryDialogProps {
  groupId: string;
  children: React.ReactNode;
}

const categoryIcons = [
  // Essential Categories
  { value: "shopping-cart", label: "Groceries", icon: ShoppingCart },
  { value: "car", label: "Transportation", icon: Car },
  { value: "zap", label: "Utilities", icon: Zap },
  { value: "home", label: "Housing", icon: Home },
  { value: "heart-pulse", label: "Healthcare", icon: HeartPulse },
  
  // Lifestyle Categories
  { value: "utensils", label: "Dining Out", icon: Utensils },
  { value: "clapperboard", label: "Entertainment", icon: Clapperboard },
  { value: "shopping-bag", label: "Shopping", icon: ShoppingBag },
  { value: "smartphone", label: "Subscriptions", icon: Smartphone },
  { value: "sparkles", label: "Personal Care", icon: Sparkles },
  
  // Financial Categories
  { value: "shield", label: "Insurance", icon: Shield },
  { value: "calculator", label: "Taxes", icon: Calculator },
  { value: "credit-card", label: "Debt Payment", icon: CreditCard },
  
  // Other Categories
  { value: "gift", label: "Gifts", icon: Gift },
  { value: "plane", label: "Travel", icon: Plane },
  { value: "graduation-cap", label: "Education", icon: GraduationCap },
  { value: "package", label: "Miscellaneous", icon: Package },
  
  // Income Categories
  { value: "briefcase", label: "Salary", icon: Briefcase },
  { value: "laptop", label: "Freelancing", icon: Laptop },
  { value: "building-2", label: "Business", icon: Building2 },
  { value: "trending-up", label: "Investments", icon: TrendingUp },
  { value: "landmark", label: "Interest", icon: Landmark },
  { value: "coins", label: "Dividends", icon: Coins },
  { value: "target", label: "Bonus", icon: Target },
  { value: "undo-2", label: "Refunds", icon: Undo2 },
  { value: "banknote", label: "Other Income", icon: Banknote },
  
  // General
  { value: "tag", label: "Default", icon: Tag },
];

const colors = [
  "#3B82F6",
  "#EF4444",
  "#10B981",
  "#F59E0B",
  "#8B5CF6",
  "#EC4899",
  "#06B6D4",
  "#84CC16",
  "#F97316",
  "#6366F1",
  "#14B8A6",
  "#F43F5E",
];

const categoryTypes = [
  { value: "income", label: "Income" },
  { value: "expense", label: "Expense" },
];

export default function AddCategoryDialog({
  groupId,
  children,
}: AddCategoryDialogProps) {
  const [open, setOpen] = useState(false);
  const queryClient = useQueryClient();

  const form = useForm<CategoryFormValues>({
    resolver: zodResolver(categorySchema),
    defaultValues: {
      name: "",
      type: "expense",
      icon: "tag",
      color: colors[0],
      parent_id: undefined, // Explicitly set for clarity, though it's the default
    },
  });

  // Fetch existing categories for parent selection
  const { data: categories } = useQuery<FamilyGroupCategory[]>({
    queryKey: ["familyGroupCategories", groupId],
    queryFn: () => getFamilyGroupCategories(groupId),
    enabled: !!groupId && open,
  });

  const createCategoryMutation = useMutation({
    mutationFn: (data: CategoryFormValues) =>
      createFamilyGroupCategory(groupId, data),
    onSuccess: () => {
      toast.success("Category created successfully!");
      form.reset();
      setOpen(false);
      queryClient.invalidateQueries({
        queryKey: ["familyGroupCategories", groupId],
      });
    },
    onError: (error) => {
      toast.error(error.message, {
        description: "Please check the category details and try again",
        action: {
          label: "Retry",
          onClick: () => onSubmit(form.getValues()),
        },
      });
    },
  });

  const onSubmit = (data: CategoryFormValues) => {
    const submissionData = {
      ...data,
      parent_id: data.parent_id === "@none@" ? undefined : data.parent_id,
    };
    createCategoryMutation.mutate(submissionData);
  };

  const selectedIcon = form.watch("icon");
  const selectedColor = form.watch("color");
  const selectedType = form.watch("type");
  const parentCategories = categories?.filter(
    (c) => c.type === selectedType && !c.parent_id
  );

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Tag className="h-5 w-5" />
            <span>Add Category</span>
          </DialogTitle>
          <DialogDescription>
            Create a new category to organize your family&apos;s transactions.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Category Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="e.g., Groceries, Salary, Utilities"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Category Type</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select category type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {categoryTypes.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Choose whether this category is for income or expenses
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="parent_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Parent Category (Optional)</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select parent category or leave blank" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="@none@">
                        No Parent (Top Level)
                      </SelectItem>
                      {parentCategories?.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          <div className="flex items-center space-x-2">
                            {category.icon && (
                              <div 
                                className="w-4 h-4 rounded flex items-center justify-center"
                                style={{ backgroundColor: category.color }}
                              >
                                {(() => {
                                  const iconData = categoryIcons.find(icon => icon.value === category.icon);
                                  const IconComponent = iconData?.icon || Tag;
                                  return <IconComponent className="h-3 w-3 text-white" />;
                                })()}
                              </div>
                            )}
                            <span>{category.name}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Create a subcategory under an existing category
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="icon"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Icon</FormLabel>
                  <FormControl>
                    <div className="max-h-40 overflow-y-auto">
                      <div className="grid grid-cols-6 gap-2">
                        {categoryIcons.map((iconOption) => (
                          <button
                            key={iconOption.value}
                            type="button"
                            className={`p-2 border rounded-lg hover:bg-muted transition-colors ${
                              selectedIcon === iconOption.value
                                ? "border-primary bg-primary/10"
                                : "border-border"
                            }`}
                            onClick={() => field.onChange(iconOption.value)}
                            title={iconOption.label}
                          >
                            <iconOption.icon className="h-4 w-4 mx-auto" />
                          </button>
                        ))}
                      </div>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="color"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Color</FormLabel>
                  <FormControl>
                    <div className="flex flex-wrap gap-2">
                      {colors.map((color) => (
                        <button
                          key={color}
                          type="button"
                          className={`w-8 h-8 rounded-full border-2 ${
                            selectedColor === color
                              ? "border-gray-900 dark:border-gray-100"
                              : "border-gray-300"
                          }`}
                          style={{ backgroundColor: color }}
                          onClick={() => field.onChange(color)}
                        />
                      ))}
                    </div>
                  </FormControl>
                  <FormDescription>
                    Choose a color to help identify this category
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Preview */}
            <div className="p-4 border rounded-lg bg-muted/50">
              <p className="text-sm font-medium mb-2">Category Preview:</p>
              <div className="flex items-center space-x-3">
                <div
                  className="w-10 h-10 rounded-lg flex items-center justify-center"
                  style={{ backgroundColor: selectedColor }}
                >
                  {(() => {
                    const iconData = categoryIcons.find(icon => icon.value === selectedIcon);
                    const IconComponent = iconData?.icon || Tag;
                    return <IconComponent className="h-5 w-5 text-white" />;
                  })()}
                </div>
                <div>
                  <p className="font-medium">
                    {form.watch("name") || "Category Name"}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {categoryTypes.find((t) => t.value === selectedType)?.label}
                    {form.watch("parent_id") &&
                      parentCategories &&
                      ` • Subcategory of ${
                        parentCategories.find(
                          (c) => c.id === form.watch("parent_id")
                        )?.name
                      }`}
                  </p>
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setOpen(false)}
                disabled={createCategoryMutation.isPending}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={createCategoryMutation.isPending}
                className="min-w-[100px]"
              >
                {createCategoryMutation.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating...
                  </>
                ) : (
                  <>
                    <Tag className="mr-2 h-4 w-4" />
                    Create Category
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
