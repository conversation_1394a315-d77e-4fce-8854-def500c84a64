"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/shared/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/shared/components/ui/form";

import { Button } from "@/shared/components/ui/button";
import { Input } from "@/shared/components/ui/input";
import { Calendar } from "@/shared/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/shared/components/ui/popover";
import {
  Loader2,
  CalendarIcon,
  Target,
  DollarSign,
  Home,
  Car,
  Plane,
  GraduationCap,
  Heart,
  Gift,
} from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/shared/utils";
import { toast } from "sonner";
import { createFamilyGroupGoal } from "@/features/family-groups/services/family-finances";

const goalSchema = z.object({
  name: z.string().min(1, "Goal name is required"),
  target_amount: z.number().min(0.01, "Target amount must be greater than 0"),
  current_amount: z.number().min(0, "Current amount must be positive"),
  target_date: z.date().optional(),
  icon: z.string().optional(),
  color: z.string().optional(),
});

type GoalFormValues = z.infer<typeof goalSchema>;

interface AddGoalDialogProps {
  groupId: string;
  children: React.ReactNode;
}

const goalIcons = [
  { value: "home", label: "House", icon: Home },
  { value: "car", label: "Car", icon: Car },
  { value: "plane", label: "Vacation", icon: Plane },
  { value: "graduation-cap", label: "Education", icon: GraduationCap },
  { value: "heart", label: "Wedding", icon: Heart },
  { value: "gift", label: "Gift", icon: Gift },
  { value: "piggy-bank", label: "Emergency Fund", icon: Target },
  { value: "smartphone", label: "Electronics", icon: Target },
];

const colors = [
  "#3B82F6",
  "#EF4444",
  "#10B981",
  "#F59E0B",
  "#8B5CF6",
  "#EC4899",
  "#06B6D4",
  "#84CC16",
  "#F97316",
  "#6366F1",
  "#14B8A6",
  "#F43F5E",
];

export default function AddGoalDialog({
  groupId,
  children,
}: AddGoalDialogProps) {
  const [open, setOpen] = useState(false);
  const queryClient = useQueryClient();

  const form = useForm<GoalFormValues>({
    resolver: zodResolver(goalSchema),
    defaultValues: {
      name: "",
      target_amount: 0,
      current_amount: 0,
      icon: "target",
      color: colors[0],
    },
  });

  const createGoalMutation = useMutation({
    mutationFn: (data: GoalFormValues) =>
      createFamilyGroupGoal(groupId, {
        name: data.name,
        target_amount: data.target_amount,
        current_amount: data.current_amount,
        target_date: data.target_date?.toISOString(),
        icon: data.icon,
        color: data.color,
        status: "active",
      }),
    onSuccess: () => {
      toast.success("Goal created successfully!");
      form.reset();
      setOpen(false);
      queryClient.invalidateQueries({
        queryKey: ["familyGroupGoals", groupId],
      });
      queryClient.invalidateQueries({
        queryKey: ["familyGroupFinancialSummary", groupId],
      });
    },
    onError: (error) => {
      toast.error(`Failed to create goal: ${error.message}`);
    },
  });

  const onSubmit = (data: GoalFormValues) => {
    createGoalMutation.mutate(data);
  };

  const selectedIcon = form.watch("icon");
  const selectedColor = form.watch("color");
  const targetAmount = form.watch("target_amount");
  const currentAmount = form.watch("current_amount");
  const progress = targetAmount > 0 ? (currentAmount / targetAmount) * 100 : 0;

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Target className="h-5 w-5" />
            <span>Create Financial Goal</span>
          </DialogTitle>
          <DialogDescription>
            Set a savings target for your family to work towards together.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Goal Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="e.g., Family Vacation, Emergency Fund"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="target_amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Target Amount</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <DollarSign className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                        <Input
                          type="number"
                          step="0.01"
                          placeholder="0.00"
                          className="pl-9"
                          {...field}
                          onChange={(e) =>
                            field.onChange(parseFloat(e.target.value) || 0)
                          }
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="current_amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Current Amount</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <DollarSign className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                        <Input
                          type="number"
                          step="0.01"
                          placeholder="0.00"
                          className="pl-9"
                          {...field}
                          onChange={(e) =>
                            field.onChange(parseFloat(e.target.value) || 0)
                          }
                        />
                      </div>
                    </FormControl>
                    <FormDescription>
                      How much you&apos;ve already saved
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="target_date"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Target Date (Optional)</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full pl-3 text-left font-normal",
                            !field.value && "text-muted-foreground"
                          )}
                        >
                          {field.value ? (
                            format(field.value, "PPP")
                          ) : (
                            <span>No target date</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={field.onChange}
                        disabled={(date) => date < new Date()}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <FormDescription>
                    When do you want to achieve this goal?
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="icon"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Icon</FormLabel>
                  <FormControl>
                    <div className="grid grid-cols-4 gap-2">
                      {goalIcons.map((iconOption) => (
                        <button
                          key={iconOption.value}
                          type="button"
                          className={`p-3 text-2xl border rounded-lg hover:bg-muted ${
                            selectedIcon === iconOption.value
                              ? "border-primary bg-primary/10"
                              : "border-border"
                          }`}
                          onClick={() => field.onChange(iconOption.value)}
                        >
                          {iconOption.value}
                        </button>
                      ))}
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="color"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Color</FormLabel>
                  <FormControl>
                    <div className="flex flex-wrap gap-2">
                      {colors.map((color) => (
                        <button
                          key={color}
                          type="button"
                          className={`w-8 h-8 rounded-full border-2 ${
                            selectedColor === color
                              ? "border-gray-900 dark:border-gray-100"
                              : "border-gray-300"
                          }`}
                          style={{ backgroundColor: color }}
                          onClick={() => field.onChange(color)}
                        />
                      ))}
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Preview */}
            <div className="p-4 border rounded-lg bg-muted/50">
              <p className="text-sm font-medium mb-3">Goal Preview:</p>
              <div className="flex items-center space-x-3 mb-3">
                <div
                  className="w-12 h-12 rounded-lg flex items-center justify-center text-xl"
                  style={{ backgroundColor: selectedColor }}
                >
                  {selectedIcon}
                </div>
                <div className="flex-1">
                  <p className="font-medium">
                    {form.watch("name") || "Goal Name"}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    ${currentAmount.toFixed(2)} of ${targetAmount.toFixed(2)}
                    {form.watch("target_date") &&
                      ` by ${format(form.watch("target_date")!, "MMM yyyy")}`}
                  </p>
                </div>
              </div>
              <div className="space-y-1">
                <div className="flex justify-between text-sm">
                  <span>Progress</span>
                  <span>{progress.toFixed(1)}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="h-2 rounded-full transition-all duration-300"
                    style={{
                      width: `${Math.min(progress, 100)}%`,
                      backgroundColor: selectedColor,
                    }}
                  />
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setOpen(false)}
                disabled={createGoalMutation.isPending}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={createGoalMutation.isPending}
                className="min-w-[100px]"
              >
                {createGoalMutation.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating...
                  </>
                ) : (
                  <>
                    <Target className="mr-2 h-4 w-4" />
                    Create Goal
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
