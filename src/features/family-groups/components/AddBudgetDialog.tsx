"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/shared/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/shared/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/shared/components/ui/select";
import { Button } from "@/shared/components/ui/button";
import { Input } from "@/shared/components/ui/input";
import { Calendar } from "@/shared/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/shared/components/ui/popover";
import { Loader2, CalendarIcon, Target, DollarSign } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/shared/utils";
import { toast } from "sonner";
import {
  createFamilyGroupBudget,
  getFamilyGroupCategories,
  type FamilyGroupCategory,
} from "@/features/family-groups/services/family-finances";

const budgetSchema = z.object({
  name: z.string().min(1, "Budget name is required"),
  category_id: z.string().optional(),
  amount: z.number().min(0.01, "Budget amount must be greater than 0"),
  period: z.enum(["weekly", "monthly", "quarterly", "yearly"]),
  start_date: z.date(),
  end_date: z.date().optional(),
});

type BudgetFormValues = z.infer<typeof budgetSchema>;

interface AddBudgetDialogProps {
  groupId: string;
  children: React.ReactNode;
}

const periods = [
  { value: "weekly", label: "Weekly" },
  { value: "monthly", label: "Monthly" },
  { value: "quarterly", label: "Quarterly" },
  { value: "yearly", label: "Yearly" },
];

export default function AddBudgetDialog({
  groupId,
  children,
}: AddBudgetDialogProps) {
  const [open, setOpen] = useState(false);
  const queryClient = useQueryClient();

  const form = useForm<BudgetFormValues>({
    resolver: zodResolver(budgetSchema),
    defaultValues: {
      name: "",
      amount: 0,
      period: "monthly",
      start_date: new Date(),
    },
  });

  // Fetch categories
  const { data: categories } = useQuery<FamilyGroupCategory[]>({
    queryKey: ["familyGroupCategories", groupId],
    queryFn: () => getFamilyGroupCategories(groupId),
    enabled: !!groupId && open,
  });

  const createBudgetMutation = useMutation({
    mutationFn: (data: BudgetFormValues) =>
      createFamilyGroupBudget(groupId, {
        name: data.name,
        category_id: data.category_id && data.category_id !== 'none' ? data.category_id : undefined,
        amount: data.amount,
        period: data.period,
        start_date: data.start_date.toISOString(),
        end_date: data.end_date?.toISOString(),
      }),
    onSuccess: () => {
      toast.success("Budget created successfully!");
      form.reset();
      setOpen(false);
      queryClient.invalidateQueries({
        queryKey: ["familyGroupBudgets", groupId],
      });
      queryClient.invalidateQueries({
        queryKey: ["familyGroupFinancialSummary", groupId],
      });
    },
    onError: (error) => {
      toast.error(`Failed to create budget: ${error.message}`);
    },
  });

  const onSubmit = (data: BudgetFormValues) => {
    createBudgetMutation.mutate(data);
  };

  const selectedCategory = categories?.find(
    (c) => c.id === form.watch("category_id")
  );

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Target className="h-5 w-5" />
            <span>Create Budget</span>
          </DialogTitle>
          <DialogDescription>
            Set spending limits for categories to help manage your family&apos;s
            finances.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Budget Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="e.g., Monthly Groceries, Entertainment Budget"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Give your budget a descriptive name
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="category_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Category (Optional)</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a category or leave blank for general budget" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="none">
                        General Budget (All Categories)
                      </SelectItem>
                      {categories?.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          <div className="flex items-center space-x-2">
                            {category.icon && <span>{category.icon}</span>}
                            <span>{category.name}</span>
                            <span className="text-xs text-muted-foreground">
                              ({category.type})
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Choose a specific category or leave blank for a general
                    budget
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Budget Amount</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <DollarSign className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                        <Input
                          type="number"
                          step="0.01"
                          placeholder="0.00"
                          className="pl-9"
                          {...field}
                          onChange={(e) =>
                            field.onChange(parseFloat(e.target.value) || 0)
                          }
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="period"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Period</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select period" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {periods.map((period) => (
                          <SelectItem key={period.value} value={period.value}>
                            {period.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="start_date"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Start Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) => date < new Date("1900-01-01")}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="end_date"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>End Date (Optional)</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>No end date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date < form.getValues("start_date") ||
                            date < new Date("1900-01-01")
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormDescription>
                      Leave blank for ongoing budget
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Preview */}
            <div className="p-4 border rounded-lg bg-muted/50">
              <p className="text-sm font-medium mb-2">Budget Preview:</p>
              <div className="space-y-1">
                <p className="font-medium">
                  {form.watch("name") || "Unnamed Budget"} - $
                  {form.watch("amount").toFixed(2)}
                </p>
                <p className="text-sm text-muted-foreground">
                  Category:{" "}
                  {selectedCategory ? selectedCategory.name : "General Budget"}
                </p>
                <p className="text-sm text-muted-foreground">
                  {periods.find((p) => p.value === form.watch("period"))?.label}{" "}
                  budget
                  {form.watch("start_date") &&
                    ` starting ${format(
                      form.watch("start_date"),
                      "MMM d, yyyy"
                    )}`}
                  {form.watch("end_date") &&
                    ` until ${format(form.watch("end_date")!, "MMM d, yyyy")}`}
                </p>
              </div>
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setOpen(false)}
                disabled={createBudgetMutation.isPending}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={createBudgetMutation.isPending}
                className="min-w-[100px]"
              >
                {createBudgetMutation.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating...
                  </>
                ) : (
                  <>
                    <Target className="mr-2 h-4 w-4" />
                    Create Budget
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
