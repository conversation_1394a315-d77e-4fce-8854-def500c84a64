"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/shared/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/shared/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/shared/components/ui/select";
import { Button } from "@/shared/components/ui/button";
import { Input } from "@/shared/components/ui/input";
import { Textarea } from "@/shared/components/ui/textarea";
import { Badge } from "@/shared/components/ui/badge";
import { CategoryIcon } from "@/shared/components/ui/category-icon";
import { Calendar } from "@/shared/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/shared/components/ui/popover";
import {
  PlusCircle,
  Loader2,
  CalendarIcon,
  DollarSign,
  TrendingUp,
  TrendingDown,
  Repeat,
  Sparkles,
} from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/shared/utils";
import { toast } from "sonner";
import {
  getFamilyGroupAccounts,
  getFamilyGroupCategories,
  createFamilyGroupTransaction,
  type FamilyGroupAccount,
  type FamilyGroupCategory,
} from "@/features/family-groups/services/family-finances";

const transactionSchema = z.object({
  account_id: z.string().min(1, "Account is required"),
  category_id: z.string().optional(),
  amount: z.number().min(0.01, "Amount must be greater than 0"),
  type: z.enum(["income", "expense", "transfer"]),
  description: z.string().min(1, "Description is required"),
  date: z.date(),
  notes: z.string().optional(),
});

type TransactionFormValues = z.infer<typeof transactionSchema>;

interface AddTransactionDialogProps {
  groupId: string;
  children: React.ReactNode;
}

// Smart categorization suggestions based on description
const suggestCategory = (
  description: string,
  categories: FamilyGroupCategory[]
): FamilyGroupCategory | null => {
  const desc = description.toLowerCase();

  // Common patterns for auto-categorization
  const patterns = [
    {
      keywords: ["grocery", "supermarket", "food", "walmart", "target"],
      type: "expense",
      name: "groceries",
    },
    {
      keywords: ["gas", "fuel", "shell", "exxon", "bp"],
      type: "expense",
      name: "transportation",
    },
    {
      keywords: ["restaurant", "cafe", "pizza", "mcdonald", "starbucks"],
      type: "expense",
      name: "dining",
    },
    {
      keywords: ["salary", "paycheck", "wage", "income"],
      type: "income",
      name: "salary",
    },
    {
      keywords: ["electric", "water", "internet", "phone", "utility"],
      type: "expense",
      name: "utilities",
    },
    {
      keywords: ["rent", "mortgage", "housing"],
      type: "expense",
      name: "housing",
    },
    {
      keywords: ["doctor", "hospital", "pharmacy", "medical"],
      type: "expense",
      name: "healthcare",
    },
    {
      keywords: ["netflix", "spotify", "subscription"],
      type: "expense",
      name: "entertainment",
    },
  ];

  for (const pattern of patterns) {
    if (pattern.keywords.some((keyword) => desc.includes(keyword))) {
      const matchingCategory = categories.find(
        (cat) =>
          cat.type === pattern.type &&
          cat.name.toLowerCase().includes(pattern.name)
      );
      if (matchingCategory) return matchingCategory;
    }
  }

  return null;
};

export default function AddTransactionDialog({
  groupId,
  children,
}: AddTransactionDialogProps) {
  const [open, setOpen] = useState(false);
  const [suggestedCategory, setSuggestedCategory] =
    useState<FamilyGroupCategory | null>(null);
  const queryClient = useQueryClient();

  const form = useForm<TransactionFormValues>({
    resolver: zodResolver(transactionSchema),
    defaultValues: {
      amount: 0,
      type: "expense" as const,
      date: new Date(),
    },
  });

  // Fetch accounts and categories
  const { data: accounts } = useQuery<FamilyGroupAccount[]>({
    queryKey: ["familyGroupAccounts", groupId],
    queryFn: () => getFamilyGroupAccounts(groupId),
    enabled: !!groupId,
  });

  const { data: categories } = useQuery<FamilyGroupCategory[]>({
    queryKey: ["familyGroupCategories", groupId],
    queryFn: () => getFamilyGroupCategories(groupId),
    enabled: !!groupId,
  });

  // Create transaction mutation
  const createTransactionMutation = useMutation({
    mutationFn: (data: TransactionFormValues) =>
      createFamilyGroupTransaction(groupId, {
        account_id: data.account_id,
        category_id: data.category_id,
        amount:
          data.type === "expense"
            ? -Math.abs(data.amount)
            : Math.abs(data.amount),
        type: data.type,
        description: data.description,
        date: data.date.toISOString(),
        notes: data.notes,
      }),
    onSuccess: () => {
      toast.success("Transaction added successfully!");
      form.reset();
      setOpen(false);
      setSuggestedCategory(null);
      // Invalidate relevant queries
      queryClient.invalidateQueries({
        queryKey: ["familyGroupTransactions", groupId],
      });
      queryClient.invalidateQueries({
        queryKey: ["familyGroupFinancialSummary", groupId],
      });
      queryClient.invalidateQueries({
        queryKey: ["familyGroupAccounts", groupId],
      });
    },
    onError: (error) => {
      toast.error(`Failed to add transaction: ${error.message}`);
    },
  });

  // Watch description field for smart categorization
  const description = form.watch("description");
  React.useEffect(() => {
    if (description && categories && description.length > 3) {
      const suggested = suggestCategory(description, categories);
      setSuggestedCategory(suggested);
      if (suggested && !form.getValues("category_id")) {
        form.setValue("category_id", suggested.id);
      }
    }
  }, [description, categories, form]);

  const onSubmit = (data: TransactionFormValues) => {
    createTransactionMutation.mutate(data);
  };

  const getTransactionTypeIcon = (type: string) => {
    switch (type) {
      case "income":
        return <TrendingUp className="h-4 w-4 text-green-600" />;
      case "expense":
        return <TrendingDown className="h-4 w-4 text-red-600" />;
      case "transfer":
        return <Repeat className="h-4 w-4 text-blue-600" />;
      default:
        return <DollarSign className="h-4 w-4" />;
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <PlusCircle className="h-5 w-5" />
            <span>Add New Transaction</span>
          </DialogTitle>
          <DialogDescription>
            Add a new transaction to your family group&apos;s financial records.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Transaction Type */}
            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Transaction Type</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select transaction type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="income">
                        <div className="flex items-center space-x-2">
                          <TrendingUp className="h-4 w-4 text-green-600" />
                          <span>Income</span>
                        </div>
                      </SelectItem>
                      <SelectItem value="expense">
                        <div className="flex items-center space-x-2">
                          <TrendingDown className="h-4 w-4 text-red-600" />
                          <span>Expense</span>
                        </div>
                      </SelectItem>
                      <SelectItem value="transfer">
                        <div className="flex items-center space-x-2">
                          <Repeat className="h-4 w-4 text-blue-600" />
                          <span>Transfer</span>
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Account */}
              <FormField
                control={form.control}
                name="account_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Account</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select account" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {accounts?.map((account) => (
                          <SelectItem key={account.id} value={account.id}>
                            <div className="flex items-center justify-between w-full">
                              <span>{account.name}</span>
                              <span className="text-sm text-muted-foreground ml-2">
                                {new Intl.NumberFormat("en-US", {
                                  style: "currency",
                                  currency: account.currency,
                                }).format(account.balance)}
                              </span>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Amount */}
              <FormField
                control={form.control}
                name="amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Amount</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <DollarSign className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          placeholder="0.00"
                          className="pl-9"
                          {...field}
                          onChange={(e) =>
                            field.onChange(parseFloat(e.target.value) || 0)
                          }
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Description with Smart Categorization */}
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter transaction description"
                      {...field}
                    />
                  </FormControl>
                  {suggestedCategory && (
                    <div className="flex items-center space-x-2 mt-2">
                      <Sparkles className="h-4 w-4 text-yellow-500" />
                      <span className="text-sm text-muted-foreground">
                        Smart suggestion:
                      </span>
                      <Badge variant="secondary" className="text-xs">
                        {suggestedCategory.name}
                      </Badge>
                    </div>
                  )}
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Category */}
              <FormField
                control={form.control}
                name="category_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Category</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {categories
                          ?.filter((cat) => cat.type === form.watch("type"))
                          .map((category) => (
                            <SelectItem key={category.id} value={category.id}>
                              <div className="flex items-center space-x-2">
                                <CategoryIcon icon={category.icon} className="h-4 w-4" />
                                <span>{category.name}</span>
                              </div>
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Date */}
              <FormField
                control={form.control}
                name="date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date > new Date() || date < new Date("1900-01-01")
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Notes */}
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Notes (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Add any additional notes about this transaction"
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Future: Recurring Transaction Toggle */}

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setOpen(false)}
                disabled={createTransactionMutation.isPending}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={createTransactionMutation.isPending}
                className="min-w-[100px]"
              >
                {createTransactionMutation.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Adding...
                  </>
                ) : (
                  <>
                    {getTransactionTypeIcon(form.watch("type"))}
                    <span className="ml-2">Add Transaction</span>
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
