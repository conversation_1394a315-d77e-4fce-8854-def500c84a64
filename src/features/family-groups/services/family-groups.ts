import { createClient } from "@/shared/services/supabase/client";
import { inviteToFamilyGroupWithEmail } from "@/shared/services/supabase/server-actions";
import {
  type FamilyGroupDetails,
  type FamilyGroupMember,
  type PendingInvitation,
  type RespondToInvitationResponse,
  type UserFamilyGroupRpcResponseItem,
  type FamilyGroupInvitation,
} from "@/shared/types/family";
import { type PostgrestError } from "@supabase/supabase-js";

const supabase = createClient();

/**
 * Fetches the family groups for the currently authenticated user.
 */
export async function getUserFamilyGroups(): Promise<
  UserFamilyGroupRpcResponseItem[]
> {
  const { data, error } = await supabase.rpc("get_user_family_groups");

  if (error) {
    console.error("Error fetching user family groups:", error);
    throw new Error(error.message);
  }
  return (data as UserFamilyGroupRpcResponseItem[]) || [];
}

/**
 * Creates a new family group.
 * @param groupName The name of the new family group.
 * @returns The ID of the newly created group.
 */
export async function createFamilyGroup(groupName: string): Promise<string> {
  const { data, error } = await supabase.rpc("create_family_group", {
    p_group_name: groupName,
  });

  if (error) {
    console.error("Error creating family group:", error);
    throw new Error(error.message);
  }
  if (typeof data === "string") {
    return data;
  }
  console.warn(
    "Create family group RPC did not return an ID in the expected string format:",
    data
  );
  throw new Error("Failed to create group or extract ID.");
}

/**
 * Interface for invitation creation response
 */
export interface InvitationCreationResponse {
  invitation_id: string;
  token: string;
  invitation_link: string;
  expires_at: string;
}

/**
 * Interface for the RPC response from get_pending_invitations_for_group
 */
interface GroupPendingInvitationRpcResponse {
  invitation_id: string;
  group_id: string;
  group_name: string;
  invited_by_user_id: string;
  invited_by_email: string | null;
  invitee_email: string;
  token: string;
  expires_at: string;
  created_at: string;
}

/**
 * Fetches detailed information for a specific family group, including its members.
 * The user must be a member of the group to retrieve details.
 * @param groupId The ID of the group.
 * @returns A FamilyGroupDetails object or null if the group is not found or user is not a member.
 */
export async function getFamilyGroupDetails(
  groupId: string
): Promise<FamilyGroupDetails | null> {
  if (!groupId) {
    console.error(
      "Error fetching group details: groupId is undefined or null."
    );
    throw new Error("Group ID must be provided.");
  }

  const { data, error } = await supabase.rpc("get_family_group_details", {
    p_group_id: groupId,
  });

  if (error) {
    console.error(`Error fetching details for group ${groupId}:`, error);
    // Check if the error is a PostgrestError and has a code property
    const postgrestError = error as PostgrestError;
    if (postgrestError && postgrestError.code === "PGRST204") {
      // PGRST204: No Content (RLS or not found)
      return null;
    }
    throw new Error(
      postgrestError.message ||
        "An unknown error occurred while fetching group details."
    );
  }

  // The RPC returns null if user is not a member or group not found.
  // If data is explicitly null, we return it as such.
  if (data === null) {
    return null;
  }

  // If data is present, it should conform to FamilyGroupDetails
  return data as FamilyGroupDetails;
}

/**
 * Invites a user to a family group and sends an email invitation.
 * @param groupId The ID of the group.
 * @param inviteeEmail The email of the user to invite.
 * @returns The invitation details including the invitation link and email status.
 */
export async function inviteToFamilyGroup(
  groupId: string,
  inviteeEmail: string
): Promise<InvitationCreationResponse & { email_sent?: boolean }> {
  try {
    // Use the new server action that handles both invitation creation and email sending
    const result = await inviteToFamilyGroupWithEmail(groupId, inviteeEmail);

    if (!result.success) {
      throw new Error(result.error || "Failed to invite user");
    }

    if (!result.data) {
      throw new Error("No invitation data returned");
    }

    // Return data in the expected format for backwards compatibility
    return {
      invitation_id: result.data.invitation_id,
      token: result.data.token,
      invitation_link: result.data.invitation_link,
      expires_at: result.data.expires_at,
      email_sent: result.data.email_sent,
    };
  } catch (error) {
    console.error("Error inviting to family group:", error);
    throw error instanceof Error ? error : new Error("Failed to invite user");
  }
}

/**
 * Accepts a family group invitation.
 * @param token The invitation token.
 */
export async function acceptFamilyGroupInvitation(
  token: string
): Promise<void> {
  const { error } = await supabase.rpc("accept_family_group_invitation", {
    p_token: token,
  });

  if (error) {
    console.error("Error accepting family group invitation:", error);
    throw new Error(error.message);
  }
}

/**
 * Fetches the members of a specific family group.
 * @param groupId The ID of the group.
 */
export async function getFamilyGroupMembers(
  groupId: string
): Promise<FamilyGroupMember[]> {
  const { data, error } = await supabase.rpc("get_family_group_members", {
    p_group_id: groupId,
  });

  if (error) {
    console.error("Error fetching family group members:", error);
    throw new Error(error.message);
  }
  return (data as FamilyGroupMember[]) || [];
}

/**
 * Removes a member from a family group.
 * @param groupId The ID of the group.
 * @param memberUserId The ID of the member to remove.
 */
export async function removeMemberFromFamilyGroup(
  groupId: string,
  memberUserId: string
): Promise<void> {
  const { error } = await supabase.rpc("remove_member_from_family_group", {
    p_group_id: groupId,
    p_user_id_to_remove: memberUserId,
  });

  if (error) {
    console.error("Error removing member from family group:", error);
    throw new Error(error.message);
  }
}

/**
 * Allows the current user to leave a family group.
 * @param groupId The ID of the group to leave.
 */
export async function leaveFamilyGroup(groupId: string): Promise<void> {
  const { error } = await supabase.rpc("leave_family_group", {
    p_group_id: groupId,
  });

  if (error) {
    console.error("Error leaving family group:", error);
    throw new Error(error.message);
  }
}

/**
 * Fetches the pending invitations for the currently authenticated user.
 */
export async function getPendingInvitationsForUser(): Promise<
  PendingInvitation[]
> {
  const { data, error } = await supabase.rpc(
    "get_pending_invitations_for_user"
  );

  if (error) {
    console.error("Error fetching pending invitations:", error);
    throw new Error(
      `Failed to fetch pending invitations: ${JSON.stringify(
        error,
        Object.getOwnPropertyNames(error)
      )}`
    );
  }
  return (data as PendingInvitation[]) || [];
}

/**
 * Responds to a family group invitation.
 * @param invitationId The ID of the invitation.
 * @param action The action to take ('accept' or 'decline').
 * @returns A RespondToInvitationResponse object.
 */
export async function respondToInvitation(
  invitationId: string,
  action: "accept" | "decline"
): Promise<RespondToInvitationResponse> {
  const { data, error } = await supabase.rpc("respond_to_invitation", {
    p_invitation_id: invitationId,
    p_action: action,
  });

  if (error) {
    console.error("Error responding to invitation:", error);
    throw new Error(
      `Failed to ${action} invitation: ${
        error.message || JSON.stringify(error)
      }`
    );
  }

  return (
    (data as RespondToInvitationResponse) || {
      success: false,
      message: "No response received from server.",
    }
  );
}

/**
 * Fetches pending invitations for a specific family group.
 * Only group admins can see these invitations.
 * @param groupId The ID of the group to fetch invitations for
 * @returns Array of pending invitations for the group
 */
export async function getGroupPendingInvitations(
  groupId: string
): Promise<FamilyGroupInvitation[]> {
  if (!groupId) {
    throw new Error("Group ID must be provided.");
  }

  try {
    // Get the current user
    const { data: userData, error: userError } = await supabase.auth.getUser();

    if (userError) {
      console.error("Error getting current user:", userError);
      throw new Error(`Failed to get current user: ${userError.message}`);
    }

    if (!userData.user?.id) {
      throw new Error("User not authenticated");
    }

    // First check if the user is an admin of the group
    const { data: isAdmin, error: adminCheckError } = await supabase.rpc(
      "is_family_group_admin",
      {
        p_group_id: groupId,
        p_user_id: userData.user.id,
      }
    );

    if (adminCheckError) {
      console.error("Error checking admin status:", adminCheckError);
      throw new Error(
        `Failed to check admin status: ${adminCheckError.message}`
      );
    }

    if (!isAdmin) {
      throw new Error("Only group admins can view pending invitations");
    }

    // Use a raw SQL query to fetch the pending invitations
    // This avoids TypeScript errors with the Supabase client
    const { data, error } = await supabase.rpc(
      "get_pending_invitations_for_group",
      {
        p_group_id: groupId,
      }
    );

    if (error) {
      console.error("Error fetching group pending invitations:", error);
      throw new Error(`Failed to fetch pending invitations: ${error.message}`);
    }

    if (!data || !Array.isArray(data)) {
      return [];
    }

    // Transform the data to match the FamilyGroupInvitation interface
    const invitations: FamilyGroupInvitation[] = data.map(
      (item: GroupPendingInvitationRpcResponse) => ({
        id: item.invitation_id,
        group_id: item.group_id,
        email: item.invitee_email,
        token: item.token || "",
        status: "pending" as const, // We know these are pending invitations
        invited_by: item.invited_by_email || item.invited_by_user_id,
        created_at: item.created_at || new Date().toISOString(),
        expires_at: item.expires_at || new Date().toISOString(),
        invited_by_user_id: item.invited_by_user_id,
        invitee_email: item.invitee_email,
      })
    );

    return invitations;
  } catch (error) {
    console.error("Error fetching group pending invitations:", error);
    // Re-throw with more context
    if (error instanceof Error) {
      throw error;
    }
    throw new Error(
      `Unexpected error fetching pending invitations: ${JSON.stringify(error)}`
    );
  }
}

/**
 * Cancels a pending invitation.
 * Only group admins can cancel invitations.
 * @param invitationId The ID of the invitation to cancel
 * @returns void
 */
export async function cancelInvitation(invitationId: string): Promise<void> {
  if (!invitationId) {
    throw new Error("Invitation ID must be provided.");
  }

  try {
    // Get the current user
    const { data: userData, error: userError } = await supabase.auth.getUser();

    if (userError) {
      console.error("Error getting current user:", userError);
      throw new Error(`Failed to get current user: ${userError.message}`);
    }

    if (!userData.user?.id) {
      throw new Error("User not authenticated");
    }

    // First, get the invitation to check the group ID
    const { data: invitationData, error: fetchError } = await supabase.rpc(
      "get_invitation_by_id",
      {
        p_invitation_id: invitationId,
      }
    );

    if (fetchError) {
      console.error("Error fetching invitation:", fetchError);
      throw new Error(`Failed to fetch invitation: ${fetchError.message}`);
    }

    if (
      !invitationData ||
      !Array.isArray(invitationData) ||
      invitationData.length === 0
    ) {
      throw new Error("Invitation not found");
    }

    // Type assertion for the invitation data (RPC returns an array)
    const typedInvitation = invitationData[0] as { group_id: string };

    // Check if the user is an admin of the group
    const { data: isAdmin, error: adminCheckError } = await supabase.rpc(
      "is_family_group_admin",
      {
        p_group_id: typedInvitation.group_id,
        p_user_id: userData.user.id,
      }
    );

    if (adminCheckError) {
      console.error("Error checking admin status:", adminCheckError);
      throw new Error(
        `Failed to check admin status: ${
          adminCheckError.message || JSON.stringify(adminCheckError)
        }`
      );
    }

    if (!isAdmin) {
      throw new Error("Only group admins can cancel invitations");
    }

    // Now cancel the invitation using an RPC that should already exist
    const { error } = await supabase.rpc("cancel_family_group_invitation", {
      p_invitation_id: invitationId,
    });

    if (error) {
      console.error("Error cancelling invitation:", error);
      throw new Error(`Failed to cancel invitation: ${error.message}`);
    }
  } catch (error) {
    console.error("Error cancelling invitation:", error);
    // Re-throw with more context
    if (error instanceof Error) {
      throw error;
    }
    throw new Error(
      `Unexpected error cancelling invitation: ${JSON.stringify(error)}`
    );
  }
}

/**
 * Expires old pending invitations.
 * @returns The number of invitations that were expired.
 */
export async function expireOldInvitations(): Promise<number> {
  const { data, error } = await supabase.rpc("expire_old_invitations");

  if (error) {
    console.error("Error expiring old invitations:", error);
    throw new Error(`Failed to expire old invitations: ${error.message}`);
  }

  return (data as number) || 0;
}

/**
 * Gets invitation details by token.
 * @param token The invitation token.
 * @returns The invitation details or null if not found.
 */
export async function getInvitationByToken(token: string) {
  const { data, error } = await supabase.rpc("get_invitation_by_token", {
    p_token: token,
  });

  if (error) {
    console.error("Error fetching invitation by token:", error);
    throw new Error(`Failed to fetch invitation: ${error.message}`);
  }

  const typedData = data as Array<{
    id: string;
    group_id: string;
    invitee_email: string;
    status: string;
    expires_at: string;
  }>;
  return typedData && typedData.length > 0 ? typedData[0] : null;
}

/**
 * Updates the name of a family group.
 * Only group admins can update the group name.
 * @param groupId The ID of the group to update
 * @param newName The new name for the group
 */
export async function updateFamilyGroupName(
  groupId: string,
  newName: string
): Promise<void> {
  if (!groupId || !newName?.trim()) {
    throw new Error("Group ID and new name must be provided.");
  }

  const { error } = await supabase
    .from("family_groups")
    .update({ name: newName.trim() })
    .eq("id", groupId);

  if (error) {
    console.error("Error updating family group name:", error);
    throw new Error(`Failed to update group name: ${error.message}`);
  }
}

/**
 * Removes a member from a family group.
 * Only group admins can remove members.
 * @param groupId The ID of the group
 * @param memberUserId The ID of the member to remove
 */
export async function removeMemberFromGroup(
  groupId: string,
  memberUserId: string
): Promise<void> {
  if (!groupId || !memberUserId) {
    throw new Error("Group ID and member user ID must be provided.");
  }

  const { error } = await supabase.rpc("remove_member_from_family_group", {
    p_group_id: groupId,
    p_user_to_remove_id: memberUserId,
  });

  if (error) {
    console.error("Error removing member from family group:", error);
    throw new Error(`Failed to remove member: ${error.message}`);
  }
}

/**
 * Updates the role of a member in a family group.
 * Only group admins can update member roles.
 * @param groupId The ID of the group
 * @param memberUserId The ID of the member whose role to update
 * @param newRole The new role for the member
 */
export async function updateMemberRole(
  groupId: string,
  memberUserId: string,
  newRole: "admin" | "member"
): Promise<void> {
  if (!groupId || !memberUserId || !newRole) {
    throw new Error("Group ID, member user ID, and new role must be provided.");
  }

  const { error } = await supabase
    .from("family_group_members")
    .update({ role: newRole })
    .eq("group_id", groupId)
    .eq("user_id", memberUserId);

  if (error) {
    console.error("Error updating member role:", error);
    throw new Error(`Failed to update member role: ${error.message}`);
  }
}

/**
 * Gets audit logs for invitations in a group (admin only).
 * @param groupId The ID of the group.
 * @returns Array of audit log entries.
 */
export async function getInvitationAuditLogs(groupId: string) {
  const { data, error } = await supabase.rpc("get_invitation_audit_logs", {
    p_group_id: groupId,
  });

  if (error) {
    console.error("Error fetching invitation audit logs:", error);
    throw new Error(`Failed to fetch audit logs: ${error.message}`);
  }

  return data || [];
}

/**
 * Deletes a family group and all associated data.
 * Only group admins can delete a group.
 * This will permanently delete all group members, invitations, and financial data.
 * @param groupId The ID of the group to delete.
 */
export async function deleteFamilyGroup(groupId: string): Promise<void> {
  if (!groupId) {
    throw new Error("Group ID must be provided.");
  }

  try {
    const { error } = await supabase.rpc("delete_family_group", {
      p_group_id: groupId,
    });

    if (error) {
      console.error("Error deleting family group:", error);

      // Check for specific error types
      if ('code' in error && error.code === "42883") {
        throw new Error(
          "The delete_family_group function does not exist in the database. Please apply the migration first."
        );
      }

      const errorMessage = 'message' in error ? error.message : 
                          'details' in error ? (error as Record<string, unknown>).details :
                          'hint' in error ? (error as Record<string, unknown>).hint :
                          JSON.stringify(error);

      throw new Error(`Failed to delete family group: ${errorMessage}`);
    }

    console.log("Family group deleted successfully");
  } catch (error) {
    console.error("Error deleting family group:", error);
    if (error instanceof Error) {
      throw error;
    }
    throw new Error(`Failed to delete family group: ${JSON.stringify(error)}`);
  }
}
