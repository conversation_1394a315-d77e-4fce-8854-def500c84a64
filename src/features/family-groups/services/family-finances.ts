import { createClient } from "@/shared/services/supabase/client";

const supabase = createClient();

/**
 * Interface for financial summary data
 */
export interface FamilyGroupFinancialSummary {
  total_balance: number;
  monthly_income: number;
  monthly_expenses: number;
  monthly_net: number;
  active_budgets: number;
  active_goals: number;
  accounts_count: number;
}

/**
 * Interface for family group account data
 */
export interface FamilyGroupAccount {
  id: string;
  name: string;
  type: string;
  balance: number;
  currency: string;
  icon?: string;
  color?: string;
  created_by_user_id: string;
  created_by_email: string; // This will be VARCHAR(255) from database but TypeScript treats it as string
  created_at: string;
  updated_at: string;
}

/**
 * Interface for family group transaction data
 */
export interface FamilyGroupTransaction {
  id: string;
  account_id: string;
  account_name: string;
  category_id?: string;
  category_name?: string;
  amount: number;
  type: string;
  description?: string;
  date: string;
  notes?: string;
  created_by_user_id: string;
  created_by_email: string;
  created_at: string;
}

/**
 * Interface for family group budget data
 */
export interface FamilyGroupBudget {
  id: string;
  category_id?: string;
  category_name?: string;
  amount: number;
  period: string;
  start_date: string;
  end_date?: string;
  spent_amount: number;
  created_by_user_id: string;
  created_by_email: string;
  created_at: string;
}

/**
 * Interface for family group goal data
 */
export interface FamilyGroupGoal {
  id: string;
  name: string;
  target_amount: number;
  current_amount: number;
  target_date?: string;
  status: string;
  icon?: string;
  color?: string;
  progress_percentage: number;
  created_by_user_id: string;
  created_by_email: string;
  created_at: string;
}

/**
 * Interface for family group category data
 */
export interface FamilyGroupCategory {
  id: string;
  name: string;
  type: string;
  icon?: string;
  color?: string;
  parent_id?: string;
  parent_name?: string;
  created_by_user_id: string;
  created_by_email: string;
  created_at: string;
}

/**
 * Gets financial summary for a family group
 * @param groupId The ID of the family group
 * @returns Financial summary data
 */
export async function getFamilyGroupFinancialSummary(
  groupId: string
): Promise<FamilyGroupFinancialSummary> {
  const { data, error } = await supabase.rpc(
    "get_family_group_financial_summary",
    {
      p_group_id: groupId,
    }
  );

  if (error) {
    console.error("Error fetching family group financial summary:", error);
    console.error("Error details:", JSON.stringify(error, null, 2));
    throw new Error(
      `Failed to fetch financial summary: ${
        error.message ||
        (error as unknown as Record<string, unknown>).details ||
        "Unknown error"
      }`
    );
  }

  return data as FamilyGroupFinancialSummary;
}

/**
 * Gets all accounts for a family group
 * @param groupId The ID of the family group
 * @returns Array of family group accounts
 */
export async function getFamilyGroupAccounts(
  groupId: string
): Promise<FamilyGroupAccount[]> {
  const { data, error } = await supabase.rpc("get_family_group_accounts", {
    p_group_id: groupId,
  });

  if (error) {
    console.error("Error fetching family group accounts:", error);
    console.error("Error details:", JSON.stringify(error, null, 2));
    throw new Error(
      `Failed to fetch accounts: ${
        error.message ||
        (error as unknown as Record<string, unknown>).details ||
        "Unknown error"
      }`
    );
  }

  return (data as FamilyGroupAccount[]) || [];
}

/**
 * Gets transactions for a family group with pagination
 * @param groupId The ID of the family group
 * @param limit Number of transactions to fetch (default: 50)
 * @param offset Number of transactions to skip (default: 0)
 * @returns Array of family group transactions
 */
export async function getFamilyGroupTransactions(
  groupId: string,
  limit: number = 50,
  offset: number = 0
): Promise<FamilyGroupTransaction[]> {
  const { data, error } = await supabase.rpc("get_family_group_transactions", {
    p_group_id: groupId,
    p_limit: limit,
    p_offset: offset,
  });

  if (error) {
    console.error("Error fetching family group transactions:", error);
    console.error("Error details:", JSON.stringify(error, null, 2));
    throw new Error(
      `Failed to fetch transactions: ${
        error.message ||
        (error as unknown as Record<string, unknown>).details ||
        "Unknown error"
      }`
    );
  }

  return (data as FamilyGroupTransaction[]) || [];
}

/**
 * Gets all budgets for a family group
 * @param groupId The ID of the family group
 * @returns Array of family group budgets
 */
export async function getFamilyGroupBudgets(
  groupId: string
): Promise<FamilyGroupBudget[]> {
  const { data, error } = await supabase.rpc("get_family_group_budgets", {
    p_group_id: groupId,
  });

  if (error) {
    console.error("Error fetching family group budgets:", error);
    console.error("Error details:", JSON.stringify(error, null, 2));
    throw new Error(
      `Failed to fetch budgets: ${
        error.message ||
        (error as unknown as Record<string, unknown>).details ||
        "Unknown error"
      }`
    );
  }

  return (data as FamilyGroupBudget[]) || [];
}

/**
 * Gets all financial goals for a family group
 * @param groupId The ID of the family group
 * @returns Array of family group goals
 */
export async function getFamilyGroupGoals(
  groupId: string
): Promise<FamilyGroupGoal[]> {
  const { data, error } = await supabase.rpc("get_family_group_goals", {
    p_group_id: groupId,
  });

  if (error) {
    console.error("Error fetching family group goals:", error);
    console.error("Error details:", JSON.stringify(error, null, 2));
    throw new Error(
      `Failed to fetch goals: ${
        error.message ||
        (error as unknown as Record<string, unknown>).details ||
        "Unknown error"
      }`
    );
  }

  return (data as FamilyGroupGoal[]) || [];
}

/**
 * Gets all categories for a family group
 * @param groupId The ID of the family group
 * @returns Array of family group categories
 */
export async function getFamilyGroupCategories(
  groupId: string
): Promise<FamilyGroupCategory[]> {
  try {
    const { data, error } = await supabase.rpc("get_family_group_categories", {
      p_group_id: groupId,
    });

    if (error) {
      console.error("Error fetching family group categories:", error);
      console.error("Error details:", JSON.stringify(error, null, 2));

      // Handle specific error cases
      if ((error as unknown as Record<string, unknown>).code === "42501") {
        throw new Error("You don't have permission to view these categories");
      } else if (
        (error as unknown as Record<string, unknown>).code === "42804"
      ) {
        console.warn(
          "Database schema mismatch detected. Please run migrations."
        );
        throw new Error(
          "Database configuration issue detected. Please contact support."
        );
      } else if (error.message.includes("not a member")) {
        throw new Error(
          "You must be a member of this family group to view categories"
        );
      }

      throw new Error(
        `Failed to load categories: ${
          error.message || "Please try again later"
        }`
      );
    }

    return (data || []) as FamilyGroupCategory[];
  } catch (err) {
    console.error("Unexpected error in getFamilyGroupCategories:", err);
    throw new Error(
      err instanceof Error ? err.message : "Failed to load categories"
    );
  }
}

/**
 * Creates a new account for a family group
 * @param groupId The ID of the family group
 * @param accountData The account data
 * @returns The created account ID
 */
export async function createFamilyGroupAccount(
  groupId: string,
  accountData: {
    name: string;
    type: string;
    balance: number;
    currency: string;
    icon?: string;
    color?: string;
  }
): Promise<string> {
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) throw new Error("User must be authenticated");

  const { data, error } = await supabase
    .from("accounts")
    .insert({
      ...accountData,
      family_group_id: groupId,
      user_id: user.id,
      created_by_user_id: user.id,
    })
    .select("id")
    .single();

  if (error) {
    console.error("Error creating family group account:", error);
    throw new Error(`Failed to create account: ${error.message}`);
  }

  return data.id;
}

/**
 * Creates a new transaction for a family group
 * @param groupId The ID of the family group
 * @param transactionData The transaction data
 * @returns The created transaction ID
 */
export async function createFamilyGroupTransaction(
  groupId: string,
  transactionData: {
    account_id: string;
    category_id?: string;
    amount: number;
    type: string;
    description?: string;
    date: string;
    notes?: string;
  }
): Promise<string> {
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) throw new Error("User must be authenticated");

  const { data, error } = await supabase
    .from("transactions")
    .insert({
      ...transactionData,
      family_group_id: groupId,
      user_id: user.id,
      created_by_user_id: user.id,
    })
    .select("id")
    .single();

  if (error) {
    console.error("Error creating family group transaction:", error);
    throw new Error(`Failed to create transaction: ${error.message}`);
  }

  return data.id;
}

/**
 * Creates a new budget for a family group
 * @param groupId The ID of the family group
 * @param budgetData The budget data
 * @returns The created budget ID
 */
export async function createFamilyGroupBudget(
  groupId: string,
  budgetData: {
    name: string;
    category_id?: string;
    amount: number;
    period: string;
    start_date: string;
    end_date?: string;
  }
): Promise<string> {
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) throw new Error("User must be authenticated");

  const { data, error } = await supabase
    .from("budgets")
    .insert({
      ...budgetData,
      family_group_id: groupId,
      user_id: user.id,
      created_by_user_id: user.id,
    })
    .select("id")
    .single();

  if (error) {
    console.error("Error creating family group budget:", error);
    throw new Error(`Failed to create budget: ${error.message}`);
  }

  return data.id;
}

/**
 * Creates a new financial goal for a family group
 * @param groupId The ID of the family group
 * @param goalData The goal data
 * @returns The created goal ID
 */
export async function createFamilyGroupGoal(
  groupId: string,
  goalData: {
    name: string;
    target_amount: number;
    current_amount: number;
    target_date?: string;
    status: string;
    icon?: string;
    color?: string;
  }
): Promise<string> {
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) throw new Error("User must be authenticated");

  const { data, error } = await supabase
    .from("financial_goals")
    .insert({
      ...goalData,
      family_group_id: groupId,
      user_id: user.id,
      created_by_user_id: user.id,
    })
    .select("id")
    .single();

  if (error) {
    console.error("Error creating family group goal:", error);
    throw new Error(`Failed to create goal: ${error.message}`);
  }

  return data.id;
}

/**
 * Creates a new category for a family group
 * @param groupId The ID of the family group
 * @param categoryData The category data
 * @returns The created category ID
 */
export async function createFamilyGroupCategory(
  groupId: string,
  categoryData: {
    name: string;
    type: string;
    icon?: string;
    color?: string;
    parent_id?: string;
  }
): Promise<string> {
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) throw new Error("User must be authenticated");

  // First check for existing category with same name and type
  const { data: existing } = await supabase
    .from("categories")
    .select("id")
    .eq("family_group_id", groupId)
    .eq("name", categoryData.name)
    .eq("type", categoryData.type)
    .maybeSingle();

  if (existing) {
    throw new Error(
      `A ${categoryData.type} category with this name already exists`
    );
  }

  const { data, error } = await supabase
    .from("categories")
    .insert({
      ...categoryData,
      family_group_id: groupId,
      user_id: user.id,
      created_by_user_id: user.id,
    })
    .select("id")
    .single();

  if (error) {
    console.error("Error creating family group category:", error);

    if (error.code === "23505") {
      throw new Error("A category with this name already exists");
    } else if (error.code === "42501") {
      throw new Error("You don't have permission to create categories");
    }

    throw new Error(
      `Failed to create category: ${error.message || "Please try again later"}`
    );
  }

  return data.id;
}
