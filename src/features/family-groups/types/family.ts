/**
 * Represents a family group as stored in the database or returned by APIs.
 */
export interface FamilyGroup {
  id: string;          // Corresponds to group_id in most contexts
  name: string;
  created_at: string;
  owner_user_id: string; // The user_id of the person who created the group
}

/**
 * Represents a member of a family group, often including their role and join date.
 * This matches the structure returned by the get_family_group_members RPC.
 */
export interface FamilyGroupMember {
  user_id: string;
  email: string;       // Joined from auth.users, type VARCHAR(255)
  role: string;        // e.g., 'admin', 'member'
  joined_at: string;   // TIMESTAMPTZ
}

/**
 * Represents the detailed information for a family group, including its members.
 * This matches the structure returned by the get_family_group_details RPC.
 */
export interface FamilyGroupDetails {
  group_id: string;
  group_name: string;
  group_created_at: string;
  members: FamilyGroupMember[];
}

/**
 * Represents an invitation to a family group.
 */
export interface FamilyGroupInvitation {
  id: string;
  group_id: string;
  group_name?: string; // Optional, if joined for display
  invited_by_user_id: string;
  invited_by_email?: string; // Optional, if joined for display
  invitee_email: string;
  status: 'pending' | 'accepted' | 'declined' | 'expired';
  token: string; 
  expires_at: string;
  created_at: string;
}

/**
 * Represents the structure of data returned by the get_user_family_groups RPC.
 */
export interface UserFamilyGroupRpcResponseItem {
  group_id: string;
  group_name: string;
  user_role: string;
  created_at: string; // This is the group's creation date from family_groups table
}

/**
 * Represents the structure of data returned by the get_pending_invitations_for_user RPC.
 */
export interface PendingInvitation {
  invitation_id: string; // UUID
  group_id: string; // UUID
  group_name: string;
  invited_by_user_id: string; // UUID
  invited_by_user_email: string;
  invited_at: string; // TIMESTAMPTZ as string
  invitation_status: string; // Should be 'pending'
}

export interface RespondToInvitationResponse {
  success: boolean;
  message: string;
}
