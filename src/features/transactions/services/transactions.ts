'use server';

import { createClient } from '@/shared/services/supabase/server';

export interface Transaction {
  id?: string;
  user_id?: string;
  amount: number;
  type: 'income' | 'expense';
  date: string;
  description: string;
  category_id: string | null;
  account_id?: string;
  created_at?: string;
}

export interface TransactionFilters {
  startDate?: string;
  endDate?: string;
  type?: 'income' | 'expense' | 'all';
  category?: string;
  account_id?: string;
  search?: string;
}

/**
 * Get all transactions for the current user with optional filters
 */
export async function getTransactions(filters?: TransactionFilters) {
  const supabase = await createClient();
  
  let query = supabase
    .from('transactions')
    .select('*')
    .order('date', { ascending: false });
  
  // Apply filters if provided
  if (filters) {
    if (filters.startDate) {
      query = query.gte('date', filters.startDate);
    }
    
    if (filters.endDate) {
      query = query.lte('date', filters.endDate);
    }
    
    if (filters.type && filters.type !== 'all') {
      query = query.eq('type', filters.type);
    }
    
    if (filters.category) {
      query = query.eq('category', filters.category);
    }
    
    if (filters.account_id) {
      query = query.eq('account_id', filters.account_id);
    }
    
    if (filters.search) {
      query = query.ilike('description', `%${filters.search}%`);
    }
  }
  
  const { data, error } = await query;
  
  if (error) {
    console.error('Error fetching transactions:', error);
    throw new Error(error.message);
  }
  
  return data as Transaction[];
}

/**
 * Get a single transaction by ID
 */
export async function getTransactionById(id: string) {
  const supabase = await createClient();
  
  const { data, error } = await supabase
    .from('transactions')
    .select('*')
    .eq('id', id)
    .single();
  
  if (error) {
    console.error('Error fetching transaction:', error);
    throw new Error(error.message);
  }
  
  return data as Transaction;
}

/**
 * Create a new transaction
 */
export async function createTransaction(transaction: Omit<Transaction, 'id' | 'created_at'>) {
  const supabase = await createClient();
  
  // Get the current user
  const { data: { user } } = await supabase.auth.getUser();
  
  if (!user) {
    throw new Error('User not authenticated');
  }
  
  const { data, error } = await supabase
    .from('transactions')
    .insert([
      {
        ...transaction,
        user_id: user.id,
        created_at: new Date().toISOString()
      }
    ])
    .select()
    .single();
  
  if (error) {
    console.error('Error creating transaction:', error);
    throw new Error(error.message);
  }
  
  return data as Transaction;
}

/**
 * Update an existing transaction
 */
export async function updateTransaction(id: string, updates: Partial<Transaction>) {
  const supabase = await createClient();
  
  const { data, error } = await supabase
    .from('transactions')
    .update(updates)
    .eq('id', id)
    .select()
    .single();
  
  if (error) {
    console.error('Error updating transaction:', error);
    throw new Error(error.message);
  }
  
  return data as Transaction;
}

/**
 * Delete a transaction
 */
export async function deleteTransaction(id: string) {
  const supabase = await createClient();
  
  const { error } = await supabase
    .from('transactions')
    .delete()
    .eq('id', id);
  
  if (error) {
    console.error('Error deleting transaction:', error);
    throw new Error(error.message);
  }
  
  return true;
}

/**
 * Get available transaction categories
 */
export async function getTransactionCategories() {
  // This could be fetched from a database table in the future
  // For now, we'll return a static list
  return [
    // Income categories
    { id: 'salary', name: 'Salary', type: 'income' },
    { id: 'freelance', name: 'Freelance', type: 'income' },
    { id: 'investments', name: 'Investments', type: 'income' },
    { id: 'gifts', name: 'Gifts', type: 'income' },
    { id: 'other_income', name: 'Other Income', type: 'income' },
    
    // Expense categories
    { id: 'housing', name: 'Housing', type: 'expense' },
    { id: 'utilities', name: 'Utilities', type: 'expense' },
    { id: 'groceries', name: 'Groceries', type: 'expense' },
    { id: 'dining', name: 'Dining', type: 'expense' },
    { id: 'transportation', name: 'Transportation', type: 'expense' },
    { id: 'healthcare', name: 'Healthcare', type: 'expense' },
    { id: 'entertainment', name: 'Entertainment', type: 'expense' },
    { id: 'shopping', name: 'Shopping', type: 'expense' },
    { id: 'personal', name: 'Personal', type: 'expense' },
    { id: 'education', name: 'Education', type: 'expense' },
    { id: 'subscriptions', name: 'Subscriptions', type: 'expense' },
    { id: 'travel', name: 'Travel', type: 'expense' },
    { id: 'debt', name: 'Debt Payments', type: 'expense' },
    { id: 'other_expense', name: 'Other Expense', type: 'expense' },
  ];
}
