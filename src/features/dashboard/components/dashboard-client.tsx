"use client";

import {
  <PERSON>,
  CardContent,
  CardDescription,
  Card<PERSON><PERSON>er,
  CardTitle,
} from "@/shared/components/ui/card";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Trigger,
} from "@/shared/components/ui/tabs";
import { <PERSON><PERSON> } from "@/shared/components/ui/button";

import {
  Wallet,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Target,
  ArrowUpRight,
  Plus,
  ArrowRight,
} from "lucide-react";
import { useMemo } from "react";
import { Badge } from "@/shared/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableRow,
  TableHeader,
  TableHead,
} from "@/shared/components/ui/table";
import { PiggyBank } from "lucide-react";
import { formatPercentage } from "@/shared/utils/dashboard";
import { useCurrencyFormatter } from "@/shared/hooks/useCurrencyFormatter";
import {
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  <PERSON><PERSON><PERSON>,
  Legend,
} from "recharts";
import Link from "next/link";
import { Progress } from "@/shared/components/ui/progress";
import type {
  Budget,
  Transaction as AppTransaction,
  Account as AppAccount,
} from "@/shared/types";
import type { Goal } from "@/shared/types";
import { ListChecks } from "lucide-react";

interface SpendingByCategoryItem {
  name: string;
  value: number;
  fill: string;
}

interface DashboardClientProps {
  stats: {
    totalBalance: number;
    monthlyIncome: number;
    monthlyExpenses: number;
    netCashFlow: number;
    savingsRate: number;
    incomeToExpenseRatio: number;
    activeBudgets: number;
    activeGoalsCount: number;
  };
  changes: {
    totalBalance: number;
    income: number;
    expenses: number;
  };
  recentTransactions: AppTransaction[];
  accounts: AppAccount[];
  spendingByCategory: SpendingByCategoryItem[];
  allTransactions: AppTransaction[];
  budgets: Budget[];
  financialGoalsPreview: Goal[];
}

export function DashboardClient({
  stats,
  changes,
  recentTransactions,
  accounts,
  spendingByCategory,
  allTransactions,
  budgets,
  financialGoalsPreview,
}: DashboardClientProps) {
  const { formatCurrency } = useCurrencyFormatter();

  const isPositiveBalance = changes.totalBalance >= 0;
  const isPositiveIncome = changes.income >= 0;
  const isPositiveExpenses = changes.expenses >= 0;

  const netCashFlow = useMemo(() => {
    return stats.monthlyIncome - stats.monthlyExpenses;
  }, [stats.monthlyIncome, stats.monthlyExpenses]);

  const incomeToExpenseRatio = useMemo(() => {
    return stats.monthlyExpenses > 0
      ? stats.monthlyIncome / stats.monthlyExpenses
      : 0;
  }, [stats.monthlyIncome, stats.monthlyExpenses]);

  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
        <p className="text-muted-foreground">
          Welcome back! Here&apos;s your financial overview.
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="border-l-4 border-green-500 overflow-hidden relative group hover:shadow-lg transition-shadow duration-300">
          <div className="absolute top-0 right-0 w-16 h-16 bg-green-500/10 rounded-bl-full"></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Balance</CardTitle>
            <Wallet className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(stats.totalBalance)}
            </div>
            <div className="flex items-center mt-2 text-xs text-muted-foreground">
              {isPositiveBalance ? (
                <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
              ) : (
                <TrendingDown className="h-3 w-3 mr-1 text-red-500" />
              )}
              <span
                className={
                  isPositiveBalance ? "text-green-500" : "text-red-500"
                }
              >
                {formatPercentage(changes.totalBalance)}
              </span>
              <span className="ml-1">from last month</span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-blue-500 overflow-hidden relative group hover:shadow-lg transition-shadow duration-300">
          <div className="absolute top-0 right-0 w-16 h-16 bg-blue-500/10 rounded-bl-full"></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Monthly Income
            </CardTitle>
            <DollarSign className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(stats.monthlyIncome)}
            </div>
            <div className="flex items-center mt-2 text-xs text-muted-foreground">
              {isPositiveIncome ? (
                <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
              ) : (
                <TrendingDown className="h-3 w-3 mr-1 text-red-500" />
              )}
              <span
                className={isPositiveIncome ? "text-green-500" : "text-red-500"}
              >
                {formatPercentage(changes.income)}
              </span>
              <span className="ml-1">from last month</span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-red-500 overflow-hidden relative group hover:shadow-lg transition-shadow duration-300">
          <div className="absolute top-0 right-0 w-16 h-16 bg-red-500/10 rounded-bl-full"></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Monthly Expenses
            </CardTitle>
            <DollarSign className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(stats.monthlyExpenses)}
            </div>
            <div className="flex items-center mt-2 text-xs text-muted-foreground">
              {isPositiveExpenses ? (
                <TrendingUp className="h-3 w-3 mr-1 text-red-500" />
              ) : (
                <TrendingDown className="h-3 w-3 mr-1 text-green-500" />
              )}
              <span
                className={
                  isPositiveExpenses ? "text-red-500" : "text-green-500"
                }
              >
                {formatPercentage(changes.expenses)}
              </span>
              <span className="ml-1">from last month</span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-purple-500 overflow-hidden relative group hover:shadow-lg transition-shadow duration-300">
          <div className="absolute top-0 right-0 w-16 h-16 bg-purple-500/10 rounded-bl-full"></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Savings Rate</CardTitle>
            <PiggyBank className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatPercentage(stats.savingsRate)}
            </div>
            <p className="text-xs text-muted-foreground mt-2">
              of income saved this month
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Additional Financial Metrics */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Additional Metrics</CardTitle>
            <CardDescription>Financial health indicators</CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableBody>
                <TableRow>
                  <TableCell className="font-medium">Net Cash Flow</TableCell>
                  <TableCell className="text-right">
                    <span
                      className={
                        netCashFlow >= 0 ? "text-green-500" : "text-red-500"
                      }
                    >
                      {formatCurrency(netCashFlow)}
                    </span>
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="font-medium">
                    Income to Expense Ratio
                  </TableCell>
                  <TableCell className="text-right">
                    {incomeToExpenseRatio.toFixed(2)} : 1
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Goals & Budgets Card */}
        <Card>
          <CardHeader>
            <CardTitle>Goals & Budgets</CardTitle>
            <CardDescription>Track your active financial plans</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center">
                <Target className="h-6 w-6 mr-3 text-primary" />
                <div>
                  <p className="text-sm font-medium">Active Financial Goals</p>
                  <p className="text-xs text-muted-foreground">
                    Your current aspirations
                  </p>
                </div>
              </div>
              <p className="text-2xl font-semibold">{stats.activeGoalsCount}</p>
            </div>
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center">
                <ListChecks className="h-6 w-6 mr-3 text-primary" />
                <div>
                  <p className="text-sm font-medium">Active Budgets</p>
                  <p className="text-xs text-muted-foreground">
                    Your current spending plans
                  </p>
                </div>
              </div>
              <p className="text-2xl font-semibold">{stats.activeBudgets}</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <div className="flex items-center justify-between">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="transactions">Transactions</TabsTrigger>
            <TabsTrigger value="budgets">Budgets</TabsTrigger>
            <TabsTrigger value="goals">Goals</TabsTrigger>
          </TabsList>
          <Button variant="outline" size="sm">
            <Plus className="mr-2 h-4 w-4" /> Add New
          </Button>
        </div>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Spending Overview</CardTitle>
                <CardDescription>
                  Your spending breakdown for this month
                </CardDescription>
              </CardHeader>
              <CardContent className="h-[350px]">
                {spendingByCategory && spendingByCategory.length > 0 ? (
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={spendingByCategory}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        nameKey="name"
                        label={({ name, percent }) =>
                          `${name} ${(percent * 100).toFixed(0)}%`
                        }
                      >
                        {spendingByCategory.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.fill} />
                        ))}
                      </Pie>
                      <Tooltip
                        formatter={(value: number) => formatCurrency(value)}
                      />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="flex items-center justify-center h-full">
                    <p className="text-muted-foreground">
                      No spending data for this month.
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Financial Goals Card */}
            <Card>
              <CardHeader>
                <CardTitle>Financial Goals</CardTitle>
                <CardDescription>
                  Your top financial aspirations
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {financialGoalsPreview && financialGoalsPreview.length > 0 ? (
                  financialGoalsPreview.map((goal) => (
                    <div key={goal.id} className="p-3 border rounded-lg">
                      <div className="flex justify-between items-center mb-1">
                        <h4 className="text-sm font-medium flex items-center">
                          {goal.icon && (
                            <span className="mr-2">{goal.icon}</span>
                          )}{" "}
                          {/* Assuming goal.icon is an emoji or simple char */}
                          {goal.name}
                        </h4>
                        <span className="text-xs text-muted-foreground">
                          {goal.daysRemaining > 0
                            ? `${goal.daysRemaining} days left`
                            : "Target date passed"}
                        </span>
                      </div>
                      <Progress value={goal.progress} className="h-2 mb-1" />
                      <div className="flex justify-between text-xs">
                        <span>{formatCurrency(goal.current_amount)}</span>
                        <span className="text-muted-foreground">
                          Target: {formatCurrency(goal.target_amount)}
                        </span>
                      </div>
                      {goal.monthlyRequired > 0 && (
                        <p className="text-xs text-muted-foreground mt-1">
                          Save ~{formatCurrency(goal.monthlyRequired)}
                          /month to reach by{" "}
                          {new Date(goal.target_date).toLocaleDateString()}
                        </p>
                      )}
                    </div>
                  ))
                ) : (
                  <div className="flex items-center justify-center h-full min-h-[100px]">
                    <p className="text-muted-foreground">
                      No active goals to display.
                    </p>
                  </div>
                )}
                {financialGoalsPreview && financialGoalsPreview.length > 0 && (
                  <Link
                    href="#"
                    onClick={() =>
                      document
                        .querySelector<HTMLButtonElement>(
                          'button[value="goals"]'
                        )
                        ?.click()
                    }
                    className="text-sm text-primary hover:underline flex items-center mt-2"
                  >
                    View All Goals <ArrowRight className="ml-1 h-4 w-4" />
                  </Link>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Recent Transactions Card */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>
                Your latest financial activities
              </CardDescription>
              <Button variant="ghost" size="sm" asChild>
                <Link href="/dashboard/transactions">
                  View All <ArrowUpRight className="ml-1 h-4 w-4" />
                </Link>
              </Button>
            </CardHeader>
            <CardContent>
              {recentTransactions && recentTransactions.length > 0 ? (
                <Table>
                  <TableBody>
                    {recentTransactions.map((transaction) => (
                      <TableRow key={transaction.id}>
                        <TableCell>
                          <div className="font-medium">
                            {transaction.description}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {new Date(transaction.date).toLocaleDateString(
                              undefined,
                              {
                                day: "numeric",
                                month: "short",
                                year: "numeric",
                              }
                            )}
                          </div>
                        </TableCell>
                        <TableCell
                          className={`text-right font-semibold ${
                            transaction.type === "income"
                              ? "text-green-500"
                              : "text-red-500"
                          }`}
                        >
                          {transaction.type === "income" ? "+" : "-"}
                          {formatCurrency(Math.abs(transaction.amount || 0))}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="flex items-center justify-center h-40">
                  <p className="text-muted-foreground">
                    No recent transactions.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="transactions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>All Transactions</CardTitle>
              <CardDescription>
                View and manage all your financial activities.
              </CardDescription>
            </CardHeader>
            <CardContent>
              {allTransactions && allTransactions.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>Description</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead className="text-right">Amount</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {allTransactions.map((transaction) => (
                      <TableRow key={transaction.id}>
                        <TableCell>
                          {new Date(transaction.date).toLocaleDateString()}
                        </TableCell>
                        <TableCell>{transaction.description}</TableCell>
                        <TableCell>{transaction.category || "N/A"}</TableCell>
                        <TableCell
                          className={`text-right ${
                            transaction.type === "income"
                              ? "text-green-500"
                              : "text-red-500"
                          }`}
                        >
                          {transaction.type === "income" ? "+" : "-"}
                          {formatCurrency(Math.abs(transaction.amount || 0))}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="flex items-center justify-center h-40">
                  <p className="text-muted-foreground">
                    No transactions found.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="budgets" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Budgets</CardTitle>
              <CardDescription>Manage your spending targets.</CardDescription>
            </CardHeader>
            <CardContent>
              {budgets && budgets.length > 0 ? (
                <div className="space-y-6">
                  {budgets.map((budget) => {
                    const spent = formatCurrency(budget.spent || 0);
                    const amount = formatCurrency(budget.amount || 0);
                    const progress = budget.progress || 0;
                    const remaining = formatCurrency(budget.remaining || 0);

                    return (
                      <div
                        key={budget.id}
                        className="p-4 border rounded-lg shadow-sm"
                      >
                        <div className="flex justify-between items-center mb-2">
                          <h3 className="text-lg font-semibold">
                            {budget.name}
                          </h3>
                          <Badge
                            style={{
                              backgroundColor: budget.category_color || "#ccc",
                            }}
                            className="text-xs"
                          >
                            {budget.category_name || "Uncategorized"}
                          </Badge>
                        </div>
                        <div className="mb-2">
                          <div className="flex justify-between text-sm">
                            <span>Spent: {spent}</span>
                            <span>Budgeted: {amount}</span>
                          </div>
                          <Progress value={progress} className="h-2 mt-1" />
                          <div className="flex justify-between text-xs text-muted-foreground mt-1">
                            <span>{progress?.toFixed(0) || 0}% Used</span>
                            <span>Remaining: {remaining}</span>
                          </div>
                        </div>
                        <p className="text-xs text-muted-foreground">
                          Period:{" "}
                          {budget.period.charAt(0).toUpperCase() +
                            budget.period.slice(1)}
                          {budget.period !== "one-time"
                            ? ` (Current)`
                            : ` (Ends: ${
                                budget.end_date
                                  ? new Date(
                                      budget.end_date
                                    ).toLocaleDateString()
                                  : "N/A"
                              })`}
                        </p>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <div className="flex items-center justify-center h-40">
                  <p className="text-muted-foreground">
                    No budgets set up yet. Create one to get started!
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="goals" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Financial Goals</CardTitle>
              <CardDescription>
                Progress towards your financial goals
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[400px] flex items-center justify-center text-muted-foreground">
                <Target className="h-8 w-8 mr-2" />
                Goal progress will be displayed here
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Recent Transactions & Accounts */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Recent Transactions</CardTitle>
            <CardDescription>Your latest financial activity</CardDescription>
          </CardHeader>
          <CardContent>
            {recentTransactions.length > 0 ? (
              <div className="space-y-4">
                {recentTransactions.map((transaction) => (
                  <div
                    key={transaction.id}
                    className="flex items-center justify-between border-b pb-2"
                  >
                    <div>
                      <p className="font-medium">{transaction.description}</p>
                      <p className="text-sm text-muted-foreground">
                        {new Date(transaction.date).toLocaleDateString()}
                      </p>
                      {transaction.category && (
                        <Badge variant="outline" className="mt-1">
                          {transaction.category}
                        </Badge>
                      )}
                    </div>
                    <p
                      className={
                        transaction.type === "income"
                          ? "text-green-500"
                          : "text-red-500"
                      }
                    >
                      {transaction.type === "income" ? "+" : "-"}
                      {formatCurrency(transaction.amount || 0)}
                    </p>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-4 text-muted-foreground">
                No recent transactions
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Accounts</CardTitle>
            <CardDescription>Manage your money</CardDescription>
          </CardHeader>
          <CardContent>
            {accounts.length > 0 ? (
              <div className="space-y-4">
                {accounts.map((account) => (
                  <div
                    key={account.id}
                    className="flex items-center justify-between border-b pb-2"
                  >
                    <div className="flex items-center">
                      {account.icon && (
                        <span className="mr-2 text-lg">{account.icon}</span>
                      )}
                      <div>
                        <p className="font-medium">{account.name}</p>
                        <p className="text-sm text-muted-foreground capitalize">
                          {account.type}
                        </p>
                      </div>
                    </div>
                    <p
                      className={
                        (account.balance || 0) >= 0
                          ? "text-green-500"
                          : "text-red-500"
                      }
                    >
                      {formatCurrency(account.balance || 0)}
                    </p>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-4 text-muted-foreground">
                No accounts added yet
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
