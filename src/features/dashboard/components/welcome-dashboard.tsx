"use client";

import { Card, CardContent } from "@/shared/components/ui/card";
import { Button } from "@/shared/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/shared/components/ui/dropdown-menu";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/shared/components/ui/avatar";
import {
  User,
  Users,
  ArrowRight,
  TrendingUp,
  PieChart,
  Target,
  Wallet,
  Heart,
  Shield,
  Sparkles,
  Star,
  CheckCircle,
  LogOut,
  Moon,
  Sun,
  Settings,
  PiggyBank,
} from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useTheme } from "next-themes";
import { createClient } from "@/shared/services/supabase/client";
import { useToast } from "@/shared/hooks/use-toast";

interface User {
  email?: string;
  user_metadata?: {
    full_name?: string;
    avatar_url?: string;
  };
}

interface UserProfile {
  full_name?: string | null;
  avatar_url?: string | null;
}

function getInitials(fullName?: string, email?: string): string {
  if (fullName) {
    const names = fullName.split(" ");
    if (names.length > 1) {
      return `${names[0][0]}${names[names.length - 1][0]}`.toUpperCase();
    }
    return names[0].substring(0, 2).toUpperCase();
  }
  if (email) {
    const emailParts = email.split("@")[0];
    const nameParts = emailParts.split(/[._-]/);
    return nameParts.length > 1
      ? nameParts
          .map((n) => n[0])
          .join("")
          .toUpperCase()
          .substring(0, 2)
      : emailParts.substring(0, 2).toUpperCase();
  }
  return "U";
}

export function WelcomeDashboard() {
  const router = useRouter();
  const { toast } = useToast();
  const { theme, setTheme } = useTheme();
  const [user, setUser] = useState<User | null>(null);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);

  useEffect(() => {
    const getUser = async () => {
      const supabase = createClient();
      const {
        data: { user },
      } = await supabase.auth.getUser();
      setUser(user);

      if (user) {
        const { data: profile } = await supabase
          .from("user_profiles")
          .select("full_name, avatar_url")
          .eq("id", user.id)
          .single();

        if (profile) {
          setUserProfile(profile);
        }
      }
    };
    getUser();

    // Listen for storage changes to refresh avatar
    const handleStorageChange = () => {
      getUser();
    };

    window.addEventListener("avatar-updated", handleStorageChange);
    return () =>
      window.removeEventListener("avatar-updated", handleStorageChange);
  }, []);

  const handleLogout = async () => {
    const supabase = createClient();
    const { error } = await supabase.auth.signOut();

    if (error) {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
      return;
    }

    router.push("/");
    router.refresh();
  };

  const getFirstName = () => {
    const fullName = userProfile?.full_name || user?.user_metadata?.full_name;
    if (fullName) {
      return fullName.split(" ")[0];
    }
    return "there";
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-100/50 to-blue-50/50 dark:from-slate-800/50 dark:to-slate-900/50"></div>

      {/* Integrated Header */}
      <header className="relative z-40 border-b border-white/20 dark:border-slate-700/50 bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl">
        <div className="flex h-16 items-center justify-between px-4 md:px-6">
          <div className="flex items-center gap-4">
            <Link
              href="/dashboard"
              className="flex items-center gap-3 hover:opacity-80 transition-opacity"
            >
              <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg shadow-md">
                <PiggyBank className="h-6 w-6 text-white" />
              </div>
              <h1 className="text-xl font-bold bg-gradient-to-r from-slate-900 to-blue-900 dark:from-white dark:to-blue-100 bg-clip-text text-transparent">
                Budget Tracker
              </h1>
            </Link>
          </div>

          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
              aria-label="Toggle theme"
              className="hover:bg-white/20 dark:hover:bg-slate-800/50"
            >
              <Sun className="h-5 w-5 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
              <Moon className="absolute h-5 w-5 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
              <span className="sr-only">Toggle theme</span>
            </Button>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className="relative h-10 w-10 rounded-full hover:bg-white/20 dark:hover:bg-slate-800/50"
                >
                  <Avatar className="h-9 w-9 ring-2 ring-white/20 dark:ring-slate-700/50">
                    <AvatarImage
                      src={
                        userProfile?.avatar_url ||
                        user?.user_metadata?.avatar_url
                      }
                    />
                    <AvatarFallback className="bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold">
                      {getInitials(
                        userProfile?.full_name ||
                          user?.user_metadata?.full_name,
                        user?.email
                      )}
                    </AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                className="w-64 bg-white/95 dark:bg-slate-900/95 backdrop-blur-xl border-white/20 dark:border-slate-700/50"
                align="end"
                forceMount
              >
                <DropdownMenuLabel className="font-normal">
                  <div className="flex flex-col space-y-2 p-2">
                    <div className="flex items-center gap-3">
                      <Avatar className="h-12 w-12">
                        <AvatarImage
                          src={
                            userProfile?.avatar_url ||
                            user?.user_metadata?.avatar_url
                          }
                        />
                        <AvatarFallback className="bg-gradient-to-r from-blue-500 to-purple-600 text-white">
                          {getInitials(
                            userProfile?.full_name ||
                              user?.user_metadata?.full_name,
                            user?.email
                          )}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="text-sm font-semibold leading-none">
                          {userProfile?.full_name ||
                            user?.user_metadata?.full_name ||
                            "User"}
                        </p>
                        <p className="text-xs leading-none text-muted-foreground mt-1">
                          {user?.email}
                        </p>
                      </div>
                    </div>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator className="bg-white/20 dark:bg-slate-700/50" />
                <Link href="/dashboard/settings" passHref>
                  <DropdownMenuItem className="cursor-pointer hover:bg-white/20 dark:hover:bg-slate-800/50">
                    <Settings className="mr-3 h-4 w-4" />
                    <span>Settings</span>
                  </DropdownMenuItem>
                </Link>
                <DropdownMenuSeparator className="bg-white/20 dark:bg-slate-700/50" />
                <DropdownMenuItem
                  onClick={handleLogout}
                  className="cursor-pointer hover:bg-white/20 dark:hover:bg-slate-800/50 text-red-600 dark:text-red-400"
                >
                  <LogOut className="mr-3 h-4 w-4" />
                  <span>Log out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </header>

      <div className="relative container mx-auto px-4 py-6 space-y-8">
        {/* Compact Welcome Header */}
        <div className="text-center space-y-4">
          <div className="space-y-3">
            {/* Smaller Glowing Icon */}
            <div className="flex justify-center">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full blur-lg opacity-30 animate-pulse"></div>
                <div className="relative p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl shadow-xl">
                  <Sparkles className="h-8 w-8 text-white" />
                </div>
              </div>
            </div>

            <h1 className="text-3xl lg:text-4xl font-bold tracking-tight bg-gradient-to-r from-slate-900 via-blue-900 to-slate-900 dark:from-white dark:via-blue-100 dark:to-white bg-clip-text text-transparent">
              Welcome back,{" "}
              <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                {getFirstName()}!
              </span>{" "}
              👋
            </h1>

            <p className="text-lg text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">
              Choose how you&apos;d like to manage your finances today. Whether
              it&apos;s{" "}
              <span className="font-semibold text-blue-600 dark:text-blue-400">
                personal budgeting
              </span>{" "}
              or{" "}
              <span className="font-semibold text-purple-600 dark:text-purple-400">
                family financial planning
              </span>
              , we&apos;ve got you covered.
            </p>
          </div>

          {/* Compact Feature highlights */}
          <div className="flex flex-wrap gap-3 justify-center">
            <div className="flex items-center gap-2 bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm rounded-full px-4 py-2 border border-slate-200 dark:border-slate-700 shadow-md">
              <Star className="h-4 w-4 text-yellow-500" />
              <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                Smart Insights
              </span>
            </div>
            <div className="flex items-center gap-2 bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm rounded-full px-4 py-2 border border-slate-200 dark:border-slate-700 shadow-md">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                Goal Tracking
              </span>
            </div>
            <div className="flex items-center gap-2 bg-white/70 dark:bg-slate-800/70 backdrop-blur-sm rounded-full px-4 py-2 border border-slate-200 dark:border-slate-700 shadow-md">
              <Shield className="h-4 w-4 text-blue-500" />
              <span className="text-sm font-medium text-slate-700 dark:text-slate-300">
                Secure & Private
              </span>
            </div>
          </div>
        </div>

        {/* Compact Mode Selection Cards */}
        <div className="grid md:grid-cols-2 gap-6 max-w-5xl mx-auto">
          {/* Personal Finance Card */}
          <Link href="/dashboard/personal" className="block h-full group">
            <Card className="relative overflow-hidden bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl shadow-xl border-0 ring-1 ring-slate-200 dark:ring-slate-700 hover:ring-blue-300 dark:hover:ring-blue-600 transition-all duration-300 hover:shadow-2xl hover:scale-[1.01] cursor-pointer h-full flex flex-col">
              {/* Gradient Overlay */}
              <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-transparent to-purple-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

              <CardContent className="relative p-6 space-y-4 flex-1 flex flex-col">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="p-3 rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 shadow-md group-hover:scale-105 transition-transform duration-300">
                      <User className="h-6 w-6 text-white" />
                    </div>
                    <div className="space-y-1">
                      <h3 className="text-xl font-bold text-slate-900 dark:text-white">
                        Personal Finance
                      </h3>
                      <p className="text-sm text-slate-600 dark:text-slate-400">
                        Manage your individual finances
                      </p>
                    </div>
                  </div>
                  <ArrowRight className="h-5 w-5 text-slate-400 group-hover:text-blue-600 group-hover:translate-x-1 transition-all duration-300" />
                </div>

                <div className="space-y-4 flex-1 flex flex-col">
                  <p className="text-slate-600 dark:text-slate-300 text-sm leading-relaxed">
                    Take control of your personal budget, track expenses, and
                    achieve your financial goals with powerful analytics.
                  </p>

                  <div className="grid grid-cols-2 gap-2">
                    <div className="flex items-center space-x-2 p-2 bg-slate-50 dark:bg-slate-800 rounded-lg">
                      <Wallet className="h-4 w-4 text-green-500" />
                      <span className="text-xs font-medium text-slate-700 dark:text-slate-300">
                        Budget Tracking
                      </span>
                    </div>
                    <div className="flex items-center space-x-2 p-2 bg-slate-50 dark:bg-slate-800 rounded-lg">
                      <PieChart className="h-4 w-4 text-blue-500" />
                      <span className="text-xs font-medium text-slate-700 dark:text-slate-300">
                        Expense Analytics
                      </span>
                    </div>
                    <div className="flex items-center space-x-2 p-2 bg-slate-50 dark:bg-slate-800 rounded-lg">
                      <Target className="h-4 w-4 text-purple-500" />
                      <span className="text-xs font-medium text-slate-700 dark:text-slate-300">
                        Financial Goals
                      </span>
                    </div>
                    <div className="flex items-center space-x-2 p-2 bg-slate-50 dark:bg-slate-800 rounded-lg">
                      <TrendingUp className="h-4 w-4 text-orange-500" />
                      <span className="text-xs font-medium text-slate-700 dark:text-slate-300">
                        Growth Insights
                      </span>
                    </div>
                  </div>

                  <div className="pt-2 mt-auto">
                    <Button className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0 shadow-md hover:shadow-lg transition-all duration-200 group-hover:scale-[1.02]">
                      Access Personal Finance
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </Link>

          {/* Family Finance Card */}
          <Link href="/dashboard/family" className="block h-full group">
            <Card className="relative overflow-hidden bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl shadow-xl border-0 ring-1 ring-slate-200 dark:ring-slate-700 hover:ring-green-300 dark:hover:ring-green-600 transition-all duration-300 hover:shadow-2xl hover:scale-[1.01] cursor-pointer h-full flex flex-col">
              {/* Gradient Overlay */}
              <div className="absolute inset-0 bg-gradient-to-br from-green-500/10 via-transparent to-blue-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

              <CardContent className="relative p-6 space-y-4 flex-1 flex flex-col">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="p-3 rounded-xl bg-gradient-to-br from-green-500 to-green-600 shadow-md group-hover:scale-105 transition-transform duration-300">
                      <Users className="h-6 w-6 text-white" />
                    </div>
                    <div className="space-y-1">
                      <h3 className="text-xl font-bold text-slate-900 dark:text-white">
                        Family Finance
                      </h3>
                      <p className="text-sm text-slate-600 dark:text-slate-400">
                        Collaborate on family budgets
                      </p>
                    </div>
                  </div>
                  <ArrowRight className="h-5 w-5 text-slate-400 group-hover:text-green-600 group-hover:translate-x-1 transition-all duration-300" />
                </div>

                <div className="space-y-4 flex-1 flex flex-col">
                  <p className="text-slate-600 dark:text-slate-300 text-sm leading-relaxed">
                    Work together with family members to manage shared expenses,
                    savings, and collaborative financial planning.
                  </p>

                  <div className="grid grid-cols-2 gap-2">
                    <div className="flex items-center space-x-2 p-2 bg-slate-50 dark:bg-slate-800 rounded-lg">
                      <Heart className="h-4 w-4 text-red-500" />
                      <span className="text-xs font-medium text-slate-700 dark:text-slate-300">
                        Shared Budgets
                      </span>
                    </div>
                    <div className="flex items-center space-x-2 p-2 bg-slate-50 dark:bg-slate-800 rounded-lg">
                      <Shield className="h-4 w-4 text-green-500" />
                      <span className="text-xs font-medium text-slate-700 dark:text-slate-300">
                        Safe Collaboration
                      </span>
                    </div>
                    <div className="flex items-center space-x-2 p-2 bg-slate-50 dark:bg-slate-800 rounded-lg">
                      <Target className="h-4 w-4 text-purple-500" />
                      <span className="text-xs font-medium text-slate-700 dark:text-slate-300">
                        Family Goals
                      </span>
                    </div>
                    <div className="flex items-center space-x-2 p-2 bg-slate-50 dark:bg-slate-800 rounded-lg">
                      <TrendingUp className="h-4 w-4 text-orange-500" />
                      <span className="text-xs font-medium text-slate-700 dark:text-slate-300">
                        Joint Planning
                      </span>
                    </div>
                  </div>

                  <div className="pt-2 mt-auto">
                    <Button className="w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white border-0 shadow-md hover:shadow-lg transition-all duration-200 group-hover:scale-[1.02]">
                      Access Family Finance
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </Link>
        </div>

        {/* Compact CTA Section */}
        <div className="max-w-3xl mx-auto">
          <Card className="relative overflow-hidden bg-white/60 dark:bg-slate-900/60 backdrop-blur-xl border-0 ring-1 ring-slate-200 dark:ring-slate-700 shadow-lg">
            {/* Background Pattern */}
            <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 via-purple-500/5 to-green-500/5"></div>

            <CardContent className="relative p-5 text-center space-y-4">
              <div className="space-y-3">
                <div className="flex justify-center">
                  <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
                    <Target className="h-6 w-6 text-white" />
                  </div>
                </div>

                <h3 className="text-lg font-bold text-slate-900 dark:text-white">
                  Ready to get started?
                </h3>

                <p className="text-sm text-slate-600 dark:text-slate-300 max-w-xl mx-auto">
                  Choose your preferred finance mode above to access your
                  dashboard, or explore both options to see what works best.
                </p>
              </div>

              {/* Compact feature highlights */}
              <div className="grid grid-cols-3 gap-3 pt-2">
                <div className="text-center space-y-1">
                  <div className="flex justify-center">
                    <Sparkles className="h-5 w-5 text-yellow-500" />
                  </div>
                  <p className="text-xs font-medium text-slate-700 dark:text-slate-300">
                    Smart Insights
                  </p>
                </div>
                <div className="text-center space-y-1">
                  <div className="flex justify-center">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  </div>
                  <p className="text-xs font-medium text-slate-700 dark:text-slate-300">
                    Goal Achievement
                  </p>
                </div>
                <div className="text-center space-y-1">
                  <div className="flex justify-center">
                    <Shield className="h-5 w-5 text-blue-500" />
                  </div>
                  <p className="text-xs font-medium text-slate-700 dark:text-slate-300">
                    Secure Platform
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
