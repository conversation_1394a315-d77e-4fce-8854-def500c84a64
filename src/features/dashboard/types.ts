// Dashboard-specific types

export type BudgetPeriod = 'monthly' | 'quarterly' | 'yearly' | 'one-time';
export type GoalStatus = 'active' | 'completed' | 'paused' | 'archived';

export interface Budget {
  id: string;
  user_id: string;
  name: string;
  amount: number;
  category_id: string;
  period: BudgetPeriod;
  currency: string;
  start_date: string;
  end_date?: string;
  created_at: string;
  updated_at: string;
  // Populated fields
  category_name?: string;
  category_color?: string;
  spent?: number;
  remaining?: number;
  progress?: number;
}

export interface Account {
  id: string;
  user_id: string;
  name: string;
  type: string;
  balance: number;
  currency: string;
  color?: string;
  created_at: string;
  updated_at: string;
}

export interface Goal {
  id: string;
  user_id: string;
  name: string;
  target_amount: number;
  current_amount: number;
  target_date: string;
  status: GoalStatus;
  description?: string;
  icon?: string;
  created_at: string;
  updated_at: string;
  // Calculated fields
  progress: number;
  daysRemaining: number;
  monthlyRequired: number;
  is_active?: boolean;
}