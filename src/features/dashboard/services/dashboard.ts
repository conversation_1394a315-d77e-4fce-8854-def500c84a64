"use server";

import { createClient } from "@/shared/services/supabase/server";

import type { Transaction as AppTransaction } from "@/shared/types"; // For general Transaction type
import type {
  Budget,
  BudgetPeriod,
  Account as AppAccount,
  Goal,
  GoalStatus,
} from "./types"; // For Supabase specific types

interface SpendingByCategory {
  name: string;
  value: number;
  fill: string;
}

interface DashboardData {
  user: unknown;
  totalBalance: number;
  monthlyIncome: number;
  monthlyExpenses: number;
  recentTransactions: AppTransaction[];
  accounts: AppAccount[];
  netCashFlow: number;
  savingsRate: number;
  incomeToExpenseRatio: number;
  previousTotalBalance: number;
  previousMonthlyIncome: number;
  previousMonthlyExpenses: number;
  spendingByCategory: SpendingByCategory[];
  allTransactions: AppTransaction[];
  budgets: Budget[];
  activeGoalsCount: number;
  financialGoalsPreview: Goal[];
}

/**
 * Calculates the start and end dates for a budget's current active period.
 */
function getPeriodDateRange(
  budget: Budget,
  currentDate: Date
): { startDate: string; endDate: string } {
  let startDate: Date;
  let endDate: Date;

  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth();

  switch (budget.period) {
    case "monthly":
      startDate = new Date(currentYear, currentMonth, 1);
      endDate = new Date(currentYear, currentMonth + 1, 0);
      break;
    case "quarterly":
      const currentQuarter = Math.floor(currentMonth / 3);
      startDate = new Date(currentYear, currentQuarter * 3, 1);
      endDate = new Date(currentYear, currentQuarter * 3 + 3, 0);
      break;
    case "yearly":
      startDate = new Date(currentYear, 0, 1);
      endDate = new Date(currentYear, 11, 31);
      break;
    case "one-time":
      startDate = new Date(budget.start_date);
      // If end_date is not set for a one-time budget, assume it's ongoing up to the current date for spent calculation.
      endDate = budget.end_date ? new Date(budget.end_date) : currentDate;
      break;
    default:
      // Should not happen with proper typing, but default to current month
      startDate = new Date(currentYear, currentMonth, 1);
      endDate = new Date(currentYear, currentMonth + 1, 0);
  }
  return {
    startDate: startDate.toISOString().split("T")[0],
    endDate: endDate.toISOString().split("T")[0],
  };
}

/**
 * Fetches budgets for the current user and calculates spent amounts.
 */
async function getBudgetsWithSpentAmount(
  userId: string,
  userCurrency: string
): Promise<Budget[]> {
  const supabase = await createClient();

  const { data: rawBudgets, error: budgetsError } = await supabase
    .from("budgets")
    .select("*, categories:category_id(name, color)")
    .eq("user_id", userId);

  if (budgetsError) {
    console.error("Error fetching budgets:", budgetsError);
    throw new Error("Failed to fetch budgets.");
  }

  if (!rawBudgets) {
    return [];
  }

  const currentDate = new Date();
  const processedBudgets = await Promise.all(
    rawBudgets.map(async (rawBudget: Record<string, unknown>) => {
      // Map Supabase raw budget to our Budget type explicitly
      const budget: Budget = {
        id: rawBudget.id as string,
        user_id: rawBudget.user_id as string,
        name: rawBudget.name as string,
        amount: rawBudget.amount as number,
        category_id: rawBudget.category_id as string,
        period: rawBudget.period as BudgetPeriod,
        currency: (rawBudget.currency as string) || userCurrency,
        start_date: rawBudget.start_date as string,
        end_date: (rawBudget.end_date as string | null) || undefined,
        created_at: rawBudget.created_at as string,
        updated_at: rawBudget.updated_at as string,
        // Populated fields from joined categories data
        category_name: (rawBudget.categories as { name?: string })?.name,
        category_color: (rawBudget.categories as { color?: string })?.color,
        // spent_amount, remaining_amount, progress will be calculated next
      };

      const { startDate, endDate } = getPeriodDateRange(budget, currentDate);

      const { data: transactions, error: transactionsError } = await supabase
        .from("transactions")
        .select("amount")
        .eq("user_id", userId)
        .eq("category_id", budget.category_id)
        .eq("type", "expense") // Only consider expenses for budget spending
        .eq("currency", userCurrency) // Filter by userCurrency for spent amount calculation
        .gte("date", startDate)
        .lte("date", endDate);

      if (transactionsError) {
        console.error(
          `Error fetching transactions for budget ${budget.name}:`,
          transactionsError
        );
        // Continue processing other budgets, but this one will have 0 spent
        budget.spent = 0;
      } else {
        budget.spent = transactions.reduce(
          (sum: number, t: { amount: number | null }) => sum + (t.amount || 0),
          0
        );
      }

      budget.remaining = budget.amount - (budget.spent || 0);
      budget.progress =
        budget.amount > 0 ? ((budget.spent || 0) / budget.amount) * 100 : 0;

      return budget;
    })
  );

  return processedBudgets;
}

/**
 * Fetches dashboard data for the current user
 * This is a server action and should only be called from server components
 */
export async function getDashboardData(): Promise<DashboardData> {
  const supabase = await createClient();
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user) {
    console.error("Error fetching user or no user found:", userError);
    return {
      user: null,
      totalBalance: 0,
      monthlyIncome: 0,
      monthlyExpenses: 0,
      recentTransactions: [],
      accounts: [],
      netCashFlow: 0,
      savingsRate: 0,
      incomeToExpenseRatio: 1,
      previousTotalBalance: 0,
      previousMonthlyIncome: 0,
      previousMonthlyExpenses: 0,
      spendingByCategory: [],
      allTransactions: [],
      budgets: [],
      activeGoalsCount: 0,
      financialGoalsPreview: [],
    };
  }
  const userId = user.id;

  // Fetch user's currency
  let userCurrency = "USD"; // Default fallback
  const { data: userProfile, error: profileError } = await supabase
    .from("user_profiles")
    .select("currency") // Changed from default_currency
    .eq("id", userId)
    .single();

  if (profileError) {
    console.warn(
      `Warning fetching user profile for currency (user: ${userId}):`,
      profileError.message,
      "Falling back to USD."
    );
    // Keep userCurrency as 'USD'
  } else if (userProfile && userProfile.currency) {
    userCurrency = userProfile.currency;
  } else {
    console.warn(
      `User profile for ${userId} does not have currency set. Falling back to USD.`
    );
    // Keep userCurrency as 'USD'
  }

  // Ensure userId is available before proceeding (already checked, but good practice)
  if (!userId) {
    console.error(
      "User ID is not available. Cannot fetch user-specific dashboard data."
    );
    return {
      user: user, // User object might still be useful
      totalBalance: 0,
      monthlyIncome: 0,
      monthlyExpenses: 0,
      recentTransactions: [],
      accounts: [],
      netCashFlow: 0,
      savingsRate: 0,
      incomeToExpenseRatio: 1,
      previousTotalBalance: 0,
      previousMonthlyIncome: 0,
      previousMonthlyExpenses: 0,
      spendingByCategory: [],
      allTransactions: [],
      budgets: [],
      activeGoalsCount: 0,
      financialGoalsPreview: [],
    };
  }

  const today = new Date();
  const startOfMonth = new Date(
    today.getFullYear(),
    today.getMonth(),
    1
  ).toISOString();
  const endOfMonth = new Date(
    today.getFullYear(),
    today.getMonth() + 1,
    0
  ).toISOString();
  const startOfPreviousMonth = new Date(
    today.getFullYear(),
    today.getMonth() - 1,
    1
  ).toISOString();
  const endOfPreviousMonth = new Date(
    today.getFullYear(),
    today.getMonth(),
    0
  ).toISOString();

  // Fetch total balance from accounts matching userCurrency
  const { data: accountsData, error: accountsError } = await supabase
    .from("accounts")
    .select("balance")
    .eq("user_id", userId)
    .eq("currency", userCurrency); // Filter by userCurrency

  if (accountsError) {
    console.error("Error fetching accounts:", accountsError);
    throw new Error("Failed to fetch account data");
  }

  const totalBalance = accountsData.reduce(
    (sum: number, account: { balance: number | null }) =>
      sum + (account.balance || 0),
    0
  );

  // Fetch monthly income (filtered by userCurrency)
  const { data: incomeData, error: incomeError } = await supabase
    .from("transactions")
    .select("amount")
    .eq("user_id", userId)
    .eq("type", "income")
    .eq("currency", userCurrency) // Filter by userCurrency
    .gte("date", startOfMonth)
    .lte("date", endOfMonth);

  if (incomeError) {
    console.error("Error fetching income:", incomeError);
    throw new Error("Failed to fetch income data");
  }

  const monthlyIncome = incomeData.reduce(
    (sum: number, transaction: { amount: number | null }) =>
      sum + (transaction.amount || 0),
    0
  );

  // Fetch monthly expenses (filtered by userCurrency)
  const { data: expensesData, error: expensesError } = await supabase
    .from("transactions")
    .select("amount")
    .eq("user_id", userId)
    .eq("type", "expense")
    .eq("currency", userCurrency) // Filter by userCurrency
    .gte("date", startOfMonth)
    .lte("date", endOfMonth);

  if (expensesError) {
    console.error("Error fetching expenses:", expensesError);
    throw new Error("Failed to fetch expenses data");
  }

  const monthlyExpenses = expensesData.reduce(
    (sum: number, transaction: { amount: number | null }) =>
      sum + (transaction.amount || 0),
    0
  );

  // Fetch previous monthly income (filtered by userCurrency)
  const { data: previousIncomeData, error: previousIncomeError } =
    await supabase
      .from("transactions")
      .select("amount")
      .eq("user_id", userId)
      .eq("type", "income")
      .eq("currency", userCurrency) // Filter by userCurrency
      .gte("date", startOfPreviousMonth)
      .lte("date", endOfPreviousMonth);

  if (previousIncomeError) {
    console.error("Error fetching previous income:", previousIncomeError);
    throw new Error("Failed to fetch previous income data");
  }

  const previousMonthlyIncome = previousIncomeData.reduce(
    (sum: number, transaction: { amount: number | null }) =>
      sum + (transaction.amount || 0),
    0
  );

  // Fetch previous monthly expenses (filtered by userCurrency)
  const { data: previousExpensesData, error: previousExpensesError } =
    await supabase
      .from("transactions")
      .select("amount")
      .eq("user_id", userId)
      .eq("type", "expense")
      .eq("currency", userCurrency) // Filter by userCurrency
      .gte("date", startOfPreviousMonth)
      .lte("date", endOfPreviousMonth);

  if (previousExpensesError) {
    console.error("Error fetching previous expenses:", previousExpensesError);
    throw new Error("Failed to fetch previous expenses data");
  }

  const previousMonthlyExpenses = previousExpensesData.reduce(
    (sum: number, transaction: { amount: number | null }) =>
      sum + (transaction.amount || 0),
    0
  );

  // Fetch previous total balance (this is an approximation based on userCurrency transactions and accounts)
  // This approximation assumes changes in non-userCurrency accounts/transactions are not part of this balance change.
  const previousTotalBalance = totalBalance - (monthlyIncome - monthlyExpenses); // This calculation remains an approximation

  // Calculate savings rate (based on userCurrency income/expenses)
  const totalIncome = monthlyIncome;
  const totalExpenses = monthlyExpenses;
  const savingsRate =
    totalIncome > 0 ? ((totalIncome - totalExpenses) / totalIncome) * 100 : 0;

  // Fetch recent transactions (limit 5 for the overview card)
  const { data: transactionsData, error: transactionsError } = await supabase
    .from("transactions")
    .select("*, categories(name, type, color), accounts(name, type, color)")
    .eq("user_id", userId)
    .order("date", { ascending: false })
    .limit(5);

  if (transactionsError) {
    console.error("Error fetching recent transactions:", transactionsError);
    throw new Error("Failed to fetch recent transactions data");
  }

  // Fetch ALL transactions for the transactions tab
  const { data: allTransactionsData, error: allTransactionsError } =
    await supabase
      .from("transactions")
      .select("*, categories(name, type, color), accounts(name, type, color)")
      .eq("user_id", userId)
      .order("date", { ascending: false });

  if (allTransactionsError) {
    console.error("Error fetching all transactions:", allTransactionsError);
    throw new Error("Failed to fetch all transactions data");
  }

  // Fetch accounts for dashboard display
  const { data: accounts, error: accountsListError } = await supabase
    .from("accounts")
    .select("*")
    .eq("user_id", userId);

  if (accountsListError) {
    console.error("Error fetching accounts list:", accountsListError);
    throw new Error("Failed to fetch accounts list");
  }

  // Fetch spending by category for the current month (filtered by userCurrency)
  const { data: spendingData, error: spendingError } = await supabase
    .from("transactions")
    .select("amount, categories(name, color)")
    .eq("user_id", userId)
    .eq("type", "expense")
    .eq("currency", userCurrency) // Filter by userCurrency
    .gte("date", startOfMonth)
    .lte("date", endOfMonth);

  if (spendingError) {
    console.error("Error fetching spending by category:", spendingError);
    // Don't throw, allow dashboard to load with partial data or an empty chart
  }

  const spendingByCategoryMap = new Map<
    string,
    { value: number; fill: string }
  >();
  if (spendingData) {
    spendingData.forEach(
      (item: {
        categories: { name: string; color?: string }[] | null;
        amount: number | null;
      }) => {
        // Ensure item.categories is an array and has at least one element
        if (
          item.categories &&
          Array.isArray(item.categories) &&
          item.categories.length > 0 &&
          item.amount
        ) {
          const category = item.categories[0]; // Take the first category
          const categoryName = category.name;
          const categoryColor = category.color || "#cccccc"; // Default color
          const currentTotal =
            spendingByCategoryMap.get(categoryName)?.value || 0;
          spendingByCategoryMap.set(categoryName, {
            value: currentTotal + item.amount,
            fill: categoryColor,
          });
        }
      }
    );
  }

  const spendingByCategory: SpendingByCategory[] = Array.from(
    spendingByCategoryMap.entries()
  ).map(([name, data]) => ({
    name,
    value: data.value,
    fill: data.fill,
  }));

  // Fetch budgets (getBudgetsWithSpentAmount needs to be userCurrency aware for its transaction summing)
  const budgets = await getBudgetsWithSpentAmount(userId, userCurrency); // Pass userCurrency

  // Fetch financial goals preview (financialGoalsPreview are assumed to be in userCurrency)
  const { data: goalsData, error: goalsError } = await supabase
    .from("financial_goals") // Changed table name
    .select("*")
    .eq("user_id", userId)
    .eq("status", "active") // Changed filter to status
    .order("target_date", { ascending: true, nullsFirst: false })
    .order("updated_at", { ascending: false });

  if (goalsError) {
    console.error("Error fetching goals:", JSON.stringify(goalsError, null, 2)); // Enhanced logging
    // Continue without goals data if there's an error
  } else if (goalsData) {
    const activeGoalsCount = goalsData.length;
    const financialGoalsPreview = goalsData
      .slice(0, 3)
      .map((goal: Record<string, unknown>) => {
        const targetDate = new Date(goal.target_date as string);
        const today = new Date();
        const timeDiff = targetDate.getTime() - today.getTime();
        const daysRemaining = Math.max(
          0,
          Math.ceil(timeDiff / (1000 * 60 * 60 * 24))
        );
        const targetAmount = goal.target_amount as number;
        const currentAmount = goal.current_amount as number;
        const progress =
          targetAmount > 0 ? (currentAmount / targetAmount) * 100 : 0;

        let monthlyRequired = 0;
        if (daysRemaining > 0 && targetAmount > currentAmount) {
          const monthsRemaining = daysRemaining / 30.4375; // Average days in a month
          monthlyRequired = (targetAmount - currentAmount) / monthsRemaining;
        }

        return {
          id: goal.id as string,
          user_id: goal.user_id as string,
          name: goal.name as string,
          target_amount: targetAmount,
          current_amount: currentAmount,
          currency: (goal.currency as string) || userCurrency,
          target_date: goal.target_date as string,
          is_active: goal.status === "active", // Derive is_active if needed, or remove if Goal type doesn't need it
          status: goal.status as GoalStatus, // Use actual status from DB
          description: (goal.description as string) || undefined,
          icon: (goal.icon as string) || undefined, // Assuming icon is part of your Goal table/type in DB
          created_at: goal.created_at as string,
          updated_at: goal.updated_at as string,
          progress: Math.min(100, progress), // Cap progress at 100
          daysRemaining: daysRemaining,
          monthlyRequired: Math.max(0, monthlyRequired),
        };
      });

    return {
      user,
      totalBalance,
      monthlyIncome,
      monthlyExpenses,
      recentTransactions: transactionsData || [],
      accounts: accounts || [],
      netCashFlow: monthlyIncome - monthlyExpenses,
      savingsRate,
      incomeToExpenseRatio:
        monthlyIncome > 0 ? (monthlyExpenses / monthlyIncome) * 100 : 0,
      previousTotalBalance,
      previousMonthlyIncome,
      previousMonthlyExpenses,
      spendingByCategory,
      allTransactions: allTransactionsData || [],
      budgets,
      activeGoalsCount,
      financialGoalsPreview,
    };
  }

  return {
    user,
    totalBalance,
    monthlyIncome,
    monthlyExpenses,
    recentTransactions: transactionsData || [],
    accounts: accounts || [],
    netCashFlow: monthlyIncome - monthlyExpenses,
    savingsRate,
    incomeToExpenseRatio:
      monthlyIncome > 0 ? (monthlyExpenses / monthlyIncome) * 100 : 0,
    previousTotalBalance,
    previousMonthlyIncome,
    previousMonthlyExpenses,
    spendingByCategory,
    allTransactions: allTransactionsData || [],
    budgets,
    activeGoalsCount: 0,
    financialGoalsPreview: [],
  };
}
