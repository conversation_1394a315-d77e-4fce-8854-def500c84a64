import type {
  Transaction,
  Account,
  Budget,
  Goal,
  BudgetPeriod,
} from "@/shared/types";

// Re-export types for use in this module
export type { Transaction, Account, Budget, Goal, BudgetPeriod };

// Additional export for Goal status if needed
export type GoalStatus = "active" | "completed" | "paused";

// Dashboard service types
export interface DashboardData {
  totalBalance: number;
  monthlyIncome: number;
  monthlyExpenses: number;
  netCashFlow: number;
  savingsRate: number;
  incomeToExpenseRatio: number;
  activeBudgets: number;
  activeGoalsCount: number;
  previousTotalBalance?: number;
  previousMonthlyIncome?: number;
  previousMonthlyExpenses?: number;
  recentTransactions: Transaction[];
  accounts: Account[];
  spendingByCategory: SpendingByCategoryItem[];
  allTransactions: Transaction[];
  budgets: Budget[];
  financialGoalsPreview: Goal[];
}

export interface SpendingByCategoryItem {
  name: string;
  value: number;
  fill: string;
}
