// Subscription service for managing recurring payments and bills
import { createClient } from "@/shared/services/supabase/client";
import type {
  SubscriptionWithDetails,
  SubscriptionSummary,
  CreateSubscriptionData,
  UpdateSubscriptionData,
  UpcomingPayment,
} from "@/shared/types/subscriptions";

const supabase = createClient();

export class SubscriptionService {
  // Get user's personal subscriptions
  static async getUserSubscriptions(): Promise<SubscriptionWithDetails[]> {
    const { data, error } = await supabase.rpc("get_user_subscriptions");

    if (error) {
      console.error("Error fetching user subscriptions:", error);
      throw new Error(`Failed to fetch subscriptions: ${error.message}`);
    }

    return (data as unknown as SubscriptionWithDetails[]) || [];
  }

  // Get family group subscriptions
  static async getFamilySubscriptions(
    groupId: string
  ): Promise<SubscriptionWithDetails[]> {
    const { data, error } = await supabase.rpc(
      "get_family_group_subscriptions",
      {
        group_uuid: groupId,
      }
    );

    if (error) {
      console.error("Error fetching family subscriptions:", error);
      throw new Error(`Failed to fetch family subscriptions: ${error.message}`);
    }

    return (data as unknown as SubscriptionWithDetails[]) || [];
  }

  // Get subscription summary for dashboard
  static async getSubscriptionSummary(): Promise<SubscriptionSummary> {
    const { data, error } = await supabase.rpc("get_subscription_summary");

    if (error) {
      console.error("Error fetching subscription summary:", error);
      throw new Error(`Failed to fetch subscription summary: ${error.message}`);
    }

    return (
      (data as unknown as SubscriptionSummary[])?.[0] || {
        total_monthly_cost: 0,
        active_subscriptions: 0,
        upcoming_payments_7_days: 0,
        expiring_soon: 0,
      }
    );
  }

  // Get family subscription summary
  static async getFamilySubscriptionSummary(
    groupId: string
  ): Promise<SubscriptionSummary> {
    const { data, error } = await supabase.rpc(
      "get_family_subscription_summary",
      {
        group_uuid: groupId,
      }
    );

    if (error) {
      console.error("Error fetching family subscription summary:", error);
      throw new Error(
        `Failed to fetch family subscription summary: ${error.message}`
      );
    }

    return (
      (data as unknown as SubscriptionSummary[])?.[0] || {
        total_monthly_cost: 0,
        active_subscriptions: 0,
        upcoming_payments_7_days: 0,
        expiring_soon: 0,
      }
    );
  }

  // Create a new subscription
  static async createSubscription(
    data: CreateSubscriptionData
  ): Promise<string> {
    const { data: subscriptionId, error } = await supabase.rpc(
      "create_subscription",
      {
        subscription_name: data.name,
        subscription_amount: data.amount,
        subscription_billing_cycle: data.billing_cycle,
        subscription_start_date: data.start_date,
        subscription_description: data.description || null,
        subscription_currency: data.currency || "USD",
        subscription_end_date: data.end_date || null,
        subscription_category_id: data.category_id || null,
        subscription_account_id: data.account_id || null,
        subscription_provider: data.provider || null,
        subscription_payment_method: data.payment_method || null,
        subscription_reminder_days: data.reminder_days || 3,
        subscription_notes: data.notes || null,
        subscription_family_group_id: data.family_group_id || null,
      }
    );

    if (error) {
      console.error("Error creating subscription:", error);
      throw new Error(`Failed to create subscription: ${error.message}`);
    }

    return subscriptionId as unknown as string;
  }

  // Update a subscription
  static async updateSubscription(
    id: string,
    data: UpdateSubscriptionData
  ): Promise<boolean> {
    const { data: success, error } = await supabase.rpc("update_subscription", {
      subscription_id: id,
      subscription_name: data.name || null,
      subscription_description: data.description || null,
      subscription_amount: data.amount || null,
      subscription_currency: data.currency || null,
      subscription_billing_cycle: data.billing_cycle || null,
      subscription_start_date: data.start_date || null,
      subscription_end_date: data.end_date || null,
      subscription_category_id: data.category_id || null,
      subscription_account_id: data.account_id || null,
      subscription_provider: data.provider || null,
      subscription_payment_method: data.payment_method || null,
      subscription_reminder_days: data.reminder_days || null,
      subscription_notes: data.notes || null,
      subscription_is_active: data.is_active ?? null,
      subscription_auto_renew: data.auto_renew ?? null,
    });

    if (error) {
      console.error("Error updating subscription:", error);
      throw new Error(`Failed to update subscription: ${error.message}`);
    }

    return (success as boolean) || false;
  }

  // Delete a subscription
  static async deleteSubscription(id: string): Promise<boolean> {
    const { data: success, error } = await supabase.rpc("delete_subscription", {
      subscription_id: id,
    });

    if (error) {
      console.error("Error deleting subscription:", error);
      throw new Error(`Failed to delete subscription: ${error.message}`);
    }

    return (success as boolean) || false;
  }

  // Toggle subscription active status
  static async toggleSubscriptionStatus(
    id: string,
    isActive: boolean
  ): Promise<boolean> {
    return this.updateSubscription(id, { is_active: isActive });
  }

  // Get upcoming payments across all subscriptions
  static async getUpcomingPayments(
    daysAhead: number = 30
  ): Promise<UpcomingPayment[]> {
    const { data, error } = await supabase.rpc("get_upcoming_payments", {
      days_ahead: daysAhead,
    });

    if (error) {
      console.error("Error fetching upcoming payments:", error);
      throw new Error(`Failed to fetch upcoming payments: ${error.message}`);
    }

    return (data as unknown as UpcomingPayment[]) || [];
  }

  // Get subscription by ID (simplified to use RPC for now to avoid table type issues)
  static async getSubscriptionById(
    id: string
  ): Promise<SubscriptionWithDetails | null> {
    try {
      const subscriptions = await this.getUserSubscriptions();
      return subscriptions.find((sub) => sub.id === id) || null;
    } catch (error) {
      console.error("Error fetching subscription by ID:", error);
      return null;
    }
  }

  // Get subscription spending by category (method removed due to type constraints)
  // This functionality can be implemented once subscriptions table is added to Supabase types
}
