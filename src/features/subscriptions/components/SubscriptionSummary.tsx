"use client";

import React from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/shared/components/ui/card";
import { Badge } from "@/shared/components/ui/badge";
import { <PERSON><PERSON> } from "@/shared/components/ui/button";
import {
  DollarSign,
  Calendar,
  RefreshCw,
  Alert<PERSON>riangle,
  TrendingUp,
  Plus,
} from "lucide-react";
import { Progress } from "@/shared/components/ui/progress";
import Link from "next/link";
import type { SubscriptionSummary } from "@/shared/types/subscriptions";
import { useCurrencyFormatter } from "@/shared/hooks/useCurrencyFormatter";

interface SubscriptionSummaryProps {
  summary: SubscriptionSummary;
  familyGroupId?: string;
  onAddSubscription?: () => void;
}

export function SubscriptionSummaryWidget({
  summary,
  familyGroupId,
  onAddSubscription,
}: SubscriptionSummaryProps) {
  const { formatCurrency } = useCurrencyFormatter();

  const getSubscriptionsLink = () => {
    return familyGroupId
      ? `/dashboard/family/${familyGroupId}/subscriptions`
      : "/dashboard/subscriptions";
  };

  const getUrgencyLevel = () => {
    if (summary.upcoming_payments_7_days > 3) return "high";
    if (summary.upcoming_payments_7_days > 1) return "medium";
    return "low";
  };

  const urgencyLevel = getUrgencyLevel();

  return (
    <Card className="relative overflow-hidden">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg font-semibold flex items-center">
              <RefreshCw className="mr-2 h-5 w-5 text-primary" />
              Subscriptions
            </CardTitle>
            <CardDescription>
              {familyGroupId ? "Family" : "Personal"} recurring payments
            </CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            {onAddSubscription && (
              <Button
                size="sm"
                variant="outline"
                onClick={onAddSubscription}
                className="h-8"
              >
                <Plus className="h-4 w-4 mr-1" />
                Add
              </Button>
            )}
            <Link href={getSubscriptionsLink()}>
              <Button size="sm" variant="outline" className="h-8">
                View All
              </Button>
            </Link>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Monthly Cost Overview */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <DollarSign className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium">Monthly Total</span>
            </div>
            <p className="text-2xl font-bold text-green-600">
              {formatCurrency(summary.total_monthly_cost)}
            </p>
            <p className="text-xs text-muted-foreground">
              {summary.active_subscriptions} active subscription
              {summary.active_subscriptions !== 1 ? "s" : ""}
            </p>
          </div>

          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-orange-600" />
              <span className="text-sm font-medium">Due This Week</span>
            </div>
            <div className="flex items-center space-x-2">
              <p className="text-2xl font-bold text-orange-600">
                {summary.upcoming_payments_7_days}
              </p>
              {urgencyLevel === "high" && (
                <Badge variant="destructive" className="text-xs">
                  <AlertTriangle className="h-3 w-3 mr-1" />
                  High
                </Badge>
              )}
              {urgencyLevel === "medium" && (
                <Badge
                  variant="outline"
                  className="text-xs border-orange-500 text-orange-600"
                >
                  Medium
                </Badge>
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              Payments due in next 7 days
            </p>
          </div>
        </div>

        {/* Progress Indicators */}
        {summary.total_monthly_cost > 0 && (
          <div className="space-y-3">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Monthly Spending</span>
              <span className="font-medium">
                {formatCurrency(summary.total_monthly_cost)}
              </span>
            </div>

            {/* Visual indicator of subscription load */}
            <div className="space-y-2">
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>Subscription density</span>
                <span>{summary.active_subscriptions} services</span>
              </div>
              <Progress
                value={Math.min((summary.active_subscriptions / 10) * 100, 100)}
                className="h-2"
              />
            </div>
          </div>
        )}

        {/* Expiring Soon Alert */}
        {summary.expiring_soon > 0 && (
          <div className="mt-4 p-3 bg-orange-50 dark:bg-orange-950/20 rounded-lg border border-orange-200 dark:border-orange-800">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-4 w-4 text-orange-600" />
              <p className="text-sm font-medium text-orange-800 dark:text-orange-200">
                {summary.expiring_soon} subscription
                {summary.expiring_soon !== 1 ? "s" : ""} expiring soon
              </p>
            </div>
            <p className="text-xs text-orange-600 dark:text-orange-300 mt-1">
              Review your subscriptions to avoid unexpected cancellations
            </p>
          </div>
        )}

        {/* Quick Actions */}
        <div className="pt-2 border-t">
          <div className="flex items-center justify-between text-sm">
            <Link
              href={getSubscriptionsLink()}
              className="flex items-center text-primary hover:underline"
            >
              <TrendingUp className="h-4 w-4 mr-1" />
              View spending trends
            </Link>
            {summary.upcoming_payments_7_days > 0 && (
              <Link
                href={`${getSubscriptionsLink()}?filter=upcoming`}
                className="text-orange-600 hover:underline"
              >
                Review upcoming
              </Link>
            )}
          </div>
        </div>

        {/* Empty State */}
        {summary.active_subscriptions === 0 && (
          <div className="text-center py-6">
            <RefreshCw className="h-8 w-8 text-muted-foreground mx-auto mb-3" />
            <p className="text-sm text-muted-foreground mb-3">
              No subscriptions tracked yet
            </p>
            {onAddSubscription && (
              <Button size="sm" onClick={onAddSubscription} className="h-8">
                <Plus className="h-4 w-4 mr-1" />
                Add Your First Subscription
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
