"use client";

import React from "react";
import { Badge } from "@/shared/components/ui/badge";
import { Button } from "@/shared/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/shared/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/shared/components/ui/dropdown-menu";
import { CategoryIcon } from "@/shared/components/ui/category-icon";
import {
  MoreHorizontal,
  Calendar,
  CreditCard,
  DollarSign,
  Pause,
  Play,
  Edit,
  Trash2,
  AlertTriangle,
} from "lucide-react";
import { format } from "date-fns";
import type { SubscriptionWithDetails } from "@/shared/types/subscriptions";
import { BILLING_CYCLE_LABELS } from "@/shared/types/subscriptions";
import { useCurrencyFormatter } from "@/shared/hooks/useCurrencyFormatter";

interface SubscriptionCardProps {
  subscription: SubscriptionWithDetails;
  onEdit?: (subscription: SubscriptionWithDetails) => void;
  onDelete?: (subscription: SubscriptionWithDetails) => void;
  onToggleStatus?: (subscription: SubscriptionWithDetails) => void;
  showFamilyIndicator?: boolean;
}

export function SubscriptionCard({
  subscription,
  onEdit,
  onDelete,
  onToggleStatus,
  showFamilyIndicator = false,
}: SubscriptionCardProps) {
  const { formatCurrency } = useCurrencyFormatter();

  const getStatusBadge = () => {
    if (!subscription.is_active) {
      return <Badge variant="secondary">Inactive</Badge>;
    }

    if (subscription.days_until_payment <= 0) {
      return <Badge variant="destructive">Due Now</Badge>;
    }

    if (subscription.days_until_payment <= 3) {
      return <Badge variant="destructive">Due Soon</Badge>;
    }

    if (subscription.days_until_payment <= 7) {
      return <Badge variant="outline">Due This Week</Badge>;
    }

    return <Badge variant="default">Active</Badge>;
  };

  const getDaysText = () => {
    if (subscription.days_until_payment <= 0) {
      return "Due today";
    }
    if (subscription.days_until_payment === 1) {
      return "Due tomorrow";
    }
    return `Due in ${subscription.days_until_payment} days`;
  };

  return (
    <Card
      className={`transition-all duration-200 hover:shadow-md ${
        !subscription.is_active ? "opacity-60" : ""
      }`}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3">
            <CategoryIcon
              icon={subscription.category_icon || "subscription"}
              className="h-8 w-8 text-primary"
            />
            <div className="flex-1 min-w-0">
              <CardTitle className="text-lg font-semibold truncate">
                {subscription.name}
              </CardTitle>
              {subscription.provider && (
                <p className="text-sm text-muted-foreground mt-1">
                  {subscription.provider}
                </p>
              )}
              {showFamilyIndicator && subscription.created_by_username && (
                <p className="text-xs text-muted-foreground mt-1">
                  Added by {subscription.created_by_username}
                </p>
              )}
            </div>
          </div>
          <div className="flex items-center space-x-2">
            {getStatusBadge()}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <MoreHorizontal className="h-4 w-4" />
                  <span className="sr-only">Open menu</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {onEdit && (
                  <DropdownMenuItem onClick={() => onEdit(subscription)}>
                    <Edit className="mr-2 h-4 w-4" />
                    Edit
                  </DropdownMenuItem>
                )}
                {onToggleStatus && (
                  <DropdownMenuItem
                    onClick={() => onToggleStatus(subscription)}
                  >
                    {subscription.is_active ? (
                      <>
                        <Pause className="mr-2 h-4 w-4" />
                        Pause
                      </>
                    ) : (
                      <>
                        <Play className="mr-2 h-4 w-4" />
                        Resume
                      </>
                    )}
                  </DropdownMenuItem>
                )}
                {onDelete && (
                  <DropdownMenuItem
                    onClick={() => onDelete(subscription)}
                    className="text-destructive"
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <DollarSign className="h-4 w-4 text-muted-foreground" />
              <div>
                <p className="font-semibold">
                  {formatCurrency(subscription.amount)}
                </p>
                <p className="text-xs text-muted-foreground">
                  {BILLING_CYCLE_LABELS[subscription.billing_cycle]}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium">
                  {format(
                    new Date(subscription.next_billing_date),
                    "MMM dd, yyyy"
                  )}
                </p>
                <p className="text-xs text-muted-foreground">{getDaysText()}</p>
              </div>
            </div>
          </div>
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <CreditCard className="h-4 w-4 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium">
                  {formatCurrency(subscription.monthly_cost)}
                </p>
                <p className="text-xs text-muted-foreground">Monthly equiv.</p>
              </div>
            </div>
            {subscription.category_name && (
              <div className="flex items-center space-x-2">
                <Badge variant="outline" className="text-xs">
                  {subscription.category_name}
                </Badge>
              </div>
            )}
          </div>
        </div>

        {subscription.description && (
          <div className="mt-4 pt-4 border-t">
            <p className="text-sm text-muted-foreground">
              {subscription.description}
            </p>
          </div>
        )}

        {subscription.end_date && (
          <div className="mt-4 pt-4 border-t">
            <div className="flex items-center space-x-2 text-orange-600">
              <AlertTriangle className="h-4 w-4" />
              <p className="text-sm font-medium">
                Expires on{" "}
                {format(new Date(subscription.end_date), "MMM dd, yyyy")}
              </p>
            </div>
          </div>
        )}

        {subscription.notes && (
          <div className="mt-4 pt-4 border-t">
            <p className="text-sm text-muted-foreground">
              <strong>Notes:</strong> {subscription.notes}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
