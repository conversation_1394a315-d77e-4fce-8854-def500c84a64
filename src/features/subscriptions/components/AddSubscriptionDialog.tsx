"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/shared/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/shared/components/ui/dialog";
import { Input } from "@/shared/components/ui/input";
import { Label } from "@/shared/components/ui/label";
import { Textarea } from "@/shared/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/shared/components/ui/select";
// import { DatePicker } from "@/shared/components/ui/date-picker";
// import { Switch } from "@/shared/components/ui/switch";
import { Badge } from "@/shared/components/ui/badge";
import { useToast } from "@/shared/hooks/use-toast";
import { useQuery } from "@tanstack/react-query";
import { Loader2, Search } from "lucide-react";
import { format } from "date-fns";
import { CategoryIcon } from "@/shared/components/ui/category-icon";
import type {
  CreateSubscriptionData,
  SubscriptionWithDetails,
} from "@/shared/types/subscriptions";
import { createClient } from "@/shared/services/supabase/client";

interface AddSubscriptionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: CreateSubscriptionData) => Promise<void>;
  subscription?: SubscriptionWithDetails;
  familyGroupId?: string;
  isLoading?: boolean;
}

const BILLING_CYCLES = [
  { value: "weekly", label: "Weekly" },
  { value: "monthly", label: "Monthly" },
  { value: "quarterly", label: "Quarterly" },
  { value: "yearly", label: "Yearly" },
] as const;

const PAYMENT_METHODS = [
  { value: "bank_transfer", label: "Bank Transfer" },
  { value: "debit_card", label: "Debit Card" },
  { value: "credit_card", label: "Credit Card" },
  { value: "cash", label: "Cash" },
  { value: "check", label: "Check" },
  { value: "paypal", label: "PayPal" },
  { value: "apple_pay", label: "Apple Pay" },
  { value: "google_pay", label: "Google Pay" },
  { value: "venmo", label: "Venmo" },
  { value: "zelle", label: "Zelle" },
  { value: "wire_transfer", label: "Wire Transfer" },
  { value: "ach", label: "ACH Transfer" },
  { value: "cryptocurrency", label: "Cryptocurrency" },
  { value: "other", label: "Other" },
] as const;

const SUBSCRIPTION_PROVIDERS = [
  { name: "Netflix", category: "entertainment", icon: "clapperboard" },
  { name: "Spotify", category: "entertainment", icon: "music" },
  { name: "Apple Music", category: "entertainment", icon: "music" },
  { name: "Amazon Prime", category: "shopping", icon: "package" },
  { name: "YouTube Premium", category: "entertainment", icon: "clapperboard" },
  { name: "Disney+", category: "entertainment", icon: "clapperboard" },
  { name: "Electricity", category: "utilities", icon: "zap" },
  { name: "Gas", category: "utilities", icon: "flame" },
  { name: "Water", category: "utilities", icon: "droplets" },
  { name: "Internet", category: "utilities", icon: "globe" },
  { name: "Phone", category: "utilities", icon: "smartphone" },
  { name: "Gym Membership", category: "health", icon: "dumbbell" },
  { name: "Car Insurance", category: "insurance", icon: "car" },
  { name: "Health Insurance", category: "insurance", icon: "heart-pulse" },
  { name: "Rent", category: "housing", icon: "home" },
  { name: "Mortgage", category: "housing", icon: "home" },
] as const;

export function AddSubscriptionDialog({
  open,
  onOpenChange,
  onSubmit,
  subscription,
  familyGroupId,
  isLoading = false,
}: AddSubscriptionDialogProps) {
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState("");
  const [showProviderSearch, setShowProviderSearch] = useState(false);

  // Fetch user profile to get currency preference
  const { data: userProfile } = useQuery({
    queryKey: ["user-profile"],
    queryFn: async () => {
      const supabase = createClient();
      const { data } = await supabase
        .from("user_profiles")
        .select("currency")
        .single();
      return data;
    },
  });

  // Fetch categories
  const { data: categories = [] } = useQuery({
    queryKey: ["categories", "expense"],
    queryFn: async () => {
      const supabase = createClient();
      const { data } = await supabase
        .from("categories")
        .select("*")
        .eq("type", "expense")
        .is("family_group_id", null);
      return data || [];
    },
  });

  // Fetch accounts
  const { data: accounts = [] } = useQuery({
    queryKey: ["accounts", familyGroupId],
    queryFn: async () => {
      const supabase = createClient();
      if (familyGroupId) {
        // For family subscriptions, get family group accounts
        const { data } = await supabase
          .from("accounts")
          .select("*")
          .eq("family_group_id", familyGroupId);
        return data || [];
      } else {
        // For personal subscriptions, get user's personal accounts
        const { data } = await supabase
          .from("accounts")
          .select("*")
          .is("family_group_id", null);
        return data || [];
      }
    },
  });

  // Initialize form data with user's currency preference
  const [formData, setFormData] = useState<CreateSubscriptionData>({
    name: "",
    description: "",
    amount: 0,
    currency: "USD", // Will be updated in useEffect when userProfile loads
    billing_cycle: "monthly",
    start_date: format(new Date(), "yyyy-MM-dd"),
    category_id: "",
    account_id: "",
    provider: "",
    payment_method: "",
    reminder_days: 3,
    notes: "",
    family_group_id: familyGroupId,
  });

  // Filter providers based on search
  const filteredProviders = SUBSCRIPTION_PROVIDERS.filter((provider) =>
    provider.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Update currency when user profile loads
  useEffect(() => {
    if (userProfile?.currency && !subscription) {
      setFormData((prev) => ({
        ...prev,
        currency: userProfile.currency,
      }));
    }
  }, [userProfile?.currency, subscription]);

  useEffect(() => {
    if (subscription) {
      setFormData({
        name: subscription.name,
        description: subscription.description || "",
        amount: subscription.amount,
        currency: subscription.currency,
        billing_cycle: subscription.billing_cycle,
        start_date: subscription.start_date,
        end_date: subscription.end_date,
        category_id: subscription.category_id || "",
        account_id: subscription.account_id || "",
        provider: subscription.provider || "",
        payment_method: subscription.payment_method || "",
        reminder_days: subscription.reminder_days,
        notes: subscription.notes || "",
        family_group_id: familyGroupId,
      });
    } else {
      // Reset form for new subscription
      setFormData({
        name: "",
        description: "",
        amount: 0,
        currency: userProfile?.currency || "USD",
        billing_cycle: "monthly",
        start_date: format(new Date(), "yyyy-MM-dd"),
        category_id: "",
        account_id: "",
        provider: "",
        payment_method: "",
        reminder_days: 3,
        notes: "",
        family_group_id: familyGroupId,
      });
    }
  }, [subscription, familyGroupId, userProfile?.currency]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      toast({
        title: "Error",
        description: "Subscription name is required",
        variant: "destructive",
      });
      return;
    }

    if (formData.amount <= 0) {
      toast({
        title: "Error",
        description: "Amount must be greater than 0",
        variant: "destructive",
      });
      return;
    }

    try {
      await onSubmit(formData);
      onOpenChange(false);
      toast({
        title: "Success",
        description: subscription
          ? "Subscription updated successfully"
          : "Subscription created successfully",
      });
    } catch (error) {
      console.error("Error saving subscription:", error);
      toast({
        title: "Error",
        description: "Failed to save subscription. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleProviderSelect = (providerName: string) => {
    setFormData((prev) => ({
      ...prev,
      provider: providerName,
      name: providerName,
    }));
    setShowProviderSearch(false);
    setSearchTerm("");
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {subscription ? "Edit Subscription" : "Add New Subscription"}
          </DialogTitle>
          <DialogDescription>
            {subscription
              ? "Update your subscription details below."
              : "Add a new recurring subscription or bill to track."}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Provider Selection */}
          <div className="space-y-2">
            <Label htmlFor="provider">Provider (Optional)</Label>
            <div className="relative">
              <div className="flex space-x-2">
                <Input
                  value={formData.provider}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      provider: e.target.value,
                    }))
                  }
                  placeholder="e.g., Netflix, Spotify, Electric Company"
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowProviderSearch(!showProviderSearch)}
                >
                  <Search className="h-4 w-4" />
                </Button>
              </div>

              {showProviderSearch && (
                <div className="absolute top-full left-0 right-0 mt-1 bg-background border rounded-md shadow-lg z-50 max-h-48 overflow-y-auto">
                  <div className="p-2">
                    <Input
                      placeholder="Search providers..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="mb-2"
                    />
                    <div className="space-y-1">
                      {filteredProviders.map((provider) => (
                        <div
                          key={provider.name}
                          className="flex items-center space-x-2 p-2 hover:bg-accent rounded-sm cursor-pointer"
                          onClick={() => handleProviderSelect(provider.name)}
                        >
                          <span>{provider.icon}</span>
                          <span className="text-sm">{provider.name}</span>
                          <Badge variant="outline" className="text-xs ml-auto">
                            {provider.category}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Basic Information */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Subscription Name *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, name: e.target.value }))
                }
                placeholder="e.g., Netflix Premium"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="amount">Amount *</Label>
              <Input
                id="amount"
                type="number"
                step="0.01"
                min="0"
                value={formData.amount || ""}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    amount: parseFloat(e.target.value) || 0,
                  }))
                }
                placeholder="0.00"
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="billing_cycle">Billing Cycle *</Label>
              <Select
                value={formData.billing_cycle}
                onValueChange={(
                  value: "weekly" | "monthly" | "quarterly" | "yearly"
                ) => setFormData((prev) => ({ ...prev, billing_cycle: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select billing cycle" />
                </SelectTrigger>
                <SelectContent>
                  {BILLING_CYCLES.map((cycle) => (
                    <SelectItem key={cycle.value} value={cycle.value}>
                      {cycle.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="currency">Currency</Label>
              <Select
                value={formData.currency}
                onValueChange={(value) =>
                  setFormData((prev) => ({ ...prev, currency: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select currency" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="USD">USD ($)</SelectItem>
                  <SelectItem value="EUR">EUR (€)</SelectItem>
                  <SelectItem value="GBP">GBP (£)</SelectItem>
                  <SelectItem value="CAD">CAD (C$)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Dates */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="start_date">Start Date</Label>
              <Input
                id="start_date"
                type="date"
                value={formData.start_date}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    start_date: e.target.value,
                  }))
                }
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="end_date">End Date (Optional)</Label>
              <Input
                id="end_date"
                type="date"
                value={formData.end_date || ""}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    end_date: e.target.value || undefined,
                  }))
                }
              />
            </div>
          </div>

          {/* Category and Account */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="category">Category</Label>
              <Select
                value={formData.category_id}
                onValueChange={(value) =>
                  setFormData((prev) => ({ ...prev, category_id: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        <div className="flex items-center space-x-2">
                          <CategoryIcon
                            icon={category.icon || undefined}
                            className="h-4 w-4"
                          />
                          <span>{category.name}</span>
                        </div>
                      </SelectItem>
                    )
                  )}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="account">Account</Label>
              <Select
                value={formData.account_id}
                onValueChange={(value) =>
                  setFormData((prev) => ({ ...prev, account_id: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select account" />
                </SelectTrigger>
                <SelectContent>
                  {accounts.map((account: { id: string; name: string }) => (
                    <SelectItem key={account.id} value={account.id}>
                      {account.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Payment Method and Reminder */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="payment_method">Payment Method</Label>
              <Select
                value={formData.payment_method}
                onValueChange={(value) =>
                  setFormData((prev) => ({ ...prev, payment_method: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select payment method" />
                </SelectTrigger>
                <SelectContent>
                  {PAYMENT_METHODS.map((method) => (
                    <SelectItem key={method.value} value={method.value}>
                      {method.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="reminder_days">Reminder (Days Before)</Label>
              <Input
                id="reminder_days"
                type="number"
                min="0"
                max="30"
                value={formData.reminder_days}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    reminder_days: parseInt(e.target.value) || 3,
                  }))
                }
              />
            </div>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  description: e.target.value,
                }))
              }
              placeholder="Brief description of the subscription"
              rows={2}
            />
          </div>

          {/* Notes */}
          <div className="space-y-2">
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, notes: e.target.value }))
              }
              placeholder="Additional notes or details"
              rows={2}
            />
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {subscription ? "Update Subscription" : "Add Subscription"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
