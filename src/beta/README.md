# Budget Tracker Beta Program

This folder contains the beta implementation of the Finance Type Separation feature, creating three distinct user experiences:

1. **Personal Finance Users** - Individual budgeting and tracking
2. **Family Finance Users** - Collaborative financial management  
3. **Combined Users** - Full access to both personal and family features

## Architecture

The beta implementation leverages the existing codebase while providing focused, tailored experiences for each user type without disrupting the current production system.

## Features

- Enhanced landing page with journey selection
- Tailored onboarding flows for each finance type
- Finance-type-aware component system
- Beta user feedback collection
- Feature flagging and A/B testing
- Progressive feature unlock system

## Usage

Beta users get access to advanced features and provide feedback to help shape the future of the application.