'use client';

import { useState, useCallback } from 'react';
import { useToast } from '@/shared/hooks/use-toast';

export interface ErrorState {
  hasError: boolean;
  error?: Error | null;
  errorMessage?: string;
  errorCode?: string;
  retryCount: number;
}

export interface ErrorHandlingOptions {
  showToast?: boolean;
  toastTitle?: string;
  toastDescription?: string;
  maxRetries?: number;
  onError?: (error: Error) => void;
  onRetry?: () => void;
}

export const useErrorHandling = (options: ErrorHandlingOptions = {}) => {
  const { toast } = useToast();
  const {
    showToast = true,
    toastTitle = 'Error',
    toastDescription = 'Something went wrong. Please try again.',
    maxRetries = 3,
    onError,
    onRetry
  } = options;

  const [errorState, setErrorState] = useState<ErrorState>({
    hasError: false,
    error: null,
    errorMessage: '',
    errorCode: '',
    retryCount: 0
  });

  const handleError = useCallback((error: Error | string, code?: string) => {
    const errorObj = typeof error === 'string' ? new Error(error) : error;
    
    console.error('Error handled:', errorObj);

    setErrorState(prev => ({
      hasError: true,
      error: errorObj,
      errorMessage: errorObj.message,
      errorCode: code || 'UNKNOWN_ERROR',
      retryCount: prev.retryCount
    }));

    if (showToast) {
      toast({
        title: toastTitle,
        description: getErrorMessage(errorObj),
        variant: 'destructive'
      });
    }

    if (onError) {
      onError(errorObj);
    }
  }, [toast, showToast, toastTitle, onError]);

  const clearError = useCallback(() => {
    setErrorState({
      hasError: false,
      error: null,
      errorMessage: '',
      errorCode: '',
      retryCount: 0
    });
  }, []);

  const retry = useCallback(() => {
    if (errorState.retryCount < maxRetries) {
      setErrorState(prev => ({
        ...prev,
        hasError: false,
        retryCount: prev.retryCount + 1
      }));

      if (onRetry) {
        onRetry();
      }
    } else {
      toast({
        title: 'Maximum retries reached',
        description: 'Please refresh the page or contact support if the problem persists.',
        variant: 'destructive'
      });
    }
  }, [errorState.retryCount, maxRetries, onRetry, toast]);

  const canRetry = errorState.retryCount < maxRetries;

  return {
    errorState,
    handleError,
    clearError,
    retry,
    canRetry
  };
};

// Utility function to get user-friendly error messages
export const getErrorMessage = (error: Error): string => {
  // Network errors
  if (error.message.includes('fetch')) {
    return 'Network error. Please check your connection and try again.';
  }

  // Authentication errors
  if (error.message.includes('auth') || error.message.includes('unauthorized')) {
    return 'Authentication error. Please log in again.';
  }

  // Permission errors
  if (error.message.includes('permission') || error.message.includes('forbidden')) {
    return 'You don\'t have permission to perform this action.';
  }

  // Validation errors
  if (error.message.includes('validation') || error.message.includes('invalid')) {
    return 'Please check your input and try again.';
  }

  // Database errors
  if (error.message.includes('database') || error.message.includes('sql')) {
    return 'Database error. Please try again later.';
  }

  // Rate limiting
  if (error.message.includes('rate limit') || error.message.includes('too many requests')) {
    return 'Too many requests. Please wait a moment and try again.';
  }

  // Default message
  return error.message || 'An unexpected error occurred. Please try again.';
};

// Hook for async operations with error handling
export const useAsyncOperation = <T>(
  operation: () => Promise<T>,
  options: ErrorHandlingOptions = {}
) => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<T | null>(null);
  const { errorState, handleError, clearError, retry } = useErrorHandling(options);

  const execute = useCallback(async () => {
    try {
      setLoading(true);
      clearError();
      const result = await operation();
      setData(result);
      return result;
    } catch (error) {
      handleError(error as Error);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [operation, handleError, clearError]);

  const retryOperation = useCallback(() => {
    retry();
    return execute();
  }, [retry, execute]);

  return {
    loading,
    data,
    error: errorState,
    execute,
    retry: retryOperation,
    clearError
  };
};

// Error types for better error handling
export enum ErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  AUTH_ERROR = 'AUTH_ERROR',
  PERMISSION_ERROR = 'PERMISSION_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  RATE_LIMIT_ERROR = 'RATE_LIMIT_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

export class AppError extends Error {
  public type: ErrorType;
  public code?: string;
  public details?: any;

  constructor(
    message: string, 
    type: ErrorType = ErrorType.UNKNOWN_ERROR, 
    code?: string, 
    details?: any
  ) {
    super(message);
    this.name = 'AppError';
    this.type = type;
    this.code = code;
    this.details = details;
  }
}

// Utility function to create specific error types
export const createNetworkError = (message = 'Network error occurred') => 
  new AppError(message, ErrorType.NETWORK_ERROR);

export const createAuthError = (message = 'Authentication failed') => 
  new AppError(message, ErrorType.AUTH_ERROR);

export const createValidationError = (message = 'Validation failed', details?: any) => 
  new AppError(message, ErrorType.VALIDATION_ERROR, undefined, details);

export const createPermissionError = (message = 'Permission denied') => 
  new AppError(message, ErrorType.PERMISSION_ERROR);

// Hook for form error handling
export const useFormErrorHandling = () => {
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});
  const { handleError: handleGlobalError } = useErrorHandling();

  const setFieldError = useCallback((field: string, message: string) => {
    setFieldErrors(prev => ({
      ...prev,
      [field]: message
    }));
  }, []);

  const clearFieldError = useCallback((field: string) => {
    setFieldErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[field];
      return newErrors;
    });
  }, []);

  const clearAllFieldErrors = useCallback(() => {
    setFieldErrors({});
  }, []);

  const handleFormError = useCallback((error: any) => {
    if (error.details && typeof error.details === 'object') {
      // Handle validation errors with field-specific messages
      Object.entries(error.details).forEach(([field, message]) => {
        setFieldError(field, message as string);
      });
    } else {
      // Handle general form errors
      handleGlobalError(error);
    }
  }, [setFieldError, handleGlobalError]);

  return {
    fieldErrors,
    setFieldError,
    clearFieldError,
    clearAllFieldErrors,
    handleFormError
  };
};
