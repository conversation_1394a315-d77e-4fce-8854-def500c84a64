'use client';

import { useState, useCallback, useRef, useEffect } from 'react';

export interface LoadingState {
  isLoading: boolean;
  loadingMessage?: string;
  progress?: number;
  stage?: string;
}

export interface LoadingOptions {
  initialMessage?: string;
  showProgress?: boolean;
  minLoadingTime?: number; // Minimum time to show loading (prevents flashing)
  timeout?: number; // Maximum loading time before showing error
}

export const useLoadingState = (options: LoadingOptions = {}) => {
  const {
    initialMessage = 'Loading...',
    showProgress = false,
    minLoadingTime = 500,
    timeout = 30000
  } = options;

  const [loadingState, setLoadingState] = useState<LoadingState>({
    isLoading: false,
    loadingMessage: initialMessage,
    progress: showProgress ? 0 : undefined,
    stage: undefined
  });

  const startTimeRef = useRef<number | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const minTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const startLoading = useCallback((message?: string, stage?: string) => {
    startTimeRef.current = Date.now();
    
    setLoadingState({
      isLoading: true,
      loadingMessage: message || initialMessage,
      progress: showProgress ? 0 : undefined,
      stage
    });

    // Set timeout for maximum loading time
    if (timeout > 0) {
      timeoutRef.current = setTimeout(() => {
        console.warn('Loading timeout reached');
        setLoadingState(prev => ({
          ...prev,
          isLoading: false
        }));
      }, timeout);
    }
  }, [initialMessage, showProgress, timeout]);

  const updateProgress = useCallback((progress: number, message?: string, stage?: string) => {
    setLoadingState(prev => ({
      ...prev,
      progress: Math.min(100, Math.max(0, progress)),
      loadingMessage: message || prev.loadingMessage,
      stage: stage || prev.stage
    }));
  }, []);

  const updateMessage = useCallback((message: string, stage?: string) => {
    setLoadingState(prev => ({
      ...prev,
      loadingMessage: message,
      stage: stage || prev.stage
    }));
  }, []);

  const stopLoading = useCallback(() => {
    const stopLoadingImmediate = () => {
      setLoadingState(prev => ({
        ...prev,
        isLoading: false
      }));

      // Clear timeouts
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
      if (minTimeoutRef.current) {
        clearTimeout(minTimeoutRef.current);
        minTimeoutRef.current = null;
      }
    };

    // Ensure minimum loading time to prevent flashing
    if (startTimeRef.current && minLoadingTime > 0) {
      const elapsedTime = Date.now() - startTimeRef.current;
      const remainingTime = minLoadingTime - elapsedTime;

      if (remainingTime > 0) {
        minTimeoutRef.current = setTimeout(stopLoadingImmediate, remainingTime);
      } else {
        stopLoadingImmediate();
      }
    } else {
      stopLoadingImmediate();
    }

    startTimeRef.current = null;
  }, [minLoadingTime]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      if (minTimeoutRef.current) {
        clearTimeout(minTimeoutRef.current);
      }
    };
  }, []);

  return {
    ...loadingState,
    startLoading,
    stopLoading,
    updateProgress,
    updateMessage
  };
};

// Hook for managing multiple loading states
export const useMultipleLoadingStates = () => {
  const [loadingStates, setLoadingStates] = useState<Record<string, LoadingState>>({});

  const startLoading = useCallback((key: string, message?: string, stage?: string) => {
    setLoadingStates(prev => ({
      ...prev,
      [key]: {
        isLoading: true,
        loadingMessage: message || 'Loading...',
        stage
      }
    }));
  }, []);

  const stopLoading = useCallback((key: string) => {
    setLoadingStates(prev => {
      const newStates = { ...prev };
      if (newStates[key]) {
        newStates[key] = {
          ...newStates[key],
          isLoading: false
        };
      }
      return newStates;
    });
  }, []);

  const updateProgress = useCallback((key: string, progress: number, message?: string) => {
    setLoadingStates(prev => ({
      ...prev,
      [key]: {
        ...prev[key],
        progress,
        loadingMessage: message || prev[key]?.loadingMessage || 'Loading...'
      }
    }));
  }, []);

  const isAnyLoading = Object.values(loadingStates).some(state => state.isLoading);
  const getLoadingState = (key: string) => loadingStates[key] || { isLoading: false };

  return {
    loadingStates,
    startLoading,
    stopLoading,
    updateProgress,
    isAnyLoading,
    getLoadingState
  };
};

// Hook for async operations with automatic loading state management
export const useAsyncWithLoading = <T>(
  asyncFunction: (...args: any[]) => Promise<T>,
  options: LoadingOptions = {}
) => {
  const loadingState = useLoadingState(options);
  const [data, setData] = useState<T | null>(null);
  const [error, setError] = useState<Error | null>(null);

  const execute = useCallback(async (...args: any[]) => {
    try {
      loadingState.startLoading();
      setError(null);
      const result = await asyncFunction(...args);
      setData(result);
      return result;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('An error occurred');
      setError(error);
      throw error;
    } finally {
      loadingState.stopLoading();
    }
  }, [asyncFunction, loadingState]);

  return {
    ...loadingState,
    data,
    error,
    execute
  };
};

// Hook for sequential loading stages
export const useStageLoading = (stages: string[]) => {
  const [currentStageIndex, setCurrentStageIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  const startStageLoading = useCallback(() => {
    setIsLoading(true);
    setCurrentStageIndex(0);
  }, []);

  const nextStage = useCallback(() => {
    setCurrentStageIndex(prev => Math.min(prev + 1, stages.length - 1));
  }, [stages.length]);

  const stopStageLoading = useCallback(() => {
    setIsLoading(false);
    setCurrentStageIndex(0);
  }, []);

  const currentStage = stages[currentStageIndex];
  const progress = stages.length > 0 ? ((currentStageIndex + 1) / stages.length) * 100 : 0;

  return {
    isLoading,
    currentStage,
    currentStageIndex,
    progress,
    startStageLoading,
    nextStage,
    stopStageLoading
  };
};

// Utility function to create loading delays for better UX
export const createLoadingDelay = (minTime: number = 500) => {
  return new Promise(resolve => setTimeout(resolve, minTime));
};

// Hook for debounced loading (useful for search, etc.)
export const useDebouncedLoading = (delay: number = 300) => {
  const [isLoading, setIsLoading] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const startDebouncedLoading = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    setIsLoading(true);
    
    timeoutRef.current = setTimeout(() => {
      setIsLoading(false);
    }, delay);
  }, [delay]);

  const stopDebouncedLoading = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    setIsLoading(false);
  }, []);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return {
    isLoading,
    startDebouncedLoading,
    stopDebouncedLoading
  };
};
