-- Beta Program Schema Updates
-- This file contains the database changes needed for the beta program

-- Add beta program fields to user_profiles table
ALTER TABLE user_profiles 
ADD COLUMN IF NOT EXISTS beta_user BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS beta_joined_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS beta_feedback_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS finance_type VARCHAR(20) DEFAULT 'personal',
ADD COLUMN IF NOT EXISTS onboarding_completed BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS enabled_features J<PERSON>NB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS dashboard_preference VARCHAR(20) DEFAULT 'auto';

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_profiles_beta_user ON user_profiles(beta_user);
CREATE INDEX IF NOT EXISTS idx_user_profiles_finance_type ON user_profiles(finance_type);
CREATE INDEX IF NOT EXISTS idx_user_profiles_onboarding ON user_profiles(onboarding_completed);

-- Create enum for finance types (if it doesn't exist)
DO $$ BEGIN
    CREATE TYPE finance_type_enum AS ENUM ('personal', 'family', 'combined');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Update finance_type column to use enum (only if not already using enum)
DO $$ BEGIN
    ALTER TABLE user_profiles 
    ALTER COLUMN finance_type TYPE finance_type_enum 
    USING finance_type::finance_type_enum;
EXCEPTION
    WHEN OTHERS THEN null;
END $$;

-- Create beta feedback table
CREATE TABLE IF NOT EXISTS beta_feedback (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    feedback_type TEXT NOT NULL CHECK (feedback_type IN ('bug', 'feature_request', 'general', 'rating')),
    content TEXT,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for beta_feedback
CREATE INDEX IF NOT EXISTS idx_beta_feedback_user_id ON beta_feedback(user_id);
CREATE INDEX IF NOT EXISTS idx_beta_feedback_type ON beta_feedback(feedback_type);
CREATE INDEX IF NOT EXISTS idx_beta_feedback_created_at ON beta_feedback(created_at);

-- Create RLS policies for beta_feedback
ALTER TABLE beta_feedback ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only view their own feedback
CREATE POLICY IF NOT EXISTS "Users can view their own beta feedback" ON beta_feedback
    FOR SELECT USING (auth.uid() = user_id);

-- Policy: Users can insert their own feedback
CREATE POLICY IF NOT EXISTS "Users can insert their own beta feedback" ON beta_feedback
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Policy: Users can update their own feedback
CREATE POLICY IF NOT EXISTS "Users can update their own beta feedback" ON beta_feedback
    FOR UPDATE USING (auth.uid() = user_id);

-- Policy: Users can delete their own feedback
CREATE POLICY IF NOT EXISTS "Users can delete their own beta feedback" ON beta_feedback
    FOR DELETE USING (auth.uid() = user_id);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for beta_feedback updated_at
DROP TRIGGER IF EXISTS update_beta_feedback_updated_at ON beta_feedback;
CREATE TRIGGER update_beta_feedback_updated_at
    BEFORE UPDATE ON beta_feedback
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create function to get beta user stats
CREATE OR REPLACE FUNCTION get_beta_user_stats(p_user_id UUID)
RETURNS JSON AS $$
DECLARE
    user_stats JSON;
BEGIN
    SELECT json_build_object(
        'feedback_count', COALESCE(COUNT(bf.id), 0),
        'last_feedback', MAX(bf.created_at),
        'average_rating', ROUND(AVG(bf.rating), 2),
        'feedback_types', json_agg(DISTINCT bf.feedback_type) FILTER (WHERE bf.feedback_type IS NOT NULL),
        'beta_joined_at', up.beta_joined_at,
        'finance_type', up.finance_type,
        'onboarding_completed', up.onboarding_completed,
        'enabled_features', up.enabled_features
    ) INTO user_stats
    FROM user_profiles up
    LEFT JOIN beta_feedback bf ON up.id = bf.user_id
    WHERE up.id = p_user_id AND up.beta_user = true
    GROUP BY up.id, up.beta_joined_at, up.finance_type, up.onboarding_completed, up.enabled_features;
    
    RETURN user_stats;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get all beta feedback (admin only)
CREATE OR REPLACE FUNCTION get_all_beta_feedback()
RETURNS TABLE (
    id UUID,
    user_email TEXT,
    feedback_type TEXT,
    content TEXT,
    rating INTEGER,
    created_at TIMESTAMPTZ,
    user_finance_type TEXT
) AS $$
BEGIN
    -- This would typically check for admin role
    -- For now, we'll leave it as a placeholder for future admin functionality
    RETURN QUERY
    SELECT 
        bf.id,
        up.email,
        bf.feedback_type,
        bf.content,
        bf.rating,
        bf.created_at,
        up.finance_type::TEXT
    FROM beta_feedback bf
    JOIN user_profiles up ON bf.user_id = up.id
    ORDER BY bf.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the user_profiles table to set default values for existing users
-- This ensures backward compatibility
UPDATE user_profiles 
SET 
    beta_user = false,
    finance_type = 'personal',
    onboarding_completed = true,
    enabled_features = '{}',
    dashboard_preference = 'auto'
WHERE 
    beta_user IS NULL 
    OR finance_type IS NULL 
    OR onboarding_completed IS NULL 
    OR enabled_features IS NULL 
    OR dashboard_preference IS NULL;

-- Create a view for beta program analytics
CREATE OR REPLACE VIEW beta_program_analytics AS
SELECT 
    COUNT(*) as total_beta_users,
    COUNT(*) FILTER (WHERE onboarding_completed = true) as completed_onboarding,
    COUNT(*) FILTER (WHERE finance_type = 'personal') as personal_users,
    COUNT(*) FILTER (WHERE finance_type = 'family') as family_users,
    COUNT(*) FILTER (WHERE finance_type = 'combined') as combined_users,
    AVG(beta_feedback_count) as avg_feedback_per_user,
    COUNT(DISTINCT bf.user_id) as users_with_feedback,
    AVG(bf.rating) FILTER (WHERE bf.feedback_type = 'rating') as avg_rating
FROM user_profiles up
LEFT JOIN beta_feedback bf ON up.id = bf.user_id
WHERE up.beta_user = true;

-- Comment: This migration adds comprehensive beta program support including:
-- 1. User profile enhancements for beta program
-- 2. Feedback collection system with proper RLS
-- 3. Analytics functions for tracking beta program success
-- 4. Backward compatibility for existing users