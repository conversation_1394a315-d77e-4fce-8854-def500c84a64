import { createClient } from '@/shared/services/supabase/server';
import { NextRequest, NextResponse } from 'next/server';

export async function betaAuthMiddleware(request: NextRequest) {
  const response = NextResponse.next();

  try {
    const supabase = await createClient();
    
    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    // If no user, redirect to beta login
    if (userError || !user) {
      return NextResponse.redirect(new URL('/beta/auth/login', request.url));
    }

    // For demo purposes, we'll simulate beta access check
    // In a real implementation, this would use the betaService
    // but middleware runs on the server and we need client-side localStorage access
    
    // Basic check - if user exists, allow access but handle redirect in client components
    return response;
  } catch (error) {
    console.error('Beta auth middleware error:', error);
    return NextResponse.redirect(new URL('/beta/auth/login', request.url));
  }
}

export function shouldApplyBetaAuth(pathname: string): boolean {
  // Apply beta auth to beta dashboard and onboarding routes
  return pathname.startsWith('/beta/dashboard') || pathname === '/beta/onboarding';
}