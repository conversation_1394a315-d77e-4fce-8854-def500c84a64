'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import { Progress } from '@/shared/components/ui/progress';
import {
  DollarSign,
  TrendingUp,
  TrendingDown,
  CreditCard,
  Target,
  PiggyBank,
  AlertTriangle,
  CheckCircle,
  Plus,
  Eye,
  EyeOff,
  RefreshCw
} from 'lucide-react';
import { PersonalFinanceData, PersonalFinanceInsight } from '../types/personal';
import { personalFinanceService } from '../services/personalFinanceService';
import { useBetaFinanceType } from '@/beta/contexts/BetaFinanceTypeContext';
import { useToast } from '@/shared/hooks/use-toast';
import { useErrorHandling } from '@/beta/hooks/useErrorHandling';
import { useLoadingState } from '@/beta/hooks/useLoadingState';
import { ErrorBoundary } from '@/beta/components/ErrorBoundary';
import { DashboardOverviewSkeleton, CustomLoadingState } from '@/beta/components/LoadingStates';

interface PersonalOverviewProps {
  onAddTransaction?: () => void;
  onAddAccount?: () => void;
  onViewBudgets?: () => void;
  onViewGoals?: () => void;
}

function PersonalOverviewComponent({
  onAddTransaction,
  onAddAccount,
  onViewBudgets,
  onViewGoals
}: PersonalOverviewProps) {
  const [data, setData] = useState<PersonalFinanceData | null>(null);
  const [insights, setInsights] = useState<PersonalFinanceInsight[]>([]);
  const [showBalances, setShowBalances] = useState(true);
  const { user } = useBetaFinanceType();
  const { toast } = useToast();

  // Enhanced error handling and loading states
  const { errorState, handleError, clearError, retry, canRetry } = useErrorHandling({
    showToast: true,
    toastTitle: 'Failed to load dashboard',
    toastDescription: 'Unable to load your personal finance data. Please try again.',
    maxRetries: 3,
    onRetry: () => loadPersonalFinanceData()
  });

  const {
    isLoading,
    startLoading,
    stopLoading,
    updateMessage
  } = useLoadingState({
    initialMessage: 'Loading your financial data...',
    minLoadingTime: 500
  });

  useEffect(() => {
    if (user) {
      loadPersonalFinanceData();
    }
  }, [user]);

  const loadPersonalFinanceData = async () => {
    if (!user) return;

    try {
      startLoading('Loading your financial data...');
      clearError();

      updateMessage('Fetching accounts and transactions...');
      const [financeData, personalInsights] = await Promise.all([
        personalFinanceService.getPersonalFinanceData(user.id),
        personalFinanceService.getPersonalInsights(user.id)
      ]);

      updateMessage('Processing insights...');
      setData(financeData);
      setInsights(personalInsights);

      updateMessage('Complete!');
    } catch (error) {
      console.error('Error loading personal finance data:', error);
      handleError(error as Error);
    } finally {
      stopLoading();
    }
  };

  const formatCurrency = (amount: number) => {
    if (!showBalances) return '••••••';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: user?.currency || 'USD'
    }).format(amount);
  };

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'budget_warning':
        return <AlertTriangle className="h-4 w-4 text-orange-500" />;
      case 'goal_milestone':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'spending_alert':
        return <TrendingUp className="h-4 w-4 text-red-500" />;
      default:
        return <TrendingUp className="h-4 w-4 text-blue-500" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-700 border-red-200';
      case 'medium': return 'bg-orange-100 text-orange-700 border-orange-200';
      case 'low': return 'bg-blue-100 text-blue-700 border-blue-200';
      default: return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  // Loading state
  if (isLoading) {
    return <DashboardOverviewSkeleton />;
  }

  // Error state
  if (errorState.hasError) {
    return (
      <div className="min-h-[400px] flex flex-col items-center justify-center space-y-4">
        <CustomLoadingState
          icon={AlertTriangle}
          message="Failed to load dashboard"
          description={errorState.errorMessage || 'Unable to load your financial data'}
        />
        <div className="flex gap-2">
          {canRetry && (
            <Button onClick={retry} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          )}
          <Button onClick={clearError}>
            Dismiss
          </Button>
        </div>
      </div>
    );
  }

  // No data state
  if (!data) {
    return (
      <div className="min-h-[400px] flex flex-col items-center justify-center space-y-4">
        <CustomLoadingState
          icon={DollarSign}
          message="No financial data found"
          description="Start by adding your first account or transaction"
        />
        <div className="flex gap-2">
          {onAddAccount && (
            <Button onClick={onAddAccount}>
              <Plus className="h-4 w-4 mr-2" />
              Add Account
            </Button>
          )}
          {onAddTransaction && (
            <Button onClick={onAddTransaction} variant="outline">
              <Plus className="h-4 w-4 mr-2" />
              Add Transaction
            </Button>
          )}
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">No financial data available. Add an account to get started!</p>
        <Button onClick={onAddAccount} className="mt-4">
          <Plus className="w-4 h-4 mr-2" />
          Add Account
        </Button>
      </div>
    );
  }

  const { analytics } = data;

  return (
    <div className="space-y-6">
      {/* Header with Balance Toggle */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Personal Finance Overview</h2>
          <p className="text-muted-foreground">
            Your complete financial picture at a glance
          </p>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowBalances(!showBalances)}
          className="flex items-center gap-2"
        >
          {showBalances ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
          {showBalances ? 'Hide' : 'Show'} Amounts
        </Button>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Balance</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(data.accounts.reduce((sum, account) => sum + account.balance, 0))}
            </div>
            <p className="text-xs text-muted-foreground">
              Across {data.accounts.length} accounts
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monthly Income</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(analytics.overview.total_income)}
            </div>
            <p className="text-xs text-muted-foreground">
              This month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monthly Expenses</CardTitle>
            <TrendingDown className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(analytics.overview.total_expenses)}
            </div>
            <p className="text-xs text-muted-foreground">
              This month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Savings Rate</CardTitle>
            <PiggyBank className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {analytics.overview.savings_rate.toFixed(1)}%
            </div>
            <p className="text-xs text-muted-foreground">
              Net: {formatCurrency(analytics.overview.net_income)}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Insights Section */}
      {insights.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Financial Insights
            </CardTitle>
            <CardDescription>
              Personalized recommendations based on your spending patterns
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {insights.slice(0, 3).map((insight) => (
                <div key={insight.id} className="flex items-start gap-3 p-3 rounded-lg border">
                  {getInsightIcon(insight.type)}
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-medium">{insight.title}</h4>
                      <Badge 
                        variant="outline" 
                        className={getPriorityColor(insight.priority)}
                      >
                        {insight.priority}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">{insight.description}</p>
                  </div>
                </div>
              ))}
              {insights.length > 3 && (
                <p className="text-sm text-muted-foreground text-center">
                  +{insights.length - 3} more insights available
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Active Budgets</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold mb-2">{analytics.overview.active_budgets}</div>
            <div className="space-y-2">
              {analytics.budget_performance.slice(0, 2).map((budget, index) => (
                <div key={index} className="space-y-1">
                  <div className="flex justify-between text-sm">
                    <span>{budget.budget_name}</span>
                    <span className={budget.status === 'exceeded' ? 'text-red-600' : 'text-green-600'}>
                      {((budget.spent / budget.budgeted) * 100).toFixed(0)}%
                    </span>
                  </div>
                  <Progress 
                    value={Math.min((budget.spent / budget.budgeted) * 100, 100)} 
                    className="h-2"
                  />
                </div>
              ))}
            </div>
            {analytics.overview.active_budgets > 0 && (
              <Button variant="outline" size="sm" onClick={onViewBudgets} className="w-full mt-3">
                View All Budgets
              </Button>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Financial Goals</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold mb-2">{analytics.overview.active_goals}</div>
            <div className="space-y-2">
              {analytics.goal_progress.slice(0, 2).map((goal, index) => (
                <div key={index} className="space-y-1">
                  <div className="flex justify-between text-sm">
                    <span>{goal.goal_name}</span>
                    <span className="text-green-600">
                      {goal.progress_percentage.toFixed(0)}%
                    </span>
                  </div>
                  <Progress value={goal.progress_percentage} className="h-2" />
                </div>
              ))}
            </div>
            {analytics.overview.active_goals > 0 && (
              <Button variant="outline" size="sm" onClick={onViewGoals} className="w-full mt-3">
                View All Goals
              </Button>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Accounts</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold mb-2">{data.accounts.length}</div>
            <div className="space-y-2">
              {data.accounts.slice(0, 3).map((account) => (
                <div key={account.id} className="flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    <CreditCard className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{account.name}</span>
                  </div>
                  <span className="text-sm font-medium">
                    {formatCurrency(account.balance)}
                  </span>
                </div>
              ))}
            </div>
            <Button variant="outline" size="sm" onClick={onAddAccount} className="w-full mt-3">
              <Plus className="w-4 h-4 mr-2" />
              Add Account
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Common tasks to manage your personal finances
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button variant="outline" onClick={onAddTransaction} className="h-20 flex-col">
              <Plus className="h-6 w-6 mb-2" />
              <span className="text-sm">Add Transaction</span>
            </Button>
            <Button variant="outline" onClick={onAddAccount} className="h-20 flex-col">
              <CreditCard className="h-6 w-6 mb-2" />
              <span className="text-sm">Add Account</span>
            </Button>
            <Button variant="outline" onClick={onViewBudgets} className="h-20 flex-col">
              <Target className="h-6 w-6 mb-2" />
              <span className="text-sm">View Budgets</span>
            </Button>
            <Button variant="outline" onClick={onViewGoals} className="h-20 flex-col">
              <PiggyBank className="h-6 w-6 mb-2" />
              <span className="text-sm">View Goals</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Export wrapped with ErrorBoundary
export function PersonalOverview(props: PersonalOverviewProps) {
  return (
    <ErrorBoundary>
      <PersonalOverviewComponent {...props} />
    </ErrorBoundary>
  );
}