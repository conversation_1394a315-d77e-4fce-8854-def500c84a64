export interface PersonalFinanceData {
  accounts: PersonalAccount[];
  transactions: PersonalTransaction[];
  budgets: PersonalBudget[];
  goals: PersonalGoal[];
  analytics: PersonalAnalytics;
}

export interface PersonalAccount {
  id: string;
  name: string;
  type: string; // Accept any account type from database
  balance: number;
  currency: string;
  institution?: string;
  last_synced?: string;
  is_active: boolean | null;
  created_at: string;
  updated_at: string;
  icon?: string | null;
  color?: string | null;
  user_id: string;
  family_group_id?: string | null;
  created_by_user_id?: string | null;
}

export interface PersonalTransaction {
  id: string;
  account_id: string;
  category_id: string | null;
  amount: number;
  description: string;
  date: string;
  type: "income" | "expense";
  category?: PersonalCategory;
  account?: PersonalAccount;
  created_at: string;
  updated_at: string;
  user_id: string;
  family_group_id?: string | null;
  created_by_user_id?: string | null;
}

export interface PersonalCategory {
  id: string;
  name: string;
  icon: string | null;
  color: string | null;
  type: string;
  parent_id?: string | null;
  is_system?: boolean;
  created_at: string;
  updated_at: string;
  user_id: string;
  family_group_id?: string | null;
  created_by_user_id?: string | null;
}

export interface PersonalBudget {
  id: string;
  name: string;
  category_id: string | null;
  amount: number;
  period: "weekly" | "monthly" | "yearly";
  period_start?: string; // Optional since it may be calculated
  period_end?: string; // Optional since it may be calculated
  spent: number;
  remaining: number;
  percentage_used: number;
  status: "on_track" | "warning" | "exceeded";
  category?: PersonalCategory | null;
  created_at: string;
  updated_at: string;
  user_id: string;
  family_group_id?: string | null;
  created_by_user_id?: string | null;
  color?: string | null;
  currency?: string | null;
}

export interface PersonalGoal {
  id: string;
  name: string;
  description: string;
  target_amount: number;
  current_amount: number;
  target_date: string;
  category?: "saving" | "debt_payoff" | "investment" | "emergency_fund"; // Optional since database may not have this
  status: "active" | "completed" | "paused";
  progress_percentage: number;
  monthly_target?: number;
  created_at: string;
  updated_at: string;
  user_id: string;
  family_group_id?: string | null;
  created_by_user_id?: string | null;
  color?: string | null;
  currency?: string | null;
}

export interface PersonalAnalytics {
  overview: {
    total_income: number;
    total_expenses: number;
    net_income: number;
    savings_rate: number;
    account_count: number;
    active_budgets: number;
    active_goals: number;
  };
  spending_by_category: {
    category_name: string;
    amount: number;
    percentage: number;
    color: string;
  }[];
  income_vs_expenses: {
    month: string;
    income: number;
    expenses: number;
    net: number;
  }[];
  budget_performance: {
    budget_name: string;
    budgeted: number;
    spent: number;
    variance: number;
    status: "on_track" | "warning" | "exceeded";
  }[];
  goal_progress: {
    goal_name: string;
    target: number;
    current: number;
    progress_percentage: number;
    projected_completion?: string;
  }[];
  monthly_trends: {
    month: string;
    total_income: number;
    total_expenses: number;
    savings: number;
    savings_rate: number;
  }[];
}

export interface PersonalFinanceInsight {
  id: string;
  type:
    | "spending_alert"
    | "budget_warning"
    | "goal_milestone"
    | "trend_analysis";
  title: string;
  description: string;
  priority: "low" | "medium" | "high";
  action_required: boolean;
  related_data?: {
    budget_id?: string;
    goal_id?: string;
    category_id?: string;
    amount?: number;
  };
  created_at: string;
}

export interface PersonalDashboardPreferences {
  show_account_balances: boolean;
  show_recent_transactions: boolean;
  show_budget_alerts: boolean;
  show_goal_progress: boolean;
  show_spending_insights: boolean;
  default_time_period: "week" | "month" | "quarter" | "year";
  chart_preferences: {
    spending_chart: "pie" | "bar" | "donut";
    trend_chart: "line" | "area" | "bar";
  };
}

// API Response types
export interface PersonalFinanceResponse {
  data: PersonalFinanceData;
  insights: PersonalFinanceInsight[];
  last_updated: string;
}

export interface PersonalBudgetFormData {
  name: string;
  category_id: string;
  amount: number;
  period: "weekly" | "monthly" | "yearly";
  start_date: string;
}

export interface PersonalGoalFormData {
  name: string;
  description?: string;
  target_amount: number;
  target_date: string;
  category: "saving" | "debt_payoff" | "investment" | "emergency_fund";
}

export interface PersonalTransactionFormData {
  account_id: string;
  category_id: string;
  amount: number;
  description: string;
  date: string;
  type: "income" | "expense";
}

export interface PersonalAccountFormData {
  name: string;
  type: "checking" | "savings" | "credit" | "investment" | "cash";
  balance: number;
  institution?: string;
}
