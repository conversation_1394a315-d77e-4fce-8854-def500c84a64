import { createClient } from "@/shared/services/supabase/client";
import {
  PersonalFinanceData,
  PersonalAccount,
  PersonalTransaction,
  PersonalBudget,
  PersonalGoal,
  PersonalAnalytics,
  PersonalFinanceInsight,
  PersonalAccountFormData,
  PersonalTransactionFormData,
  PersonalBudgetFormData,
  PersonalGoalFormData,
} from "../types/personal";

export class PersonalFinanceService {
  private supabase = createClient();

  // Get complete personal finance data
  async getPersonalFinanceData(userId: string): Promise<PersonalFinanceData> {
    try {
      const [accounts, transactions, budgets, goals] = await Promise.all([
        this.getAccounts(userId),
        this.getRecentTransactions(userId, 50),
        this.getBudgets(userId),
        this.getGoals(userId),
      ]);

      const analytics = await this.getAnalytics(userId);

      return {
        accounts,
        transactions,
        budgets,
        goals,
        analytics,
      };
    } catch (error) {
      console.error("Error fetching personal finance data:", error);
      throw error;
    }
  }

  // Account Management
  async getAccounts(userId: string): Promise<PersonalAccount[]> {
    const { data, error } = await this.supabase
      .from("accounts")
      .select("*")
      .eq("user_id", userId)
      .is("family_group_id", null) // Personal accounts only (no family group)
      .order("created_at", { ascending: false });

    if (error) {
      console.error("Error fetching personal accounts:", error);
      throw error;
    }
    return data || [];
  }

  async createAccount(
    userId: string,
    accountData: PersonalAccountFormData
  ): Promise<PersonalAccount> {
    const { data, error } = await this.supabase
      .from("accounts")
      .insert({
        user_id: userId,
        family_group_id: null, // Personal account
        ...accountData,
      })
      .select()
      .single();

    if (error) {
      console.error("Error creating personal account:", error);
      throw error;
    }
    return data;
  }

  async updateAccount(
    accountId: string,
    updates: Partial<PersonalAccountFormData>
  ): Promise<PersonalAccount> {
    const { data, error } = await this.supabase
      .from("accounts")
      .update(updates)
      .eq("id", accountId)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  async deleteAccount(accountId: string): Promise<void> {
    const { error } = await this.supabase
      .from("accounts")
      .delete()
      .eq("id", accountId);

    if (error) throw error;
  }

  // Transaction Management
  async getRecentTransactions(
    userId: string,
    limit: number = 20
  ): Promise<PersonalTransaction[]> {
    const { data, error } = await this.supabase
      .from("transactions")
      .select(
        `
        *,
        category:categories(id, name, icon, color, type),
        account:accounts(id, name, type)
      `
      )
      .eq("user_id", userId)
      .is("family_group_id", null) // Personal transactions only
      .order("date", { ascending: false })
      .limit(limit);

    if (error) {
      console.error("Error fetching personal transactions:", error);
      throw error;
    }
    return data || [];
  }

  async getTransactionsByDateRange(
    userId: string,
    startDate: string,
    endDate: string
  ): Promise<PersonalTransaction[]> {
    const { data, error } = await this.supabase
      .from("transactions")
      .select(
        `
        *,
        category:categories(id, name, icon, color, type),
        account:accounts(id, name, type)
      `
      )
      .eq("user_id", userId)
      .is("family_group_id", null)
      .gte("date", startDate)
      .lte("date", endDate)
      .order("date", { ascending: false });

    if (error) {
      console.error("Error fetching transactions by date range:", error);
      throw error;
    }
    return data || [];
  }

  async createTransaction(
    userId: string,
    transactionData: PersonalTransactionFormData
  ): Promise<PersonalTransaction> {
    const { data, error } = await this.supabase
      .from("transactions")
      .insert({
        user_id: userId,
        family_group_id: null, // Personal transaction
        ...transactionData,
      })
      .select(
        `
        *,
        category:categories(id, name, icon, color, type),
        account:accounts(id, name, type)
      `
      )
      .single();

    if (error) {
      console.error("Error creating personal transaction:", error);
      throw error;
    }
    return data;
  }

  async updateTransaction(
    transactionId: string,
    updates: Partial<PersonalTransactionFormData>
  ): Promise<PersonalTransaction> {
    const { data, error } = await this.supabase
      .from("transactions")
      .update(updates)
      .eq("id", transactionId)
      .select(
        `
        *,
        category:categories(id, name, icon, color, type),
        account:accounts(id, name, type)
      `
      )
      .single();

    if (error) throw error;
    return data;
  }

  async deleteTransaction(transactionId: string): Promise<void> {
    const { error } = await this.supabase
      .from("transactions")
      .delete()
      .eq("id", transactionId);

    if (error) throw error;
  }

  // Budget Management
  async getBudgets(userId: string): Promise<PersonalBudget[]> {
    const { data, error } = await this.supabase
      .from("budgets")
      .select(
        `
        *,
        category:categories(id, name, icon, color, type)
      `
      )
      .eq("user_id", userId)
      .is("family_group_id", null) // Personal budgets only
      .order("created_at", { ascending: false });

    if (error) {
      console.error("Error fetching personal budgets:", error);
      throw error;
    }

    // Calculate budget metrics for each budget
    const budgetsWithMetrics = await Promise.all(
      (data || []).map(async (budget) => {
        const spent = await this.getBudgetSpent(budget.id);
        const remaining = budget.amount - spent;
        const percentage_used = (spent / budget.amount) * 100;

        let status: "on_track" | "warning" | "exceeded" = "on_track";
        if (percentage_used >= 100) status = "exceeded";
        else if (percentage_used >= 80) status = "warning";

        return {
          ...budget,
          spent,
          remaining,
          percentage_used,
          status,
        };
      })
    );

    return budgetsWithMetrics;
  }

  private async getBudgetSpent(budgetId: string): Promise<number> {
    // Get budget details
    const { data: budget, error: budgetError } = await this.supabase
      .from("budgets")
      .select("category_id, period_start, period_end")
      .eq("id", budgetId)
      .single();

    if (budgetError || !budget) return 0;

    // Get spending for this category in the budget period
    const { data, error } = await this.supabase
      .from("transactions")
      .select("amount")
      .eq("category_id", budget.category_id)
      .eq("type", "expense")
      .gte("date", budget.period_start)
      .lte("date", budget.period_end);

    if (error) return 0;

    return (data || []).reduce(
      (total, transaction) => total + Math.abs(transaction.amount),
      0
    );
  }

  async createBudget(
    userId: string,
    budgetData: PersonalBudgetFormData
  ): Promise<PersonalBudget> {
    // Calculate period dates based on period type
    const startDate = new Date(budgetData.start_date);
    const endDate = new Date(startDate);

    switch (budgetData.period) {
      case "weekly":
        endDate.setDate(startDate.getDate() + 7);
        break;
      case "monthly":
        endDate.setMonth(startDate.getMonth() + 1);
        break;
      case "yearly":
        endDate.setFullYear(startDate.getFullYear() + 1);
        break;
    }

    const { data, error } = await this.supabase
      .from("budgets")
      .insert({
        user_id: userId,
        family_group_id: null, // Personal budget
        name: budgetData.name,
        category_id: budgetData.category_id,
        amount: budgetData.amount,
        period: budgetData.period,
        period_start: startDate.toISOString().split("T")[0],
        period_end: endDate.toISOString().split("T")[0],
      })
      .select(
        `
        *,
        category:categories(id, name, icon, color, type)
      `
      )
      .single();

    if (error) throw error;

    // Add metrics
    const spent = await this.getBudgetSpent(data.id);
    const remaining = data.amount - spent;
    const percentage_used = (spent / data.amount) * 100;

    let status: "on_track" | "warning" | "exceeded" = "on_track";
    if (percentage_used >= 100) status = "exceeded";
    else if (percentage_used >= 80) status = "warning";

    return {
      ...data,
      spent,
      remaining,
      percentage_used,
      status,
    };
  }

  // Goal Management
  async getGoals(userId: string): Promise<PersonalGoal[]> {
    const { data, error } = await this.supabase
      .from("financial_goals")
      .select("*")
      .eq("user_id", userId)
      .is("family_group_id", null) // Personal goals only
      .order("created_at", { ascending: false });

    if (error) {
      console.error("Error fetching personal goals:", error);
      throw error;
    }

    // Calculate progress for each goal
    return (data || []).map((goal) => {
      const progress_percentage = Math.min(
        (goal.current_amount / goal.target_amount) * 100,
        100
      );

      return {
        ...goal,
        progress_percentage,
      };
    });
  }

  async createGoal(
    userId: string,
    goalData: PersonalGoalFormData
  ): Promise<PersonalGoal> {
    const { data, error } = await this.supabase
      .from("financial_goals")
      .insert({
        user_id: userId,
        family_group_id: null, // Personal goal
        ...goalData,
        current_amount: 0,
        status: "active",
      })
      .select()
      .single();

    if (error) throw error;

    return {
      ...data,
      progress_percentage: 0,
    };
  }

  async updateGoalProgress(
    goalId: string,
    currentAmount: number
  ): Promise<PersonalGoal> {
    const { data, error } = await this.supabase
      .from("financial_goals")
      .update({ current_amount: currentAmount })
      .eq("id", goalId)
      .select()
      .single();

    if (error) throw error;

    const progress_percentage = Math.min(
      (data.current_amount / data.target_amount) * 100,
      100
    );

    return {
      ...data,
      progress_percentage,
    };
  }

  // Analytics
  async getAnalytics(userId: string): Promise<PersonalAnalytics> {
    try {
      const [
        overview,
        spendingByCategory,
        incomeVsExpenses,
        budgetPerformance,
        goalProgress,
        monthlyTrends,
      ] = await Promise.all([
        this.getOverviewAnalytics(userId),
        this.getSpendingByCategory(userId),
        this.getIncomeVsExpenses(userId),
        this.getBudgetPerformance(userId),
        this.getGoalProgress(userId),
        this.getMonthlyTrends(userId),
      ]);

      return {
        overview,
        spending_by_category: spendingByCategory,
        income_vs_expenses: incomeVsExpenses,
        budget_performance: budgetPerformance,
        goal_progress: goalProgress,
        monthly_trends: monthlyTrends,
      };
    } catch (error) {
      console.error("Error fetching analytics:", error);
      throw error;
    }
  }

  private async getOverviewAnalytics(userId: string) {
    // Get current month's data
    const startOfMonth = new Date();
    startOfMonth.setDate(1);
    const endOfMonth = new Date();
    endOfMonth.setMonth(endOfMonth.getMonth() + 1);
    endOfMonth.setDate(0);

    const { data: transactions } = await this.supabase
      .from("transactions")
      .select("amount, type")
      .eq("user_id", userId)
      .is("family_group_id", null)
      .gte("date", startOfMonth.toISOString().split("T")[0])
      .lte("date", endOfMonth.toISOString().split("T")[0]);

    const totalIncome = (transactions || [])
      .filter((t) => t.type === "income")
      .reduce((sum, t) => sum + t.amount, 0);

    const totalExpenses = (transactions || [])
      .filter((t) => t.type === "expense")
      .reduce((sum, t) => sum + Math.abs(t.amount), 0);

    const netIncome = totalIncome - totalExpenses;
    const savingsRate = totalIncome > 0 ? (netIncome / totalIncome) * 100 : 0;

    // Get counts
    const { data: accounts } = await this.supabase
      .from("accounts")
      .select("id")
      .eq("user_id", userId)
      .is("family_group_id", null);

    const { data: budgets } = await this.supabase
      .from("budgets")
      .select("id")
      .eq("user_id", userId)
      .is("family_group_id", null);

    const { data: goals } = await this.supabase
      .from("financial_goals")
      .select("id")
      .eq("user_id", userId)
      .is("family_group_id", null)
      .eq("status", "active");

    return {
      total_income: totalIncome,
      total_expenses: totalExpenses,
      net_income: netIncome,
      savings_rate: savingsRate,
      account_count: (accounts || []).length,
      active_budgets: (budgets || []).length,
      active_goals: (goals || []).length,
    };
  }

  private async getSpendingByCategory(userId: string) {
    const startOfMonth = new Date();
    startOfMonth.setDate(1);
    const endOfMonth = new Date();
    endOfMonth.setMonth(endOfMonth.getMonth() + 1);
    endOfMonth.setDate(0);

    const { data } = await this.supabase
      .from("transactions")
      .select(
        `
        amount,
        category:categories(name, color)
      `
      )
      .eq("user_id", userId)
      .is("family_group_id", null)
      .eq("type", "expense")
      .gte("date", startOfMonth.toISOString().split("T")[0])
      .lte("date", endOfMonth.toISOString().split("T")[0]);

    if (!data) return [];

    const categoryTotals = data.reduce((acc, transaction) => {
      const categoryName = transaction.category?.name || "Uncategorized";
      const color = transaction.category?.color || "#gray";
      const amount = Math.abs(transaction.amount);

      if (!acc[categoryName]) {
        acc[categoryName] = { amount: 0, color };
      }
      acc[categoryName].amount += amount;

      return acc;
    }, {} as Record<string, { amount: number; color: string }>);

    const totalSpending = Object.values(categoryTotals).reduce(
      (sum, cat) => sum + cat.amount,
      0
    );

    return Object.entries(categoryTotals)
      .map(([category_name, { amount, color }]) => ({
        category_name,
        amount,
        percentage: totalSpending > 0 ? (amount / totalSpending) * 100 : 0,
        color,
      }))
      .sort((a, b) => b.amount - a.amount);
  }

  private async getIncomeVsExpenses(userId: string) {
    // Get last 6 months of data
    const months = [];
    for (let i = 5; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      months.push(date);
    }

    const result = await Promise.all(
      months.map(async (month) => {
        const startOfMonth = new Date(month.getFullYear(), month.getMonth(), 1);
        const endOfMonth = new Date(
          month.getFullYear(),
          month.getMonth() + 1,
          0
        );

        const { data } = await this.supabase
          .from("transactions")
          .select("amount, type")
          .eq("user_id", userId)
          .is("family_group_id", null)
          .gte("date", startOfMonth.toISOString().split("T")[0])
          .lte("date", endOfMonth.toISOString().split("T")[0]);

        const income = (data || [])
          .filter((t) => t.type === "income")
          .reduce((sum, t) => sum + t.amount, 0);

        const expenses = (data || [])
          .filter((t) => t.type === "expense")
          .reduce((sum, t) => sum + Math.abs(t.amount), 0);

        return {
          month: month.toLocaleDateString("en-US", {
            month: "short",
            year: "numeric",
          }),
          income,
          expenses,
          net: income - expenses,
        };
      })
    );

    return result;
  }

  private async getBudgetPerformance(userId: string) {
    const budgets = await this.getBudgets(userId);

    return budgets.map((budget) => ({
      budget_name: budget.name,
      budgeted: budget.amount,
      spent: budget.spent,
      variance: budget.amount - budget.spent,
      status: budget.status,
    }));
  }

  private async getGoalProgress(userId: string) {
    const goals = await this.getGoals(userId);

    return goals.map((goal) => {
      // Calculate projected completion based on current progress
      const monthsRemaining = goal.target_date
        ? Math.max(
            0,
            (new Date(goal.target_date).getTime() - new Date().getTime()) /
              (1000 * 60 * 60 * 24 * 30)
          )
        : null;

      const remainingAmount = goal.target_amount - goal.current_amount;
      const monthlyRequired = monthsRemaining
        ? remainingAmount / monthsRemaining
        : 0;

      return {
        goal_name: goal.name,
        target: goal.target_amount,
        current: goal.current_amount,
        progress_percentage: goal.progress_percentage,
        projected_completion:
          monthlyRequired > 0 && goal.progress_percentage > 0
            ? goal.target_date
            : undefined,
      };
    });
  }

  private async getMonthlyTrends(userId: string) {
    return this.getIncomeVsExpenses(userId).then((data) =>
      data.map((item) => ({
        month: item.month,
        total_income: item.income,
        total_expenses: item.expenses,
        savings: item.net,
        savings_rate: item.income > 0 ? (item.net / item.income) * 100 : 0,
      }))
    );
  }

  // Insights and AI-powered recommendations
  async getPersonalInsights(userId: string): Promise<PersonalFinanceInsight[]> {
    const insights: PersonalFinanceInsight[] = [];

    try {
      // Check for budget warnings
      const budgets = await this.getBudgets(userId);
      budgets.forEach((budget) => {
        if (budget.status === "exceeded") {
          insights.push({
            id: `budget-exceeded-${budget.id}`,
            type: "budget_warning",
            title: "Budget Exceeded",
            description: `You've exceeded your "${budget.name}" budget by $${(
              budget.spent - budget.amount
            ).toFixed(2)}`,
            priority: "high",
            action_required: true,
            related_data: {
              budget_id: budget.id,
              amount: budget.spent - budget.amount,
            },
            created_at: new Date().toISOString(),
          });
        } else if (budget.status === "warning") {
          insights.push({
            id: `budget-warning-${budget.id}`,
            type: "budget_warning",
            title: "Budget Warning",
            description: `You've used ${budget.percentage_used.toFixed(
              1
            )}% of your "${budget.name}" budget`,
            priority: "medium",
            action_required: false,
            related_data: { budget_id: budget.id },
            created_at: new Date().toISOString(),
          });
        }
      });

      // Check for goal milestones
      const goals = await this.getGoals(userId);
      goals.forEach((goal) => {
        if (goal.progress_percentage >= 25 && goal.progress_percentage < 30) {
          insights.push({
            id: `goal-milestone-${goal.id}`,
            type: "goal_milestone",
            title: "Goal Milestone Reached",
            description: `You're 25% of the way to your "${goal.name}" goal!`,
            priority: "low",
            action_required: false,
            related_data: { goal_id: goal.id },
            created_at: new Date().toISOString(),
          });
        }
      });

      return insights;
    } catch (error) {
      console.error("Error generating insights:", error);
      return [];
    }
  }
}

export const personalFinanceService = new PersonalFinanceService();
