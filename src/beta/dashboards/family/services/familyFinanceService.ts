import { createClient } from "@/shared/services/supabase/client";
import {
  FamilyFinanceData,
  FamilyGroup,
  FamilyGroupMember,
  FamilyAccount,
  FamilyTransaction,
  FamilyBudget,
  FamilyGoal,
  FamilyAnalytics,
  FamilyGroupFormData,
  FamilyAccountFormData,
  FamilyTransactionFormData,
  FamilyBudgetFormData,
  FamilyGoalFormData,
  FamilyGroupInvitation,
} from "../types/family";

export class FamilyFinanceService {
  private supabase = createClient();

  // Get complete family finance data
  async getFamilyFinanceData(userId: string): Promise<FamilyFinanceData> {
    try {
      const [groups, accounts, transactions, budgets, goals] = await Promise.all([
        this.getFamilyGroups(userId),
        this.getFamilyAccounts(userId),
        this.getRecentFamilyTransactions(userId, 50),
        this.getFamilyBudgets(userId),
        this.getFamilyGoals(userId),
      ]);

      const analytics = await this.getFamilyAnalytics(userId);

      return {
        groups,
        accounts,
        transactions,
        budgets,
        goals,
        analytics,
      };
    } catch (error) {
      console.error("Error fetching family finance data:", error);
      throw error;
    }
  }

  // Family Group Management
  async getFamilyGroups(userId: string): Promise<FamilyGroup[]> {
    try {
      const { data, error } = await this.supabase
        .from("family_group_members")
        .select(`
          group_id,
          role,
          joined_at,
          family_groups!inner(
            id,
            name,
            description,
            owner_user_id,
            created_at,
            updated_at
          )
        `)
        .eq("user_id", userId);

      if (error) {
        console.error("Error fetching family groups:", error);
        throw error;
      }

      // Calculate group metrics for each group
      const groupsWithMetrics = await Promise.all(
        (data || []).map(async (membership: any) => {
          const group = membership.family_groups;
          const [memberCount, balance, monthlyIncome, monthlyExpenses] = await Promise.all([
            this.getGroupMemberCount(group.id),
            this.getGroupBalance(group.id),
            this.getGroupMonthlyIncome(group.id),
            this.getGroupMonthlyExpenses(group.id),
          ]);

          return {
            id: group.id,
            name: group.name,
            description: group.description,
            owner_user_id: group.owner_user_id,
            member_count: memberCount,
            balance,
            monthly_income: monthlyIncome,
            monthly_expenses: monthlyExpenses,
            currency: "USD", // Default currency, could be stored in group settings
            is_admin: membership.role === "admin" || group.owner_user_id === userId,
            is_member: true,
            created_at: group.created_at,
            updated_at: group.updated_at,
          };
        })
      );

      return groupsWithMetrics;
    } catch (error) {
      console.error("Error getting family groups:", error);
      throw error;
    }
  }

  async createFamilyGroup(
    userId: string,
    groupData: FamilyGroupFormData
  ): Promise<FamilyGroup> {
    try {
      // Create the family group
      const { data: group, error: groupError } = await this.supabase
        .from("family_groups")
        .insert({
          name: groupData.name,
          description: groupData.description,
          owner_user_id: userId,
        })
        .select()
        .single();

      if (groupError) {
        console.error("Error creating family group:", groupError);
        throw groupError;
      }

      // Add the creator as an admin member
      const { error: memberError } = await this.supabase
        .from("family_group_members")
        .insert({
          group_id: group.id,
          user_id: userId,
          role: "admin",
        });

      if (memberError) {
        console.error("Error adding creator as member:", memberError);
        throw memberError;
      }

      return {
        id: group.id,
        name: group.name,
        description: group.description,
        owner_user_id: group.owner_user_id,
        member_count: 1,
        balance: 0,
        monthly_income: 0,
        monthly_expenses: 0,
        currency: groupData.currency || "USD",
        is_admin: true,
        is_member: true,
        created_at: group.created_at,
        updated_at: group.updated_at,
      };
    } catch (error) {
      console.error("Error creating family group:", error);
      throw error;
    }
  }

  async updateFamilyGroup(
    groupId: string,
    updates: Partial<FamilyGroupFormData>
  ): Promise<FamilyGroup> {
    try {
      const { data, error } = await this.supabase
        .from("family_groups")
        .update(updates)
        .eq("id", groupId)
        .select()
        .single();

      if (error) {
        console.error("Error updating family group:", error);
        throw error;
      }

      // Return updated group with metrics
      const [memberCount, balance, monthlyIncome, monthlyExpenses] = await Promise.all([
        this.getGroupMemberCount(groupId),
        this.getGroupBalance(groupId),
        this.getGroupMonthlyIncome(groupId),
        this.getGroupMonthlyExpenses(groupId),
      ]);

      return {
        id: data.id,
        name: data.name,
        description: data.description,
        owner_user_id: data.owner_user_id,
        member_count: memberCount,
        balance,
        monthly_income: monthlyIncome,
        monthly_expenses: monthlyExpenses,
        currency: "USD",
        is_admin: true, // Assuming user has permission to update
        is_member: true,
        created_at: data.created_at,
        updated_at: data.updated_at,
      };
    } catch (error) {
      console.error("Error updating family group:", error);
      throw error;
    }
  }

  async deleteFamilyGroup(groupId: string): Promise<void> {
    try {
      const { error } = await this.supabase
        .from("family_groups")
        .delete()
        .eq("id", groupId);

      if (error) {
        console.error("Error deleting family group:", error);
        throw error;
      }
    } catch (error) {
      console.error("Error deleting family group:", error);
      throw error;
    }
  }

  // Family Group Members Management
  async getGroupMembers(groupId: string): Promise<FamilyGroupMember[]> {
    try {
      const { data, error } = await this.supabase
        .from("family_group_members")
        .select(`
          id,
          group_id,
          user_id,
          role,
          joined_at,
          user_profiles!inner(
            full_name,
            username,
            avatar_url,
            email
          )
        `)
        .eq("group_id", groupId);

      if (error) {
        console.error("Error fetching group members:", error);
        throw error;
      }

      return (data || []).map((member: any) => ({
        id: member.id,
        group_id: member.group_id,
        user_id: member.user_id,
        role: member.role,
        permissions: this.getPermissionsForRole(member.role),
        joined_at: member.joined_at,
        user_profile: member.user_profiles,
      }));
    } catch (error) {
      console.error("Error getting group members:", error);
      throw error;
    }
  }

  async inviteToFamilyGroup(
    groupId: string,
    email: string,
    role: 'admin' | 'member' | 'viewer',
    invitedBy: string
  ): Promise<FamilyGroupInvitation> {
    try {
      // Create invitation
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + 7); // 7 days expiry

      const { data, error } = await this.supabase
        .from("family_group_invitations")
        .insert({
          group_id: groupId,
          email,
          role,
          invited_by: invitedBy,
          expires_at: expiresAt.toISOString(),
          status: "pending",
        })
        .select(`
          *,
          family_groups(name, description),
          invited_by_user:user_profiles!invited_by(full_name, username)
        `)
        .single();

      if (error) {
        console.error("Error creating invitation:", error);
        throw error;
      }

      return {
        id: data.id,
        group_id: data.group_id,
        email: data.email,
        role: data.role,
        status: data.status,
        invited_by: data.invited_by,
        created_at: data.created_at,
        expires_at: data.expires_at,
        group: data.family_groups,
        invited_by_user: data.invited_by_user,
      };
    } catch (error) {
      console.error("Error inviting to family group:", error);
      throw error;
    }
  }

  // Helper methods for group metrics
  private async getGroupMemberCount(groupId: string): Promise<number> {
    const { count, error } = await this.supabase
      .from("family_group_members")
      .select("*", { count: "exact", head: true })
      .eq("group_id", groupId);

    if (error) {
      console.error("Error getting member count:", error);
      return 0;
    }
    return count || 0;
  }

  private async getGroupBalance(groupId: string): Promise<number> {
    const { data, error } = await this.supabase
      .from("accounts")
      .select("balance")
      .eq("family_group_id", groupId);

    if (error) {
      console.error("Error getting group balance:", error);
      return 0;
    }

    return (data || []).reduce((total, account) => total + (account.balance || 0), 0);
  }

  private async getGroupMonthlyIncome(groupId: string): Promise<number> {
    const startOfMonth = new Date();
    startOfMonth.setDate(1);
    const endOfMonth = new Date();
    endOfMonth.setMonth(endOfMonth.getMonth() + 1);
    endOfMonth.setDate(0);

    const { data, error } = await this.supabase
      .from("transactions")
      .select("amount")
      .eq("family_group_id", groupId)
      .eq("type", "income")
      .gte("date", startOfMonth.toISOString().split("T")[0])
      .lte("date", endOfMonth.toISOString().split("T")[0]);

    if (error) {
      console.error("Error getting group monthly income:", error);
      return 0;
    }

    return (data || []).reduce((total, transaction) => total + (transaction.amount || 0), 0);
  }

  private async getGroupMonthlyExpenses(groupId: string): Promise<number> {
    const startOfMonth = new Date();
    startOfMonth.setDate(1);
    const endOfMonth = new Date();
    endOfMonth.setMonth(endOfMonth.getMonth() + 1);
    endOfMonth.setDate(0);

    const { data, error } = await this.supabase
      .from("transactions")
      .select("amount")
      .eq("family_group_id", groupId)
      .eq("type", "expense")
      .gte("date", startOfMonth.toISOString().split("T")[0])
      .lte("date", endOfMonth.toISOString().split("T")[0]);

    if (error) {
      console.error("Error getting group monthly expenses:", error);
      return 0;
    }

    return (data || []).reduce((total, transaction) => total + Math.abs(transaction.amount || 0), 0);
  }

  private getPermissionsForRole(role: string) {
    const basePermissions = {
      can_view_transactions: true,
      can_add_transactions: false,
      can_edit_transactions: false,
      can_delete_transactions: false,
      can_manage_budgets: false,
      can_manage_goals: false,
      can_manage_accounts: false,
      can_invite_members: false,
      can_remove_members: false,
      can_edit_group: false,
    };

    switch (role) {
      case "admin":
        return {
          ...basePermissions,
          can_add_transactions: true,
          can_edit_transactions: true,
          can_delete_transactions: true,
          can_manage_budgets: true,
          can_manage_goals: true,
          can_manage_accounts: true,
          can_invite_members: true,
          can_remove_members: true,
          can_edit_group: true,
        };
      case "member":
        return {
          ...basePermissions,
          can_add_transactions: true,
          can_edit_transactions: true,
          can_manage_budgets: true,
          can_manage_goals: true,
        };
      case "viewer":
      default:
        return basePermissions;
    }
  }
  }

  // Family Account Management
  async getFamilyAccounts(userId: string): Promise<FamilyAccount[]> {
    try {
      // Get all groups the user is a member of
      const { data: memberships, error: membershipError } = await this.supabase
        .from("family_group_members")
        .select("group_id")
        .eq("user_id", userId);

      if (membershipError) {
        console.error("Error fetching user memberships:", membershipError);
        throw membershipError;
      }

      if (!memberships || memberships.length === 0) {
        return [];
      }

      const groupIds = memberships.map(m => m.group_id);

      const { data, error } = await this.supabase
        .from("accounts")
        .select("*")
        .in("family_group_id", groupIds)
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Error fetching family accounts:", error);
        throw error;
      }

      return (data || []).map(account => ({
        id: account.id,
        family_group_id: account.family_group_id,
        name: account.name,
        type: account.type,
        balance: account.balance || 0,
        currency: account.currency || "USD",
        institution: account.institution,
        last_synced: account.last_synced,
        is_active: account.is_active !== false,
        created_at: account.created_at,
        updated_at: account.updated_at,
        created_by_user_id: account.created_by_user_id || account.user_id,
        permissions: {
          can_view: [], // TODO: Implement permission logic
          can_edit: [],
          can_transact: [],
        },
      }));
    } catch (error) {
      console.error("Error getting family accounts:", error);
      throw error;
    }
  }

  async createFamilyAccount(
    userId: string,
    groupId: string,
    accountData: FamilyAccountFormData
  ): Promise<FamilyAccount> {
    try {
      const { data, error } = await this.supabase
        .from("accounts")
        .insert({
          user_id: userId,
          family_group_id: groupId,
          created_by_user_id: userId,
          ...accountData,
        })
        .select()
        .single();

      if (error) {
        console.error("Error creating family account:", error);
        throw error;
      }

      return {
        id: data.id,
        family_group_id: data.family_group_id,
        name: data.name,
        type: data.type,
        balance: data.balance || 0,
        currency: data.currency || "USD",
        institution: data.institution,
        last_synced: data.last_synced,
        is_active: data.is_active !== false,
        created_at: data.created_at,
        updated_at: data.updated_at,
        created_by_user_id: data.created_by_user_id || data.user_id,
        permissions: {
          can_view: [],
          can_edit: [],
          can_transact: [],
        },
      };
    } catch (error) {
      console.error("Error creating family account:", error);
      throw error;
    }
  }

  async updateFamilyAccount(
    accountId: string,
    updates: Partial<FamilyAccountFormData>
  ): Promise<FamilyAccount> {
    try {
      const { data, error } = await this.supabase
        .from("accounts")
        .update(updates)
        .eq("id", accountId)
        .select()
        .single();

      if (error) {
        console.error("Error updating family account:", error);
        throw error;
      }

      return {
        id: data.id,
        family_group_id: data.family_group_id,
        name: data.name,
        type: data.type,
        balance: data.balance || 0,
        currency: data.currency || "USD",
        institution: data.institution,
        last_synced: data.last_synced,
        is_active: data.is_active !== false,
        created_at: data.created_at,
        updated_at: data.updated_at,
        created_by_user_id: data.created_by_user_id || data.user_id,
        permissions: {
          can_view: [],
          can_edit: [],
          can_transact: [],
        },
      };
    } catch (error) {
      console.error("Error updating family account:", error);
      throw error;
    }
  }

  async deleteFamilyAccount(accountId: string): Promise<void> {
    try {
      const { error } = await this.supabase
        .from("accounts")
        .delete()
        .eq("id", accountId);

      if (error) {
        console.error("Error deleting family account:", error);
        throw error;
      }
    } catch (error) {
      console.error("Error deleting family account:", error);
      throw error;
    }
  }
}

  // Family Transaction Management
  async getRecentFamilyTransactions(
    userId: string,
    limit: number = 20
  ): Promise<FamilyTransaction[]> {
    try {
      // Get all groups the user is a member of
      const { data: memberships, error: membershipError } = await this.supabase
        .from("family_group_members")
        .select("group_id")
        .eq("user_id", userId);

      if (membershipError) {
        console.error("Error fetching user memberships:", membershipError);
        throw membershipError;
      }

      if (!memberships || memberships.length === 0) {
        return [];
      }

      const groupIds = memberships.map(m => m.group_id);

      const { data, error } = await this.supabase
        .from("transactions")
        .select(`
          *,
          category:categories(id, name, icon, color, type),
          account:accounts(id, name, type),
          created_by_user:user_profiles!created_by(full_name, username)
        `)
        .in("family_group_id", groupIds)
        .order("date", { ascending: false })
        .limit(limit);

      if (error) {
        console.error("Error fetching family transactions:", error);
        throw error;
      }

      return (data || []).map(transaction => ({
        id: transaction.id,
        family_group_id: transaction.family_group_id,
        account_id: transaction.account_id,
        category_id: transaction.category_id,
        amount: transaction.amount,
        description: transaction.description,
        type: transaction.type,
        date: transaction.date,
        currency: transaction.currency || "USD",
        created_by: transaction.created_by,
        created_at: transaction.created_at,
        updated_at: transaction.updated_at,
        category: transaction.category,
        account: transaction.account,
        created_by_user: transaction.created_by_user,
      }));
    } catch (error) {
      console.error("Error getting family transactions:", error);
      throw error;
    }
  }

  async getFamilyTransactionsByDateRange(
    userId: string,
    groupId: string,
    startDate: string,
    endDate: string
  ): Promise<FamilyTransaction[]> {
    try {
      const { data, error } = await this.supabase
        .from("transactions")
        .select(`
          *,
          category:categories(id, name, icon, color, type),
          account:accounts(id, name, type),
          created_by_user:user_profiles!created_by(full_name, username)
        `)
        .eq("family_group_id", groupId)
        .gte("date", startDate)
        .lte("date", endDate)
        .order("date", { ascending: false });

      if (error) {
        console.error("Error fetching family transactions by date range:", error);
        throw error;
      }

      return (data || []).map(transaction => ({
        id: transaction.id,
        family_group_id: transaction.family_group_id,
        account_id: transaction.account_id,
        category_id: transaction.category_id,
        amount: transaction.amount,
        description: transaction.description,
        type: transaction.type,
        date: transaction.date,
        currency: transaction.currency || "USD",
        created_by: transaction.created_by,
        created_at: transaction.created_at,
        updated_at: transaction.updated_at,
        category: transaction.category,
        account: transaction.account,
        created_by_user: transaction.created_by_user,
      }));
    } catch (error) {
      console.error("Error getting family transactions by date range:", error);
      throw error;
    }
  }

  async createFamilyTransaction(
    userId: string,
    groupId: string,
    transactionData: FamilyTransactionFormData
  ): Promise<FamilyTransaction> {
    try {
      const { data, error } = await this.supabase
        .from("transactions")
        .insert({
          user_id: userId,
          family_group_id: groupId,
          created_by: userId,
          ...transactionData,
        })
        .select(`
          *,
          category:categories(id, name, icon, color, type),
          account:accounts(id, name, type),
          created_by_user:user_profiles!created_by(full_name, username)
        `)
        .single();

      if (error) {
        console.error("Error creating family transaction:", error);
        throw error;
      }

      return {
        id: data.id,
        family_group_id: data.family_group_id,
        account_id: data.account_id,
        category_id: data.category_id,
        amount: data.amount,
        description: data.description,
        type: data.type,
        date: data.date,
        currency: data.currency || "USD",
        created_by: data.created_by,
        created_at: data.created_at,
        updated_at: data.updated_at,
        category: data.category,
        account: data.account,
        created_by_user: data.created_by_user,
      };
    } catch (error) {
      console.error("Error creating family transaction:", error);
      throw error;
    }
  }

  async updateFamilyTransaction(
    transactionId: string,
    updates: Partial<FamilyTransactionFormData>
  ): Promise<FamilyTransaction> {
    try {
      const { data, error } = await this.supabase
        .from("transactions")
        .update(updates)
        .eq("id", transactionId)
        .select(`
          *,
          category:categories(id, name, icon, color, type),
          account:accounts(id, name, type),
          created_by_user:user_profiles!created_by(full_name, username)
        `)
        .single();

      if (error) {
        console.error("Error updating family transaction:", error);
        throw error;
      }

      return {
        id: data.id,
        family_group_id: data.family_group_id,
        account_id: data.account_id,
        category_id: data.category_id,
        amount: data.amount,
        description: data.description,
        type: data.type,
        date: data.date,
        currency: data.currency || "USD",
        created_by: data.created_by,
        created_at: data.created_at,
        updated_at: data.updated_at,
        category: data.category,
        account: data.account,
        created_by_user: data.created_by_user,
      };
    } catch (error) {
      console.error("Error updating family transaction:", error);
      throw error;
    }
  }

  async deleteFamilyTransaction(transactionId: string): Promise<void> {
    try {
      const { error } = await this.supabase
        .from("transactions")
        .delete()
        .eq("id", transactionId);

      if (error) {
        console.error("Error deleting family transaction:", error);
        throw error;
      }
    } catch (error) {
      console.error("Error deleting family transaction:", error);
      throw error;
    }
  }
}

  // Family Budget Management
  async getFamilyBudgets(userId: string): Promise<FamilyBudget[]> {
    try {
      // Get all groups the user is a member of
      const { data: memberships, error: membershipError } = await this.supabase
        .from("family_group_members")
        .select("group_id")
        .eq("user_id", userId);

      if (membershipError) {
        console.error("Error fetching user memberships:", membershipError);
        throw membershipError;
      }

      if (!memberships || memberships.length === 0) {
        return [];
      }

      const groupIds = memberships.map(m => m.group_id);

      const { data, error } = await this.supabase
        .from("budgets")
        .select(`
          *,
          category:categories(id, name, icon, color, type)
        `)
        .in("family_group_id", groupIds)
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Error fetching family budgets:", error);
        throw error;
      }

      // Calculate budget metrics for each budget
      const budgetsWithMetrics = await Promise.all(
        (data || []).map(async (budget) => {
          const spent = await this.getFamilyBudgetSpent(budget.id);
          const remaining = budget.amount - spent;
          const percentage_used = (spent / budget.amount) * 100;

          let status: "on_track" | "warning" | "exceeded" = "on_track";
          if (percentage_used >= 100) status = "exceeded";
          else if (percentage_used >= 80) status = "warning";

          return {
            id: budget.id,
            family_group_id: budget.family_group_id,
            name: budget.name,
            category_id: budget.category_id,
            amount: budget.amount,
            period: budget.period,
            period_start: budget.period_start,
            period_end: budget.period_end,
            spent,
            remaining,
            percentage_used,
            status,
            created_by: budget.created_by || budget.user_id,
            created_at: budget.created_at,
            updated_at: budget.updated_at,
            category: budget.category,
            contributors: [], // TODO: Implement contributor tracking
          };
        })
      );

      return budgetsWithMetrics;
    } catch (error) {
      console.error("Error getting family budgets:", error);
      throw error;
    }
  }

  private async getFamilyBudgetSpent(budgetId: string): Promise<number> {
    // Get budget details
    const { data: budget, error: budgetError } = await this.supabase
      .from("budgets")
      .select("category_id, period_start, period_end, family_group_id")
      .eq("id", budgetId)
      .single();

    if (budgetError || !budget) return 0;

    // Get spending for this category in the budget period
    const { data, error } = await this.supabase
      .from("transactions")
      .select("amount")
      .eq("family_group_id", budget.family_group_id)
      .eq("category_id", budget.category_id)
      .eq("type", "expense")
      .gte("date", budget.period_start)
      .lte("date", budget.period_end);

    if (error) return 0;

    return (data || []).reduce(
      (total, transaction) => total + Math.abs(transaction.amount),
      0
    );
  }

  async createFamilyBudget(
    userId: string,
    groupId: string,
    budgetData: FamilyBudgetFormData
  ): Promise<FamilyBudget> {
    try {
      // Calculate period dates based on period type
      const startDate = new Date(budgetData.start_date);
      const endDate = new Date(startDate);

      switch (budgetData.period) {
        case "weekly":
          endDate.setDate(startDate.getDate() + 7);
          break;
        case "monthly":
          endDate.setMonth(startDate.getMonth() + 1);
          break;
        case "yearly":
          endDate.setFullYear(startDate.getFullYear() + 1);
          break;
      }

      const { data, error } = await this.supabase
        .from("budgets")
        .insert({
          user_id: userId,
          family_group_id: groupId,
          created_by: userId,
          name: budgetData.name,
          category_id: budgetData.category_id,
          amount: budgetData.amount,
          period: budgetData.period,
          period_start: startDate.toISOString().split("T")[0],
          period_end: endDate.toISOString().split("T")[0],
        })
        .select(`
          *,
          category:categories(id, name, icon, color, type)
        `)
        .single();

      if (error) {
        console.error("Error creating family budget:", error);
        throw error;
      }

      return {
        id: data.id,
        family_group_id: data.family_group_id,
        name: data.name,
        category_id: data.category_id,
        amount: data.amount,
        period: data.period,
        period_start: data.period_start,
        period_end: data.period_end,
        spent: 0,
        remaining: data.amount,
        percentage_used: 0,
        status: "on_track",
        created_by: data.created_by || data.user_id,
        created_at: data.created_at,
        updated_at: data.updated_at,
        category: data.category,
        contributors: [],
      };
    } catch (error) {
      console.error("Error creating family budget:", error);
      throw error;
    }
  }

  async updateFamilyBudget(
    budgetId: string,
    updates: Partial<FamilyBudgetFormData>
  ): Promise<FamilyBudget> {
    try {
      const { data, error } = await this.supabase
        .from("budgets")
        .update(updates)
        .eq("id", budgetId)
        .select(`
          *,
          category:categories(id, name, icon, color, type)
        `)
        .single();

      if (error) {
        console.error("Error updating family budget:", error);
        throw error;
      }

      const spent = await this.getFamilyBudgetSpent(budgetId);
      const remaining = data.amount - spent;
      const percentage_used = (spent / data.amount) * 100;

      let status: "on_track" | "warning" | "exceeded" = "on_track";
      if (percentage_used >= 100) status = "exceeded";
      else if (percentage_used >= 80) status = "warning";

      return {
        id: data.id,
        family_group_id: data.family_group_id,
        name: data.name,
        category_id: data.category_id,
        amount: data.amount,
        period: data.period,
        period_start: data.period_start,
        period_end: data.period_end,
        spent,
        remaining,
        percentage_used,
        status,
        created_by: data.created_by || data.user_id,
        created_at: data.created_at,
        updated_at: data.updated_at,
        category: data.category,
        contributors: [],
      };
    } catch (error) {
      console.error("Error updating family budget:", error);
      throw error;
    }
  }

  async deleteFamilyBudget(budgetId: string): Promise<void> {
    try {
      const { error } = await this.supabase
        .from("budgets")
        .delete()
        .eq("id", budgetId);

      if (error) {
        console.error("Error deleting family budget:", error);
        throw error;
      }
    } catch (error) {
      console.error("Error deleting family budget:", error);
      throw error;
    }
  }
}

  // Family Goal Management
  async getFamilyGoals(userId: string): Promise<FamilyGoal[]> {
    try {
      // Get all groups the user is a member of
      const { data: memberships, error: membershipError } = await this.supabase
        .from("family_group_members")
        .select("group_id")
        .eq("user_id", userId);

      if (membershipError) {
        console.error("Error fetching user memberships:", membershipError);
        throw membershipError;
      }

      if (!memberships || memberships.length === 0) {
        return [];
      }

      const groupIds = memberships.map(m => m.group_id);

      const { data, error } = await this.supabase
        .from("financial_goals")
        .select("*")
        .in("family_group_id", groupIds)
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Error fetching family goals:", error);
        throw error;
      }

      // Calculate progress for each goal
      const goalsWithProgress = (data || []).map(goal => {
        const progress_percentage = goal.target_amount > 0
          ? (goal.current_amount / goal.target_amount) * 100
          : 0;

        let days_remaining: number | undefined;
        let monthly_target = 0;

        if (goal.target_date) {
          const targetDate = new Date(goal.target_date);
          const today = new Date();
          days_remaining = Math.max(0, Math.ceil((targetDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)));

          if (days_remaining > 0) {
            const months_remaining = days_remaining / 30;
            const remaining_amount = goal.target_amount - goal.current_amount;
            monthly_target = remaining_amount / months_remaining;
          }
        }

        return {
          id: goal.id,
          family_group_id: goal.family_group_id,
          name: goal.name,
          description: goal.description,
          target_amount: goal.target_amount,
          current_amount: goal.current_amount || 0,
          target_date: goal.target_date,
          status: goal.status,
          progress_percentage,
          monthly_target,
          days_remaining,
          currency: goal.currency || "USD",
          created_by: goal.created_by || goal.user_id,
          created_at: goal.created_at,
          updated_at: goal.updated_at,
          contributors: [], // TODO: Implement contributor tracking
        };
      });

      return goalsWithProgress;
    } catch (error) {
      console.error("Error getting family goals:", error);
      throw error;
    }
  }

  async createFamilyGoal(
    userId: string,
    groupId: string,
    goalData: FamilyGoalFormData
  ): Promise<FamilyGoal> {
    try {
      const { data, error } = await this.supabase
        .from("financial_goals")
        .insert({
          user_id: userId,
          family_group_id: groupId,
          created_by: userId,
          ...goalData,
          current_amount: 0,
          status: "active",
        })
        .select()
        .single();

      if (error) {
        console.error("Error creating family goal:", error);
        throw error;
      }

      let days_remaining: number | undefined;
      let monthly_target = 0;

      if (data.target_date) {
        const targetDate = new Date(data.target_date);
        const today = new Date();
        days_remaining = Math.max(0, Math.ceil((targetDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)));

        if (days_remaining > 0) {
          const months_remaining = days_remaining / 30;
          monthly_target = data.target_amount / months_remaining;
        }
      }

      return {
        id: data.id,
        family_group_id: data.family_group_id,
        name: data.name,
        description: data.description,
        target_amount: data.target_amount,
        current_amount: 0,
        target_date: data.target_date,
        status: "active",
        progress_percentage: 0,
        monthly_target,
        days_remaining,
        currency: data.currency || "USD",
        created_by: data.created_by || data.user_id,
        created_at: data.created_at,
        updated_at: data.updated_at,
        contributors: [],
      };
    } catch (error) {
      console.error("Error creating family goal:", error);
      throw error;
    }
  }

  async updateFamilyGoal(
    goalId: string,
    updates: Partial<FamilyGoalFormData>
  ): Promise<FamilyGoal> {
    try {
      const { data, error } = await this.supabase
        .from("financial_goals")
        .update(updates)
        .eq("id", goalId)
        .select()
        .single();

      if (error) {
        console.error("Error updating family goal:", error);
        throw error;
      }

      const progress_percentage = data.target_amount > 0
        ? (data.current_amount / data.target_amount) * 100
        : 0;

      let days_remaining: number | undefined;
      let monthly_target = 0;

      if (data.target_date) {
        const targetDate = new Date(data.target_date);
        const today = new Date();
        days_remaining = Math.max(0, Math.ceil((targetDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)));

        if (days_remaining > 0) {
          const months_remaining = days_remaining / 30;
          const remaining_amount = data.target_amount - data.current_amount;
          monthly_target = remaining_amount / months_remaining;
        }
      }

      return {
        id: data.id,
        family_group_id: data.family_group_id,
        name: data.name,
        description: data.description,
        target_amount: data.target_amount,
        current_amount: data.current_amount || 0,
        target_date: data.target_date,
        status: data.status,
        progress_percentage,
        monthly_target,
        days_remaining,
        currency: data.currency || "USD",
        created_by: data.created_by || data.user_id,
        created_at: data.created_at,
        updated_at: data.updated_at,
        contributors: [],
      };
    } catch (error) {
      console.error("Error updating family goal:", error);
      throw error;
    }
  }

  async deleteFamilyGoal(goalId: string): Promise<void> {
    try {
      const { error } = await this.supabase
        .from("financial_goals")
        .delete()
        .eq("id", goalId);

      if (error) {
        console.error("Error deleting family goal:", error);
        throw error;
      }
    } catch (error) {
      console.error("Error deleting family goal:", error);
      throw error;
    }
  }

  // Family Analytics (placeholder - basic implementation)
  async getFamilyAnalytics(userId: string): Promise<FamilyAnalytics> {
    try {
      // This is a basic implementation - can be expanded with more sophisticated analytics
      const [groups, accounts, budgets, goals] = await Promise.all([
        this.getFamilyGroups(userId),
        this.getFamilyAccounts(userId),
        this.getFamilyBudgets(userId),
        this.getFamilyGoals(userId),
      ]);

      const totalBalance = accounts.reduce((sum, account) => sum + account.balance, 0);
      const activeBudgets = budgets.filter(b => b.status !== "exceeded").length;
      const activeGoals = goals.filter(g => g.status === "active").length;

      // Calculate monthly income/expenses from recent transactions
      const startOfMonth = new Date();
      startOfMonth.setDate(1);
      const endOfMonth = new Date();
      endOfMonth.setMonth(endOfMonth.getMonth() + 1);
      endOfMonth.setDate(0);

      const transactions = await this.getRecentFamilyTransactions(userId, 1000);
      const monthlyTransactions = transactions.filter(t => {
        const transactionDate = new Date(t.date);
        return transactionDate >= startOfMonth && transactionDate <= endOfMonth;
      });

      const totalIncome = monthlyTransactions
        .filter(t => t.type === "income")
        .reduce((sum, t) => sum + t.amount, 0);

      const totalExpenses = monthlyTransactions
        .filter(t => t.type === "expense")
        .reduce((sum, t) => sum + Math.abs(t.amount), 0);

      const netIncome = totalIncome - totalExpenses;
      const savingsRate = totalIncome > 0 ? (netIncome / totalIncome) * 100 : 0;

      return {
        overview: {
          total_balance: totalBalance,
          total_income: totalIncome,
          total_expenses: totalExpenses,
          net_income: netIncome,
          savings_rate: savingsRate,
          group_count: groups.length,
          account_count: accounts.length,
          active_budgets: activeBudgets,
          active_goals: activeGoals,
          member_count: groups.reduce((sum, group) => sum + group.member_count, 0),
        },
        spending_by_category: [], // TODO: Implement
        income_vs_expenses: [], // TODO: Implement
        budget_performance: [], // TODO: Implement
        goal_progress: [], // TODO: Implement
        member_contributions: [], // TODO: Implement
        monthly_trends: [], // TODO: Implement
      };
    } catch (error) {
      console.error("Error getting family analytics:", error);
      throw error;
    }
  }
}

export const familyFinanceService = new FamilyFinanceService();
