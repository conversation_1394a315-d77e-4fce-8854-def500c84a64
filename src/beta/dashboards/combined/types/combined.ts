import {
  PersonalFinanceData,
  PersonalAnalytics,
} from "../../personal/types/personal";

export interface FamilyFinanceData {
  groups: FamilyGroup[];
  total_balance: number;
  monthly_income: number;
  monthly_expenses: number;
  active_budgets: number;
  active_goals: number;
  recent_transactions: FamilyTransaction[];
}

export interface FamilyGroup {
  id: string;
  name: string;
  description?: string;
  member_count: number;
  balance: number;
  monthly_income: number;
  monthly_expenses: number;
  currency: string;
  is_admin: boolean;
  created_at: string;
  updated_at: string;
}

export interface FamilyTransaction {
  id: string;
  group_id: string;
  group_name: string;
  amount: number;
  description: string;
  type: "income" | "expense" | "transfer";
  category?: string;
  date: string;
  created_by: string;
  currency: string;
}

export interface CombinedFinanceData {
  personal: PersonalFinanceData;
  family: FamilyFinanceData;
  unified: UnifiedAnalytics;
  user_preferences: UserPreferences;
}

export interface UnifiedAnalytics {
  total_balance: number;
  total_monthly_income: number;
  total_monthly_expenses: number;
  net_cash_flow: number;
  savings_rate: number;
  personal_vs_family: {
    personal_balance: number;
    family_balance: number;
    personal_spending: number;
    family_spending: number;
    personal_income: number;
    family_income: number;
  };
  goal_progress: UnifiedGoalProgress[];
  spending_comparison: SpendingComparison[];
  cash_flow_trends: CashFlowTrend[];
}

export interface UnifiedGoalProgress {
  id: string;
  name: string;
  type: "personal" | "family";
  target_amount: number;
  current_amount: number;
  progress: number;
  days_remaining: number;
  monthly_required: number;
  currency: string;
  group_name?: string;
}

export interface SpendingComparison {
  category: string;
  personal_amount: number;
  family_amount: number;
  total_amount: number;
  percentage: number;
  color: string;
}

export interface CashFlowTrend {
  month: string;
  personal_income: number;
  personal_expenses: number;
  family_income: number;
  family_expenses: number;
  net_flow: number;
}

export interface UserPreferences {
  show_amounts: boolean;
  preferred_currency: string;
  default_view: "personal" | "family" | "unified";
  analytics_period: "month" | "quarter" | "year";
}

export interface CombinedFinanceInsight {
  id: string;
  type:
    | "cross_account"
    | "budget_optimization"
    | "goal_alignment"
    | "spending_pattern"
    | "cash_flow";
  title: string;
  description: string;
  priority: "high" | "medium" | "low";
  action_required: boolean;
  action_text?: string;
  data_points: Record<string, any>;
  created_at: string;
}

// Form types for managing combined finances
export interface CombinedTransactionFormData {
  amount: number;
  description: string;
  type: "income" | "expense" | "transfer";
  source: "personal" | "family";
  category_id?: string;
  account_id?: string;
  group_id?: string;
  date: string;
  currency: string;
}

export interface CombinedBudgetFormData {
  name: string;
  amount: number;
  category_id: string;
  scope: "personal" | "family" | "unified";
  period: "weekly" | "monthly" | "yearly";
  start_date: string;
  end_date?: string;
  group_id?: string;
  currency: string;
}

export interface CombinedGoalFormData {
  name: string;
  description?: string;
  target_amount: number;
  current_amount?: number;
  target_date: string;
  scope: "personal" | "family" | "unified";
  icon?: string;
  currency: string;
  group_id?: string;
}

export type FinanceMode = "personal" | "family" | "unified";

export interface ModeConfiguration {
  mode: FinanceMode;
  enabled_features: string[];
  data_sources: string[];
  analytics_config: {
    include_personal: boolean;
    include_family: boolean;
    comparison_enabled: boolean;
    forecasting_enabled: boolean;
  };
}
