"use client";

import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/shared/components/ui/card";
import { Button } from "@/shared/components/ui/button";
import { Badge } from "@/shared/components/ui/badge";
import { Progress } from "@/shared/components/ui/progress";
import {
  Users,
  DollarSign,
  TrendingUp,
  TrendingDown,
  Plus,
  Eye,
  EyeOff,
  Settings,
  UserPlus,
  Calendar,
  ArrowRight,
} from "lucide-react";
import { FamilyFinanceData, FamilyGroup } from "../types/combined";
import { combinedFinanceService } from "../services/combinedFinanceService";
import { useBetaFinanceType } from "@/beta/contexts/BetaFinanceTypeContext";
import { useToast } from "@/shared/hooks/use-toast";

interface FamilyOverviewProps {
  onCreateGroup?: () => void;
  onJoinGroup?: () => void;
  onViewGroup?: (groupId: string) => void;
  onAddTransaction?: () => void;
}

export function FamilyOverview({
  onCreateGroup,
  onJoinGroup,
  onViewGroup,
  onAddTransaction,
}: FamilyOverviewProps) {
  const [data, setData] = useState<FamilyFinanceData | null>(null);
  const [loading, setLoading] = useState(true);
  const [showBalances, setShowBalances] = useState(true);
  const { user } = useBetaFinanceType();
  const { toast } = useToast();

  useEffect(() => {
    if (user) {
      loadFamilyFinanceData();
    }
  }, [user]);

  const loadFamilyFinanceData = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const familyData = await combinedFinanceService.getFamilyFinanceData(
        user.id
      );
      setData(familyData);
    } catch (error) {
      console.error("Error loading family finance data:", error);
      toast({
        title: "Error",
        description: "Failed to load family financial data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    if (!showBalances) return "••••••";
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: user?.currency || "USD",
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {[...Array(3)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="space-y-0 pb-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-full"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!data || data.groups.length === 0) {
    return (
      <div className="space-y-6">
        {/* Header with Balance Toggle */}
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">
              Family Finance
            </h2>
            <p className="text-muted-foreground">
              Manage shared finances with your family groups
            </p>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowBalances(!showBalances)}
            className="flex items-center gap-2"
          >
            {showBalances ? (
              <Eye className="w-4 h-4" />
            ) : (
              <EyeOff className="w-4 h-4" />
            )}
            {showBalances ? "Hide" : "Show"} Amounts
          </Button>
        </div>

        {/* Empty State */}
        <Card className="border-2 border-dashed border-green-200 bg-green-50/50">
          <CardContent className="text-center py-12">
            <Users className="w-16 h-16 mx-auto text-green-600 mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Family Groups Yet</h3>
            <p className="text-muted-foreground mb-6 max-w-md mx-auto">
              Create or join a family group to start managing shared finances,
              budgets, and goals together with your loved ones.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Button
                onClick={onCreateGroup}
                className="flex items-center gap-2"
              >
                <Plus className="w-4 h-4" />
                Create Family Group
              </Button>
              <Button
                onClick={onJoinGroup}
                variant="outline"
                className="flex items-center gap-2"
              >
                <UserPlus className="w-4 h-4" />
                Join Existing Group
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Benefits Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5 text-green-600" />
              Family Finance Benefits
            </CardTitle>
            <CardDescription>
              Why manage your finances together?
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="p-2 rounded-full bg-green-100">
                    <DollarSign className="h-4 w-4 text-green-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold">Shared Budgets</h4>
                    <p className="text-sm text-muted-foreground">
                      Create and manage budgets together for household expenses,
                      vacations, and major purchases.
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="p-2 rounded-full bg-blue-100">
                    <TrendingUp className="h-4 w-4 text-blue-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold">Joint Goals</h4>
                    <p className="text-sm text-muted-foreground">
                      Work together towards common financial goals like
                      emergency funds, home purchases, or retirement.
                    </p>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="p-2 rounded-full bg-purple-100">
                    <Users className="h-4 w-4 text-purple-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold">Transparency</h4>
                    <p className="text-sm text-muted-foreground">
                      Everyone can see spending patterns and contribute to
                      better financial decisions.
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="p-2 rounded-full bg-orange-100">
                    <Settings className="h-4 w-4 text-orange-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold">Role-based Access</h4>
                    <p className="text-sm text-muted-foreground">
                      Control who can view, edit, or manage different aspects of
                      your family finances.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Balance Toggle */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Family Finance</h2>
          <p className="text-muted-foreground">
            Managing {data.groups.length} family{" "}
            {data.groups.length === 1 ? "group" : "groups"}
          </p>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowBalances(!showBalances)}
          className="flex items-center gap-2"
        >
          {showBalances ? (
            <Eye className="w-4 h-4" />
          ) : (
            <EyeOff className="w-4 h-4" />
          )}
          {showBalances ? "Hide" : "Show"} Amounts
        </Button>
      </div>

      {/* Family Summary Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Family Balance
            </CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(data.total_balance)}
            </div>
            <p className="text-xs text-muted-foreground">
              Across {data.groups.length} groups
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Monthly Income
            </CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(data.monthly_income)}
            </div>
            <p className="text-xs text-muted-foreground">
              Family groups combined
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Monthly Expenses
            </CardTitle>
            <TrendingDown className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(data.monthly_expenses)}
            </div>
            <p className="text-xs text-muted-foreground">This month</p>
          </CardContent>
        </Card>
      </div>

      {/* Family Groups */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {data.groups.map((group) => (
          <Card key={group.id} className="border-l-4 border-green-500">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  {group.name}
                </CardTitle>
                <div className="flex items-center gap-2">
                  {group.is_admin && (
                    <Badge variant="outline" className="text-xs">
                      Admin
                    </Badge>
                  )}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onViewGroup?.(group.id)}
                    className="h-8 w-8 p-0"
                  >
                    <ArrowRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              {group.description && (
                <CardDescription>{group.description}</CardDescription>
              )}
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Financial Metrics */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium">Balance</p>
                  <p className="text-2xl font-bold">
                    {formatCurrency(group.balance)}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium">Net Flow</p>
                  <p
                    className={`text-2xl font-bold ${
                      group.monthly_income - group.monthly_expenses >= 0
                        ? "text-green-600"
                        : "text-red-600"
                    }`}
                  >
                    {formatCurrency(
                      group.monthly_income - group.monthly_expenses
                    )}
                  </p>
                </div>
              </div>

              {/* Income vs Expenses */}
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Monthly Income</span>
                  <span className="font-medium">
                    {formatCurrency(group.monthly_income)}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Monthly Expenses</span>
                  <span className="font-medium">
                    {formatCurrency(group.monthly_expenses)}
                  </span>
                </div>
              </div>

              {/* Group Info */}
              <div className="flex items-center justify-between pt-2 border-t">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Users className="w-4 h-4" />
                  {group.member_count} members
                </div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Calendar className="w-4 h-4" />
                  Since {formatDate(group.created_at)}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Recent Family Transactions */}
      {data.recent_transactions.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Recent Family Transactions
            </CardTitle>
            <CardDescription>
              Latest transactions across all your family groups
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {data.recent_transactions.slice(0, 5).map((transaction) => (
                <div
                  key={transaction.id}
                  className="flex items-center justify-between py-2 border-b last:border-b-0"
                >
                  <div>
                    <p className="font-medium">{transaction.description}</p>
                    <p className="text-sm text-muted-foreground">
                      {transaction.group_name} •{" "}
                      {transaction.category || "Uncategorized"}
                    </p>
                  </div>
                  <div className="text-right">
                    <p
                      className={`font-semibold ${
                        transaction.type === "income"
                          ? "text-green-600"
                          : "text-red-600"
                      }`}
                    >
                      {transaction.type === "income" ? "+" : "-"}
                      {formatCurrency(Math.abs(transaction.amount))}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {formatDate(transaction.date)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Family Finance Actions</CardTitle>
          <CardDescription>
            Manage your family financial activities
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button
              onClick={onAddTransaction}
              className="h-auto flex-col gap-2 py-4"
            >
              <Plus className="w-6 h-6" />
              Add Transaction
            </Button>
            <Button
              onClick={onCreateGroup}
              variant="outline"
              className="h-auto flex-col gap-2 py-4"
            >
              <Users className="w-6 h-6" />
              Create Group
            </Button>
            <Button
              onClick={onJoinGroup}
              variant="outline"
              className="h-auto flex-col gap-2 py-4"
            >
              <UserPlus className="w-6 h-6" />
              Join Group
            </Button>
            <Button variant="outline" className="h-auto flex-col gap-2 py-4">
              <Settings className="w-6 h-6" />
              Manage Groups
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
