"use client";

import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/shared/components/ui/card";
import { Button } from "@/shared/components/ui/button";
import { Badge } from "@/shared/components/ui/badge";
import { Progress } from "@/shared/components/ui/progress";
import {
  DollarSign,
  TrendingUp,
  TrendingDown,
  Users,
  User,
  Target,
  PiggyBank,
  AlertTriangle,
  CheckCircle,
  Plus,
  Eye,
  EyeOff,
  Layers,
  BarChart3,
  ArrowUpRight,
  ArrowDownRight,
} from "lucide-react";
import { CombinedFinanceData, CombinedFinanceInsight } from "../types/combined";
import { combinedFinanceService } from "../services/combinedFinanceService";
import { useBetaFinanceType } from "@/beta/contexts/BetaFinanceTypeContext";
import { useToast } from "@/shared/hooks/use-toast";

interface CombinedOverviewProps {
  onAddTransaction?: () => void;
  onAddAccount?: () => void;
  onViewBudgets?: () => void;
  onViewGoals?: () => void;
  onSwitchMode?: (mode: "personal" | "family" | "unified") => void;
}

export function CombinedOverview({
  onAddTransaction,
  onAddAccount,
  onViewBudgets,
  onViewGoals,
  onSwitchMode,
}: CombinedOverviewProps) {
  const [data, setData] = useState<CombinedFinanceData | null>(null);
  const [insights, setInsights] = useState<CombinedFinanceInsight[]>([]);
  const [loading, setLoading] = useState(true);
  const [showBalances, setShowBalances] = useState(true);
  const { user } = useBetaFinanceType();
  const { toast } = useToast();

  useEffect(() => {
    if (user) {
      loadCombinedFinanceData();
    }
  }, [user]);

  const loadCombinedFinanceData = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const [combinedData, combinedInsights] = await Promise.all([
        combinedFinanceService.getCombinedFinanceData(user.id),
        combinedFinanceService.getCombinedInsights(user.id),
      ]);

      setData(combinedData);
      setInsights(combinedInsights);
    } catch (error) {
      console.error("Error loading combined finance data:", error);
      toast({
        title: "Error",
        description: "Failed to load your financial data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    if (!showBalances) return "••••••";
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: data?.user_preferences.preferred_currency || "USD",
    }).format(amount);
  };

  const getInsightIcon = (type: string) => {
    switch (type) {
      case "cross_account":
        return <Layers className="h-4 w-4 text-purple-500" />;
      case "budget_optimization":
        return <Target className="h-4 w-4 text-orange-500" />;
      case "goal_alignment":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "spending_pattern":
        return <BarChart3 className="h-4 w-4 text-blue-500" />;
      case "cash_flow":
        return <TrendingUp className="h-4 w-4 text-red-500" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "bg-red-100 text-red-700 border-red-200";
      case "medium":
        return "bg-orange-100 text-orange-700 border-orange-200";
      case "low":
        return "bg-blue-100 text-blue-700 border-blue-200";
      default:
        return "bg-gray-100 text-gray-700 border-gray-200";
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="space-y-0 pb-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-full"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">
          No financial data available. Add an account to get started!
        </p>
        <Button onClick={onAddAccount} className="mt-4">
          <Plus className="w-4 h-4 mr-2" />
          Add Account
        </Button>
      </div>
    );
  }

  const { unified, personal, family } = data;

  return (
    <div className="space-y-6">
      {/* Header with Balance Toggle */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">
            Combined Finance Overview
          </h2>
          <p className="text-muted-foreground">
            Your complete financial picture - personal and family combined
          </p>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowBalances(!showBalances)}
          className="flex items-center gap-2"
        >
          {showBalances ? (
            <Eye className="w-4 h-4" />
          ) : (
            <EyeOff className="w-4 h-4" />
          )}
          {showBalances ? "Hide" : "Show"} Amounts
        </Button>
      </div>

      {/* Key Unified Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="border-l-4 border-purple-500">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Balance</CardTitle>
            <Layers className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(unified.total_balance)}
            </div>
            <p className="text-xs text-muted-foreground">
              Personal + Family combined
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Net Cash Flow</CardTitle>
            {unified.net_cash_flow >= 0 ? (
              <ArrowUpRight className="h-4 w-4 text-green-500" />
            ) : (
              <ArrowDownRight className="h-4 w-4 text-red-500" />
            )}
          </CardHeader>
          <CardContent>
            <div
              className={`text-2xl font-bold ${
                unified.net_cash_flow >= 0 ? "text-green-600" : "text-red-600"
              }`}
            >
              {formatCurrency(unified.net_cash_flow)}
            </div>
            <p className="text-xs text-muted-foreground">This month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Savings Rate</CardTitle>
            <PiggyBank className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {unified.savings_rate.toFixed(1)}%
            </div>
            <p className="text-xs text-muted-foreground">
              Combined savings rate
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Goals</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {unified.goal_progress.length}
            </div>
            <p className="text-xs text-muted-foreground">
              Personal + Family goals
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Personal vs Family Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Personal vs Family Breakdown
            </CardTitle>
            <CardDescription>
              Your financial distribution across personal and family accounts
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm flex items-center gap-2">
                  <User className="w-4 h-4 text-blue-500" />
                  Personal Balance
                </span>
                <span className="font-semibold">
                  {formatCurrency(unified.personal_vs_family.personal_balance)}
                </span>
              </div>
              <Progress
                value={
                  (unified.personal_vs_family.personal_balance /
                    unified.total_balance) *
                  100
                }
                className="h-2"
              />
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm flex items-center gap-2">
                  <Users className="w-4 h-4 text-green-500" />
                  Family Balance
                </span>
                <span className="font-semibold">
                  {formatCurrency(unified.personal_vs_family.family_balance)}
                </span>
              </div>
              <Progress
                value={
                  (unified.personal_vs_family.family_balance /
                    unified.total_balance) *
                  100
                }
                className="h-2"
              />
            </div>

            <div className="pt-2 border-t">
              <div className="flex items-center justify-between text-sm">
                <span>Personal Spending</span>
                <span>
                  {formatCurrency(unified.personal_vs_family.personal_spending)}
                </span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span>Family Spending</span>
                <span>
                  {formatCurrency(unified.personal_vs_family.family_spending)}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Combined Goal Progress */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Combined Goal Progress
            </CardTitle>
            <CardDescription>
              Track your personal and family financial goals together
            </CardDescription>
          </CardHeader>
          <CardContent>
            {unified.goal_progress.length === 0 ? (
              <div className="text-center py-4">
                <p className="text-sm text-muted-foreground mb-3">
                  No active goals found
                </p>
                <Button onClick={onViewGoals} size="sm">
                  <Plus className="w-4 h-4 mr-2" />
                  Add Goal
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {unified.goal_progress.slice(0, 3).map((goal) => (
                  <div key={goal.id} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium text-sm">{goal.name}</h4>
                        <p className="text-xs text-muted-foreground flex items-center gap-1">
                          {goal.type === "personal" ? (
                            <User className="w-3 h-3" />
                          ) : (
                            <Users className="w-3 h-3" />
                          )}
                          {goal.type === "family" && goal.group_name
                            ? goal.group_name
                            : goal.type}
                        </p>
                      </div>
                      <div className="text-right">
                        <div className="font-semibold text-sm">
                          {formatCurrency(goal.current_amount)} /{" "}
                          {formatCurrency(goal.target_amount)}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {goal.progress.toFixed(0)}% complete
                        </div>
                      </div>
                    </div>
                    <Progress value={goal.progress} className="h-2" />
                  </div>
                ))}
                {unified.goal_progress.length > 3 && (
                  <Button
                    onClick={onViewGoals}
                    variant="outline"
                    size="sm"
                    className="w-full"
                  >
                    View All {unified.goal_progress.length} Goals
                  </Button>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Family Groups Summary */}
      {family.groups.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Family Groups
            </CardTitle>
            <CardDescription>
              Your family financial groups overview
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {family.groups.map((group) => (
                <Card key={group.id} className="border-l-4 border-green-500">
                  <CardContent className="pt-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-semibold">{group.name}</h4>
                      {group.is_admin && (
                        <Badge variant="outline" className="text-xs">
                          Admin
                        </Badge>
                      )}
                    </div>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span>Balance:</span>
                        <span className="font-medium">
                          {formatCurrency(group.balance)}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span>Members:</span>
                        <span>{group.member_count}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Monthly Flow:</span>
                        <span
                          className={
                            group.monthly_income - group.monthly_expenses >= 0
                              ? "text-green-600"
                              : "text-red-600"
                          }
                        >
                          {formatCurrency(
                            group.monthly_income - group.monthly_expenses
                          )}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Combined Insights */}
      {insights.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              Smart Insights
            </CardTitle>
            <CardDescription>
              AI-powered insights from your combined financial data
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {insights.map((insight) => (
                <div
                  key={insight.id}
                  className={`p-4 rounded-lg border ${getPriorityColor(
                    insight.priority
                  )}`}
                >
                  <div className="flex items-start gap-3">
                    {getInsightIcon(insight.type)}
                    <div className="flex-1">
                      <h4 className="font-semibold">{insight.title}</h4>
                      <p className="text-sm mt-1">{insight.description}</p>
                      {insight.action_required && insight.action_text && (
                        <Button size="sm" className="mt-2">
                          {insight.action_text}
                        </Button>
                      )}
                    </div>
                    <Badge variant="outline" className="text-xs">
                      {insight.priority}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Manage your combined finances efficiently
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button
              onClick={onAddTransaction}
              className="h-auto flex-col gap-2 py-4"
            >
              <Plus className="w-6 h-6" />
              Add Transaction
            </Button>
            <Button
              onClick={onAddAccount}
              variant="outline"
              className="h-auto flex-col gap-2 py-4"
            >
              <DollarSign className="w-6 h-6" />
              Add Account
            </Button>
            <Button
              onClick={onViewBudgets}
              variant="outline"
              className="h-auto flex-col gap-2 py-4"
            >
              <Target className="w-6 h-6" />
              View Budgets
            </Button>
            <Button
              onClick={onViewGoals}
              variant="outline"
              className="h-auto flex-col gap-2 py-4"
            >
              <PiggyBank className="w-6 h-6" />
              View Goals
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
