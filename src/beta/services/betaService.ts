// Beta service for handling beta user operations
// This service works with the current database schema and simulates beta functionality

import { createClient } from '@/shared/services/supabase/client';
import { FinanceType, BetaUser, BetaFeedback } from '../types';

// Local storage keys for beta program simulation
const BETA_USERS_KEY = 'beta_users';
const BETA_FEEDBACK_KEY = 'beta_feedback';

export class BetaService {
  private supabase = createClient();

  // Get beta user data (simulated with localStorage for demo)
  async getBetaUser(userId: string): Promise<BetaUser | null> {
    try {
      // Get user profile from Supabase
      const { data: profile } = await this.supabase
        .from('user_profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (!profile) return null;

      // Get beta data from localStorage (simulating beta fields)
      const betaUsers = this.getBetaUsersFromStorage();
      const betaData = betaUsers[userId] || {};

      // Get auth user email
      const { data: { user } } = await this.supabase.auth.getUser();

      return {
        id: profile.id,
        email: user?.email || profile.email,
        full_name: profile.full_name,
        username: profile.username,
        avatar_url: profile.avatar_url,
        currency: profile.currency,
        finance_type: betaData.finance_type || 'personal',
        beta_user: betaData.beta_user || false,
        beta_joined_at: betaData.beta_joined_at,
        beta_feedback_count: betaData.beta_feedback_count || 0,
        onboarding_completed: betaData.onboarding_completed || false,
        enabled_features: betaData.enabled_features || {},
        dashboard_preference: betaData.dashboard_preference || 'auto',
        created_at: profile.created_at,
        updated_at: profile.updated_at
      };
    } catch (error) {
      console.error('Error getting beta user:', error);
      return null;
    }
  }

  // Update beta user data
  async updateBetaUser(userId: string, updates: Partial<BetaUser>): Promise<void> {
    try {
      const betaUsers = this.getBetaUsersFromStorage();
      betaUsers[userId] = { ...betaUsers[userId], ...updates };
      this.setBetaUsersInStorage(betaUsers);
    } catch (error) {
      console.error('Error updating beta user:', error);
    }
  }

  // Set finance type
  async setFinanceType(userId: string, financeType: FinanceType): Promise<void> {
    await this.updateBetaUser(userId, { 
      finance_type: financeType,
      dashboard_preference: financeType
    });
  }

  // Submit feedback (simulated with localStorage)
  async submitFeedback(userId: string, feedback: Omit<BetaFeedback, 'id' | 'user_id' | 'created_at'>): Promise<void> {
    try {
      const feedbackList = this.getFeedbackFromStorage();
      const newFeedback: BetaFeedback = {
        id: crypto.randomUUID(),
        user_id: userId,
        created_at: new Date().toISOString(),
        ...feedback
      };
      
      feedbackList.push(newFeedback);
      this.setFeedbackInStorage(feedbackList);

      // Update feedback count
      const betaUsers = this.getBetaUsersFromStorage();
      const currentUser = betaUsers[userId] || {};
      betaUsers[userId] = {
        ...currentUser,
        beta_feedback_count: (currentUser.beta_feedback_count || 0) + 1
      };
      this.setBetaUsersInStorage(betaUsers);
    } catch (error) {
      console.error('Error submitting feedback:', error);
    }
  }

  // Mark user as beta user
  async enableBetaAccess(userId: string): Promise<void> {
    await this.updateBetaUser(userId, {
      beta_user: true,
      beta_joined_at: new Date().toISOString()
    });
  }

  // Complete onboarding
  async completeOnboarding(userId: string): Promise<void> {
    await this.updateBetaUser(userId, {
      onboarding_completed: true
    });
  }

  // Reset onboarding (for testing different finance types)
  async resetOnboarding(userId: string): Promise<void> {
    await this.updateBetaUser(userId, {
      onboarding_completed: false,
      finance_type: 'personal',
      dashboard_preference: 'auto'
    });
  }

  // Reset all beta data (complete reset)
  async resetBetaData(userId: string): Promise<void> {
    const betaUsers = this.getBetaUsersFromStorage();
    delete betaUsers[userId];
    this.setBetaUsersInStorage(betaUsers);
  }

  // Private methods for localStorage management
  private getBetaUsersFromStorage(): Record<string, Partial<BetaUser>> {
    if (typeof window === 'undefined') return {};
    try {
      const stored = localStorage.getItem(BETA_USERS_KEY);
      return stored ? JSON.parse(stored) : {};
    } catch {
      return {};
    }
  }

  private setBetaUsersInStorage(betaUsers: Record<string, Partial<BetaUser>>): void {
    if (typeof window === 'undefined') return;
    try {
      localStorage.setItem(BETA_USERS_KEY, JSON.stringify(betaUsers));
    } catch (error) {
      console.error('Error storing beta users:', error);
    }
  }

  private getFeedbackFromStorage(): BetaFeedback[] {
    if (typeof window === 'undefined') return [];
    try {
      const stored = localStorage.getItem(BETA_FEEDBACK_KEY);
      return stored ? JSON.parse(stored) : [];
    } catch {
      return [];
    }
  }

  private setFeedbackInStorage(feedback: BetaFeedback[]): void {
    if (typeof window === 'undefined') return;
    try {
      localStorage.setItem(BETA_FEEDBACK_KEY, JSON.stringify(feedback));
    } catch (error) {
      console.error('Error storing feedback:', error);
    }
  }
}

export const betaService = new BetaService();