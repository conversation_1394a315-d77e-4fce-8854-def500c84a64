// Beta service for handling beta user operations
// This service provides real database integration for beta program functionality

import { createClient } from '@/shared/services/supabase/client';
import { FinanceType, BetaUser, BetaFeedback } from '../types';

export class BetaService {
  private supabase = createClient();

  // Get beta user data from database
  async getBetaUser(userId: string): Promise<BetaUser | null> {
    try {
      // Get user profile with beta fields from Supabase
      const { data: profile, error } = await this.supabase
        .from('user_profiles')
        .select(`
          id,
          email,
          full_name,
          username,
          avatar_url,
          currency,
          finance_type,
          beta_user,
          beta_joined_at,
          beta_feedback_count,
          onboarding_completed,
          enabled_features,
          dashboard_preference,
          created_at,
          updated_at
        `)
        .eq('id', userId)
        .single();

      if (error || !profile) {
        console.error('Error fetching user profile:', error);
        return null;
      }

      // Get auth user email as fallback
      const { data: { user } } = await this.supabase.auth.getUser();

      return {
        id: profile.id,
        email: profile.email || user?.email || null,
        full_name: profile.full_name,
        username: profile.username,
        avatar_url: profile.avatar_url,
        currency: profile.currency || 'USD',
        finance_type: (profile.finance_type as FinanceType) || 'personal',
        beta_user: profile.beta_user || false,
        beta_joined_at: profile.beta_joined_at,
        beta_feedback_count: profile.beta_feedback_count || 0,
        onboarding_completed: profile.onboarding_completed || false,
        enabled_features: profile.enabled_features || {},
        dashboard_preference: profile.dashboard_preference || 'auto',
        created_at: profile.created_at,
        updated_at: profile.updated_at
      };
    } catch (error) {
      console.error('Error getting beta user:', error);
      return null;
    }
  }

  // Update beta user data in database
  async updateBetaUser(userId: string, updates: Partial<BetaUser>): Promise<void> {
    try {
      // Filter out fields that shouldn't be updated directly
      const { id, email, created_at, ...updateFields } = updates;

      const { error } = await this.supabase
        .from('user_profiles')
        .update(updateFields)
        .eq('id', userId);

      if (error) {
        console.error('Error updating beta user:', error);
        throw error;
      }
    } catch (error) {
      console.error('Error updating beta user:', error);
      throw error;
    }
  }

  // Set finance type
  async setFinanceType(userId: string, financeType: FinanceType): Promise<void> {
    await this.updateBetaUser(userId, {
      finance_type: financeType,
      dashboard_preference: financeType
    });
  }

  // Submit feedback to database
  async submitFeedback(userId: string, feedback: Omit<BetaFeedback, 'id' | 'user_id' | 'created_at'>): Promise<void> {
    try {
      // Insert feedback into database
      const { error: feedbackError } = await this.supabase
        .from('beta_feedback')
        .insert({
          user_id: userId,
          feedback_type: feedback.feedback_type,
          content: feedback.content,
          rating: feedback.rating
        });

      if (feedbackError) {
        console.error('Error inserting feedback:', feedbackError);
        throw feedbackError;
      }

      // Update feedback count in user profile
      const { error: updateError } = await this.supabase
        .from('user_profiles')
        .update({
          beta_feedback_count: this.supabase.raw('beta_feedback_count + 1')
        })
        .eq('id', userId);

      if (updateError) {
        console.error('Error updating feedback count:', updateError);
        // Don't throw here as the feedback was successfully saved
      }
    } catch (error) {
      console.error('Error submitting feedback:', error);
      throw error;
    }
  }

  // Mark user as beta user
  async enableBetaAccess(userId: string): Promise<void> {
    await this.updateBetaUser(userId, {
      beta_user: true,
      beta_joined_at: new Date().toISOString()
    });
  }

  // Complete onboarding
  async completeOnboarding(userId: string): Promise<void> {
    await this.updateBetaUser(userId, {
      onboarding_completed: true
    });
  }

  // Reset onboarding (for testing different finance types)
  async resetOnboarding(userId: string): Promise<void> {
    await this.updateBetaUser(userId, {
      onboarding_completed: false,
      finance_type: 'personal',
      dashboard_preference: 'auto'
    });
  }

  // Reset all beta data (complete reset)
  async resetBetaData(userId: string): Promise<void> {
    try {
      // Reset beta fields to defaults
      const { error } = await this.supabase
        .from('user_profiles')
        .update({
          beta_user: false,
          beta_joined_at: null,
          beta_feedback_count: 0,
          finance_type: 'personal',
          onboarding_completed: false,
          enabled_features: {},
          dashboard_preference: 'auto'
        })
        .eq('id', userId);

      if (error) {
        console.error('Error resetting beta data:', error);
        throw error;
      }

      // Optionally delete all feedback for this user
      const { error: feedbackError } = await this.supabase
        .from('beta_feedback')
        .delete()
        .eq('user_id', userId);

      if (feedbackError) {
        console.error('Error deleting user feedback:', feedbackError);
        // Don't throw here as the main reset was successful
      }
    } catch (error) {
      console.error('Error resetting beta data:', error);
      throw error;
    }
  }

  // Get user feedback history
  async getUserFeedback(userId: string): Promise<BetaFeedback[]> {
    try {
      const { data, error } = await this.supabase
        .from('beta_feedback')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching user feedback:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('Error getting user feedback:', error);
      return [];
    }
  }

  // Get beta program statistics (for admin/analytics)
  async getBetaProgramStats(): Promise<any> {
    try {
      const { data, error } = await this.supabase
        .from('beta_program_analytics')
        .select('*')
        .single();

      if (error) {
        console.error('Error fetching beta program stats:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error getting beta program stats:', error);
      return null;
    }
  }
}

export const betaService = new BetaService();