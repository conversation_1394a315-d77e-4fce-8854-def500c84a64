// Service Factory for switching between real and mock services
// This factory returns the appropriate service based on the data mode

import { DataMode } from '../contexts/BetaFinanceTypeContext';

// Real services
import { PersonalFinanceService } from '../dashboards/personal/services/personalFinanceService';
import { FamilyFinanceService } from '../dashboards/family/services/familyFinanceService';
import { CombinedFinanceService } from '../dashboards/combined/services/combinedFinanceService';
import { BetaService } from './betaService';
import { AIInsightsService } from './aiInsightsService';

// Mock services
import { MockPersonalFinanceService } from './mockPersonalFinanceService';
import { MockFamilyFinanceService } from './mockFamilyFinanceService';
import { MockCombinedFinanceService } from './mockCombinedFinanceService';

// Service instances
const realPersonalService = new PersonalFinanceService();
const realFamilyService = new FamilyFinanceService();
const realCombinedService = new CombinedFinanceService();
const realBetaService = new BetaService();
const realAIInsightsService = new AIInsightsService();

const mockPersonalService = new MockPersonalFinanceService();
const mockFamilyService = new MockFamilyFinanceService();
const mockCombinedService = new MockCombinedFinanceService();

// Mock implementations for beta and AI services
class MockBetaService {
  async delay(ms: number = 300): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async getBetaUser(userId: string) {
    await this.delay();
    console.log('🎭 Using MOCK Beta User for user:', userId);
    return {
      id: userId,
      email: '<EMAIL>',
      full_name: 'Mock User',
      username: 'mockuser',
      avatar_url: null,
      currency: 'USD',
      finance_type: 'personal' as const,
      beta_user: true,
      beta_joined_at: '2024-01-01T00:00:00Z',
      beta_feedback_count: 2,
      onboarding_completed: true,
      enabled_features: {},
      dashboard_preference: 'auto',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: new Date().toISOString()
    };
  }

  async updateBetaUser(userId: string, updates: any) {
    await this.delay();
    console.log('🎭 MOCK: Updating beta user:', userId, updates);
  }

  async setFinanceType(userId: string, financeType: any) {
    await this.delay();
    console.log('🎭 MOCK: Setting finance type:', financeType);
  }

  async submitFeedback(userId: string, feedback: any) {
    await this.delay();
    console.log('🎭 MOCK: Submitting feedback:', feedback);
  }

  async enableBetaAccess(userId: string) {
    await this.delay();
    console.log('🎭 MOCK: Enabling beta access for user:', userId);
  }

  async completeOnboarding(userId: string) {
    await this.delay();
    console.log('🎭 MOCK: Completing onboarding for user:', userId);
  }

  async resetOnboarding(userId: string) {
    await this.delay();
    console.log('🎭 MOCK: Resetting onboarding for user:', userId);
  }

  async resetBetaData(userId: string) {
    await this.delay();
    console.log('🎭 MOCK: Resetting beta data for user:', userId);
  }

  async getUserFeedback(userId: string) {
    await this.delay();
    console.log('🎭 Using MOCK User Feedback for user:', userId);
    return [
      {
        id: 'feedback-1',
        user_id: userId,
        feedback_type: 'feature_request',
        content: 'Love the new dashboard design!',
        rating: 5,
        created_at: '2024-01-15T00:00:00Z'
      },
      {
        id: 'feedback-2',
        user_id: userId,
        feedback_type: 'bug_report',
        content: 'Minor issue with mobile responsiveness',
        rating: 4,
        created_at: '2024-01-10T00:00:00Z'
      }
    ];
  }

  async getBetaProgramStats() {
    await this.delay();
    console.log('🎭 Using MOCK Beta Program Stats');
    return {
      total_beta_users: 150,
      active_users: 120,
      feedback_count: 45,
      average_rating: 4.2,
      feature_adoption: {
        personal_finance: 95,
        family_finance: 60,
        combined_finance: 40
      }
    };
  }
}

class MockAIInsightsService {
  async delay(ms: number = 300): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async generateInsights(userId: string, financeType: string) {
    await this.delay();
    console.log('🎭 Using MOCK AI Insights for user:', userId, 'type:', financeType);
    
    return [
      {
        id: 'ai-insight-1',
        type: 'spending_pattern',
        priority: 'medium',
        title: 'Spending Pattern Analysis',
        description: 'Your grocery spending has increased 15% compared to last month.',
        impact: '$45 monthly increase',
        confidence: 85,
        category: 'Spending',
        actionable: true,
        timeframe: 'This month',
        action_text: 'Review grocery budget',
        related_data: { category: 'Groceries', increase: 15 },
        created_at: new Date().toISOString()
      },
      {
        id: 'ai-insight-2',
        type: 'goal_acceleration',
        priority: 'low',
        title: 'Goal Progress Optimization',
        description: 'You could reach your vacation goal 2 months earlier by increasing contributions by $50/month.',
        impact: '2 months faster',
        confidence: 90,
        category: 'Goals',
        actionable: true,
        timeframe: 'Next 6 months',
        action_text: 'Increase contributions',
        related_data: { goal: 'Vacation Fund', acceleration: 2 },
        created_at: new Date().toISOString()
      },
      {
        id: 'ai-insight-3',
        type: 'opportunity',
        priority: 'high',
        title: 'Investment Opportunity',
        description: 'With your high savings rate, consider diversifying into index funds.',
        impact: 'Potential 7% annual return',
        confidence: 75,
        category: 'Investments',
        actionable: true,
        timeframe: 'Consider now',
        action_text: 'Explore investments',
        related_data: { savings_rate: 46.5, potential_return: 7 },
        created_at: new Date().toISOString()
      }
    ];
  }

  async getInsightsSummary(userId: string, financeType: string) {
    await this.delay();
    console.log('🎭 Using MOCK AI Insights Summary for user:', userId);
    
    return {
      total_insights: 3,
      high_priority_count: 1,
      actionable_count: 3,
      potential_savings: 540, // $45 * 12 months
      confidence_score: 83,
      last_updated: new Date().toISOString()
    };
  }
}

const mockBetaService = new MockBetaService();
const mockAIInsightsService = new MockAIInsightsService();

// Service factory functions
export const getPersonalFinanceService = (dataMode: DataMode) => {
  if (dataMode === 'mock') {
    console.log('🎭 Using MOCK Personal Finance Service');
    return mockPersonalService;
  }
  console.log('🔗 Using REAL Personal Finance Service');
  return realPersonalService;
};

export const getFamilyFinanceService = (dataMode: DataMode) => {
  if (dataMode === 'mock') {
    console.log('🎭 Using MOCK Family Finance Service');
    return mockFamilyService;
  }
  console.log('🔗 Using REAL Family Finance Service');
  return realFamilyService;
};

export const getCombinedFinanceService = (dataMode: DataMode) => {
  if (dataMode === 'mock') {
    console.log('🎭 Using MOCK Combined Finance Service');
    return mockCombinedService;
  }
  console.log('🔗 Using REAL Combined Finance Service');
  return realCombinedService;
};

export const getBetaService = (dataMode: DataMode) => {
  if (dataMode === 'mock') {
    console.log('🎭 Using MOCK Beta Service');
    return mockBetaService as any; // Type assertion for compatibility
  }
  console.log('🔗 Using REAL Beta Service');
  return realBetaService;
};

export const getAIInsightsService = (dataMode: DataMode) => {
  if (dataMode === 'mock') {
    console.log('🎭 Using MOCK AI Insights Service');
    return mockAIInsightsService as any; // Type assertion for compatibility
  }
  console.log('🔗 Using REAL AI Insights Service');
  return realAIInsightsService;
};

// Utility function to get all services for a given data mode
export const getAllServices = (dataMode: DataMode) => {
  return {
    personalFinanceService: getPersonalFinanceService(dataMode),
    familyFinanceService: getFamilyFinanceService(dataMode),
    combinedFinanceService: getCombinedFinanceService(dataMode),
    betaService: getBetaService(dataMode),
    aiInsightsService: getAIInsightsService(dataMode)
  };
};

// Helper function to display current data mode
export const getDataModeInfo = (dataMode: DataMode) => {
  return {
    mode: dataMode,
    description: dataMode === 'mock' 
      ? 'Using mock data for testing and development' 
      : 'Using real database data',
    icon: dataMode === 'mock' ? '🎭' : '🔗',
    color: dataMode === 'mock' ? '#F59E0B' : '#10B981'
  };
};
