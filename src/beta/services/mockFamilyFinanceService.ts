// Mock Family Finance Service for testing purposes
// This service returns mock data instead of making real database calls

import { FamilyFinanceData } from '../dashboards/family/types/family';
import { generateMockFamilyData } from './mockDataGenerator';

export class MockFamilyFinanceService {
  private mockData: FamilyFinanceData;

  constructor() {
    this.mockData = generateMockFamilyData();
  }

  // Simulate async operations with small delays
  private async delay(ms: number = 300): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async getFamilyFinanceData(userId: string): Promise<FamilyFinanceData> {
    await this.delay();
    console.log('🎭 Using MOCK Family Finance Data for user:', userId);
    return this.mockData;
  }

  async getFamilyGroups(userId: string) {
    await this.delay();
    console.log('🎭 Using MOCK Family Groups for user:', userId);
    return this.mockData.groups;
  }

  async getFamilyAccounts(userId: string) {
    await this.delay();
    console.log('🎭 Using MOCK Family Accounts for user:', userId);
    return this.mockData.accounts;
  }

  async getRecentFamilyTransactions(userId: string, limit: number = 20) {
    await this.delay();
    console.log('🎭 Using MOCK Family Transactions for user:', userId);
    return this.mockData.transactions.slice(0, limit);
  }

  async getFamilyTransactionsByDateRange(userId: string, groupId: string, startDate: string, endDate: string) {
    await this.delay();
    console.log('🎭 Using MOCK Family Transactions by date range for user:', userId);
    return this.mockData.transactions.filter(t => 
      t.family_group_id === groupId && t.date >= startDate && t.date <= endDate
    );
  }

  async getFamilyBudgets(userId: string) {
    await this.delay();
    console.log('🎭 Using MOCK Family Budgets for user:', userId);
    return this.mockData.budgets;
  }

  async getFamilyGoals(userId: string) {
    await this.delay();
    console.log('🎭 Using MOCK Family Goals for user:', userId);
    return this.mockData.goals;
  }

  async getFamilyAnalytics(userId: string) {
    await this.delay();
    console.log('🎭 Using MOCK Family Analytics for user:', userId);
    return this.mockData.analytics;
  }

  // Create methods (simulate success)
  async createFamilyGroup(userId: string, groupData: any) {
    await this.delay();
    console.log('🎭 MOCK: Creating family group for user:', userId);
    const newGroup = {
      id: `mock-group-${Date.now()}`,
      ...groupData,
      owner_user_id: userId,
      member_count: 1,
      balance: 0,
      monthly_income: 0,
      monthly_expenses: 0,
      currency: 'USD',
      is_admin: true,
      is_member: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    this.mockData.groups.push(newGroup);
    return newGroup;
  }

  async createFamilyAccount(userId: string, groupId: string, accountData: any) {
    await this.delay();
    console.log('🎭 MOCK: Creating family account for user:', userId);
    const newAccount = {
      id: `mock-fam-acc-${Date.now()}`,
      family_group_id: groupId,
      ...accountData,
      balance: accountData.balance || 0,
      created_by_user_id: userId,
      permissions: { can_view: [], can_edit: [], can_transact: [] },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    this.mockData.accounts.push(newAccount);
    return newAccount;
  }

  async createFamilyTransaction(userId: string, groupId: string, transactionData: any) {
    await this.delay();
    console.log('🎭 MOCK: Creating family transaction for user:', userId);
    const newTransaction = {
      id: `mock-fam-txn-${Date.now()}`,
      family_group_id: groupId,
      ...transactionData,
      created_by: userId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      category: { id: 'cat-1', name: 'Other', icon: '📝', color: '#6B7280', type: transactionData.type },
      created_by_user: { full_name: 'Mock User', username: 'mockuser' }
    };
    this.mockData.transactions.unshift(newTransaction);
    return newTransaction;
  }

  async createFamilyBudget(userId: string, groupId: string, budgetData: any) {
    await this.delay();
    console.log('🎭 MOCK: Creating family budget for user:', userId);
    const newBudget = {
      id: `mock-fam-budget-${Date.now()}`,
      family_group_id: groupId,
      ...budgetData,
      spent: 0,
      remaining: budgetData.amount,
      percentage_used: 0,
      status: 'on_track' as const,
      created_by: userId,
      contributors: [],
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    this.mockData.budgets.push(newBudget);
    return newBudget;
  }

  async createFamilyGoal(userId: string, groupId: string, goalData: any) {
    await this.delay();
    console.log('🎭 MOCK: Creating family goal for user:', userId);
    const newGoal = {
      id: `mock-fam-goal-${Date.now()}`,
      family_group_id: groupId,
      ...goalData,
      current_amount: 0,
      progress_percentage: 0,
      monthly_target: goalData.target_amount / 12,
      status: 'active' as const,
      created_by: userId,
      contributors: [],
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    this.mockData.goals.push(newGoal);
    return newGoal;
  }

  // Update methods (simulate success)
  async updateFamilyGroup(groupId: string, updates: any) {
    await this.delay();
    console.log('🎭 MOCK: Updating family group:', groupId);
    const groupIndex = this.mockData.groups.findIndex(g => g.id === groupId);
    if (groupIndex !== -1) {
      this.mockData.groups[groupIndex] = {
        ...this.mockData.groups[groupIndex],
        ...updates,
        updated_at: new Date().toISOString()
      };
      return this.mockData.groups[groupIndex];
    }
    throw new Error('Family group not found');
  }

  async updateFamilyAccount(accountId: string, updates: any) {
    await this.delay();
    console.log('🎭 MOCK: Updating family account:', accountId);
    const accountIndex = this.mockData.accounts.findIndex(a => a.id === accountId);
    if (accountIndex !== -1) {
      this.mockData.accounts[accountIndex] = {
        ...this.mockData.accounts[accountIndex],
        ...updates,
        updated_at: new Date().toISOString()
      };
      return this.mockData.accounts[accountIndex];
    }
    throw new Error('Family account not found');
  }

  async updateFamilyTransaction(transactionId: string, updates: any) {
    await this.delay();
    console.log('🎭 MOCK: Updating family transaction:', transactionId);
    const transactionIndex = this.mockData.transactions.findIndex(t => t.id === transactionId);
    if (transactionIndex !== -1) {
      this.mockData.transactions[transactionIndex] = {
        ...this.mockData.transactions[transactionIndex],
        ...updates,
        updated_at: new Date().toISOString()
      };
      return this.mockData.transactions[transactionIndex];
    }
    throw new Error('Family transaction not found');
  }

  async updateFamilyBudget(budgetId: string, updates: any) {
    await this.delay();
    console.log('🎭 MOCK: Updating family budget:', budgetId);
    const budgetIndex = this.mockData.budgets.findIndex(b => b.id === budgetId);
    if (budgetIndex !== -1) {
      this.mockData.budgets[budgetIndex] = {
        ...this.mockData.budgets[budgetIndex],
        ...updates,
        updated_at: new Date().toISOString()
      };
      return this.mockData.budgets[budgetIndex];
    }
    throw new Error('Family budget not found');
  }

  async updateFamilyGoal(goalId: string, updates: any) {
    await this.delay();
    console.log('🎭 MOCK: Updating family goal:', goalId);
    const goalIndex = this.mockData.goals.findIndex(g => g.id === goalId);
    if (goalIndex !== -1) {
      this.mockData.goals[goalIndex] = {
        ...this.mockData.goals[goalIndex],
        ...updates,
        updated_at: new Date().toISOString()
      };
      return this.mockData.goals[goalIndex];
    }
    throw new Error('Family goal not found');
  }

  // Delete methods (simulate success)
  async deleteFamilyGroup(groupId: string) {
    await this.delay();
    console.log('🎭 MOCK: Deleting family group:', groupId);
    this.mockData.groups = this.mockData.groups.filter(g => g.id !== groupId);
  }

  async deleteFamilyAccount(accountId: string) {
    await this.delay();
    console.log('🎭 MOCK: Deleting family account:', accountId);
    this.mockData.accounts = this.mockData.accounts.filter(a => a.id !== accountId);
  }

  async deleteFamilyTransaction(transactionId: string) {
    await this.delay();
    console.log('🎭 MOCK: Deleting family transaction:', transactionId);
    this.mockData.transactions = this.mockData.transactions.filter(t => t.id !== transactionId);
  }

  async deleteFamilyBudget(budgetId: string) {
    await this.delay();
    console.log('🎭 MOCK: Deleting family budget:', budgetId);
    this.mockData.budgets = this.mockData.budgets.filter(b => b.id !== budgetId);
  }

  async deleteFamilyGoal(goalId: string) {
    await this.delay();
    console.log('🎭 MOCK: Deleting family goal:', goalId);
    this.mockData.goals = this.mockData.goals.filter(g => g.id !== goalId);
  }

  // Additional family-specific methods
  async getGroupMembers(groupId: string) {
    await this.delay();
    console.log('🎭 MOCK: Getting group members for:', groupId);
    return [
      {
        id: 'member-1',
        group_id: groupId,
        user_id: 'user-1',
        role: 'admin' as const,
        permissions: {
          can_view_transactions: true,
          can_add_transactions: true,
          can_edit_transactions: true,
          can_delete_transactions: true,
          can_manage_budgets: true,
          can_manage_goals: true,
          can_manage_accounts: true,
          can_invite_members: true,
          can_remove_members: true,
          can_edit_group: true
        },
        joined_at: '2024-01-01T00:00:00Z',
        user_profile: {
          full_name: 'John Smith',
          username: 'johnsmith',
          avatar_url: null,
          email: '<EMAIL>'
        }
      },
      {
        id: 'member-2',
        group_id: groupId,
        user_id: 'user-2',
        role: 'member' as const,
        permissions: {
          can_view_transactions: true,
          can_add_transactions: true,
          can_edit_transactions: true,
          can_delete_transactions: false,
          can_manage_budgets: true,
          can_manage_goals: true,
          can_manage_accounts: false,
          can_invite_members: false,
          can_remove_members: false,
          can_edit_group: false
        },
        joined_at: '2024-01-02T00:00:00Z',
        user_profile: {
          full_name: 'Jane Smith',
          username: 'janesmith',
          avatar_url: null,
          email: '<EMAIL>'
        }
      }
    ];
  }

  async inviteToFamilyGroup(groupId: string, email: string, role: string, invitedBy: string) {
    await this.delay();
    console.log('🎭 MOCK: Inviting to family group:', { groupId, email, role });
    return {
      id: `mock-invite-${Date.now()}`,
      group_id: groupId,
      email,
      role,
      status: 'pending' as const,
      invited_by: invitedBy,
      created_at: new Date().toISOString(),
      expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      group: {
        name: 'Smith Family',
        description: 'Our family finances'
      },
      invited_by_user: {
        full_name: 'John Smith',
        username: 'johnsmith'
      }
    };
  }
}

export const mockFamilyFinanceService = new MockFamilyFinanceService();
