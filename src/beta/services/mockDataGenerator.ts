// Mock data generator for testing purposes
// This generates realistic financial data for testing the beta features

import { PersonalFinanceData } from '../dashboards/personal/types/personal';
import { FamilyFinanceData } from '../dashboards/family/types/family';
import { CombinedFinanceData } from '../dashboards/combined/types/combined';

// Helper function to generate random dates
const getRandomDate = (daysBack: number = 30): string => {
  const date = new Date();
  date.setDate(date.getDate() - Math.floor(Math.random() * daysBack));
  return date.toISOString().split('T')[0];
};

// Helper function to generate random amounts
const getRandomAmount = (min: number = 10, max: number = 1000): number => {
  return Math.floor(Math.random() * (max - min) + min);
};

// Generate mock personal finance data
export const generateMockPersonalData = (): PersonalFinanceData => {
  const mockAccounts = [
    {
      id: 'acc-1',
      name: 'Main Checking',
      type: 'checking',
      balance: 2500.75,
      currency: 'USD',
      institution: 'Chase Bank',
      is_active: true,
      created_at: '2024-01-15T00:00:00Z',
      updated_at: '2024-01-15T00:00:00Z'
    },
    {
      id: 'acc-2',
      name: 'Savings Account',
      type: 'savings',
      balance: 15000.00,
      currency: 'USD',
      institution: 'Chase Bank',
      is_active: true,
      created_at: '2024-01-15T00:00:00Z',
      updated_at: '2024-01-15T00:00:00Z'
    },
    {
      id: 'acc-3',
      name: 'Credit Card',
      type: 'credit',
      balance: -850.25,
      currency: 'USD',
      institution: 'Capital One',
      is_active: true,
      created_at: '2024-01-15T00:00:00Z',
      updated_at: '2024-01-15T00:00:00Z'
    }
  ];

  const mockTransactions = [
    {
      id: 'txn-1',
      account_id: 'acc-1',
      category_id: 'cat-1',
      amount: -85.50,
      description: 'Grocery Store',
      type: 'expense' as const,
      date: getRandomDate(7),
      currency: 'USD',
      created_at: '2024-01-15T00:00:00Z',
      updated_at: '2024-01-15T00:00:00Z',
      category: { id: 'cat-1', name: 'Groceries', icon: '🛒', color: '#10B981', type: 'expense' }
    },
    {
      id: 'txn-2',
      account_id: 'acc-1',
      category_id: 'cat-2',
      amount: 3200.00,
      description: 'Salary Deposit',
      type: 'income' as const,
      date: getRandomDate(3),
      currency: 'USD',
      created_at: '2024-01-15T00:00:00Z',
      updated_at: '2024-01-15T00:00:00Z',
      category: { id: 'cat-2', name: 'Salary', icon: '💰', color: '#059669', type: 'income' }
    },
    {
      id: 'txn-3',
      account_id: 'acc-3',
      category_id: 'cat-3',
      amount: -45.00,
      description: 'Gas Station',
      type: 'expense' as const,
      date: getRandomDate(5),
      currency: 'USD',
      created_at: '2024-01-15T00:00:00Z',
      updated_at: '2024-01-15T00:00:00Z',
      category: { id: 'cat-3', name: 'Transportation', icon: '⛽', color: '#F59E0B', type: 'expense' }
    },
    {
      id: 'txn-4',
      account_id: 'acc-1',
      category_id: 'cat-4',
      amount: -1200.00,
      description: 'Rent Payment',
      type: 'expense' as const,
      date: getRandomDate(1),
      currency: 'USD',
      created_at: '2024-01-15T00:00:00Z',
      updated_at: '2024-01-15T00:00:00Z',
      category: { id: 'cat-4', name: 'Housing', icon: '🏠', color: '#EF4444', type: 'expense' }
    },
    {
      id: 'txn-5',
      account_id: 'acc-1',
      category_id: 'cat-5',
      amount: -25.99,
      description: 'Netflix Subscription',
      type: 'expense' as const,
      date: getRandomDate(10),
      currency: 'USD',
      created_at: '2024-01-15T00:00:00Z',
      updated_at: '2024-01-15T00:00:00Z',
      category: { id: 'cat-5', name: 'Entertainment', icon: '🎬', color: '#8B5CF6', type: 'expense' }
    }
  ];

  const mockBudgets = [
    {
      id: 'budget-1',
      name: 'Monthly Groceries',
      category_id: 'cat-1',
      amount: 400.00,
      period: 'monthly' as const,
      period_start: '2024-01-01',
      period_end: '2024-01-31',
      spent: 285.50,
      remaining: 114.50,
      percentage_used: 71.4,
      status: 'on_track' as const,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-15T00:00:00Z',
      category: { id: 'cat-1', name: 'Groceries', icon: '🛒', color: '#10B981', type: 'expense' }
    },
    {
      id: 'budget-2',
      name: 'Transportation',
      category_id: 'cat-3',
      amount: 200.00,
      period: 'monthly' as const,
      period_start: '2024-01-01',
      period_end: '2024-01-31',
      spent: 165.00,
      remaining: 35.00,
      percentage_used: 82.5,
      status: 'warning' as const,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-15T00:00:00Z',
      category: { id: 'cat-3', name: 'Transportation', icon: '⛽', color: '#F59E0B', type: 'expense' }
    },
    {
      id: 'budget-3',
      name: 'Entertainment',
      category_id: 'cat-5',
      amount: 100.00,
      period: 'monthly' as const,
      period_start: '2024-01-01',
      period_end: '2024-01-31',
      spent: 125.99,
      remaining: -25.99,
      percentage_used: 126.0,
      status: 'exceeded' as const,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-15T00:00:00Z',
      category: { id: 'cat-5', name: 'Entertainment', icon: '🎬', color: '#8B5CF6', type: 'expense' }
    }
  ];

  const mockGoals = [
    {
      id: 'goal-1',
      name: 'Emergency Fund',
      description: 'Build 6 months of expenses',
      target_amount: 20000.00,
      current_amount: 15000.00,
      target_date: '2024-12-31',
      status: 'active' as const,
      progress_percentage: 75.0,
      monthly_target: 500.00,
      days_remaining: 320,
      currency: 'USD',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-15T00:00:00Z'
    },
    {
      id: 'goal-2',
      name: 'Vacation Fund',
      description: 'Save for Europe trip',
      target_amount: 5000.00,
      current_amount: 1200.00,
      target_date: '2024-08-01',
      status: 'active' as const,
      progress_percentage: 24.0,
      monthly_target: 760.00,
      days_remaining: 180,
      currency: 'USD',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-15T00:00:00Z'
    }
  ];

  const mockAnalytics = {
    overview: {
      total_balance: 16650.50,
      total_income: 3200.00,
      total_expenses: 1356.49,
      net_income: 1843.51,
      savings_rate: 57.6,
      account_count: 3,
      active_budgets: 3,
      active_goals: 2
    },
    spending_by_category: [
      { category: 'Housing', amount: 1200.00, percentage: 88.5, color: '#EF4444', transaction_count: 1 },
      { category: 'Groceries', amount: 85.50, percentage: 6.3, color: '#10B981', transaction_count: 1 },
      { category: 'Transportation', amount: 45.00, percentage: 3.3, color: '#F59E0B', transaction_count: 1 },
      { category: 'Entertainment', amount: 25.99, percentage: 1.9, color: '#8B5CF6', transaction_count: 1 }
    ],
    income_vs_expenses: [
      { month: 'Jan 2024', income: 3200.00, expenses: 1356.49, net: 1843.51, savings_rate: 57.6 }
    ],
    budget_performance: [
      { budget_name: 'Monthly Groceries', allocated: 400.00, spent: 285.50, remaining: 114.50, percentage_used: 71.4, status: 'on_track' as const },
      { budget_name: 'Transportation', allocated: 200.00, spent: 165.00, remaining: 35.00, percentage_used: 82.5, status: 'warning' as const },
      { budget_name: 'Entertainment', allocated: 100.00, spent: 125.99, remaining: -25.99, percentage_used: 126.0, status: 'exceeded' as const }
    ],
    goal_progress: [
      { goal_name: 'Emergency Fund', target_amount: 20000.00, current_amount: 15000.00, progress_percentage: 75.0, on_track: true },
      { goal_name: 'Vacation Fund', target_amount: 5000.00, current_amount: 1200.00, progress_percentage: 24.0, on_track: false }
    ],
    monthly_trends: [
      { month: 'Dec 2023', income: 3200.00, expenses: 1450.00, net: 1750.00 },
      { month: 'Jan 2024', income: 3200.00, expenses: 1356.49, net: 1843.51 }
    ]
  };

  return {
    accounts: mockAccounts,
    transactions: mockTransactions,
    budgets: mockBudgets,
    goals: mockGoals,
    analytics: mockAnalytics
  };
};

// Generate mock family finance data
export const generateMockFamilyData = (): FamilyFinanceData => {
  const mockGroups = [
    {
      id: 'group-1',
      name: 'Smith Family',
      description: 'Our family finances',
      owner_user_id: 'user-1',
      member_count: 3,
      balance: 8500.00,
      monthly_income: 6500.00,
      monthly_expenses: 4200.00,
      currency: 'USD',
      is_admin: true,
      is_member: true,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-15T00:00:00Z'
    }
  ];

  const mockAccounts = [
    {
      id: 'fam-acc-1',
      family_group_id: 'group-1',
      name: 'Family Checking',
      type: 'checking',
      balance: 3500.00,
      currency: 'USD',
      institution: 'Wells Fargo',
      is_active: true,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-15T00:00:00Z',
      created_by_user_id: 'user-1',
      permissions: { can_view: [], can_edit: [], can_transact: [] }
    },
    {
      id: 'fam-acc-2',
      family_group_id: 'group-1',
      name: 'Family Savings',
      type: 'savings',
      balance: 5000.00,
      currency: 'USD',
      institution: 'Wells Fargo',
      is_active: true,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-15T00:00:00Z',
      created_by_user_id: 'user-1',
      permissions: { can_view: [], can_edit: [], can_transact: [] }
    }
  ];

  const mockTransactions = [
    {
      id: 'fam-txn-1',
      family_group_id: 'group-1',
      account_id: 'fam-acc-1',
      amount: -150.00,
      description: 'Family Grocery Shopping',
      type: 'expense' as const,
      date: getRandomDate(3),
      currency: 'USD',
      created_by: 'user-1',
      created_at: '2024-01-15T00:00:00Z',
      updated_at: '2024-01-15T00:00:00Z',
      category: { id: 'cat-1', name: 'Groceries', icon: '🛒', color: '#10B981', type: 'expense' },
      created_by_user: { full_name: 'John Smith', username: 'johnsmith' }
    },
    {
      id: 'fam-txn-2',
      family_group_id: 'group-1',
      account_id: 'fam-acc-1',
      amount: 6500.00,
      description: 'Combined Salaries',
      type: 'income' as const,
      date: getRandomDate(1),
      currency: 'USD',
      created_by: 'user-1',
      created_at: '2024-01-15T00:00:00Z',
      updated_at: '2024-01-15T00:00:00Z',
      category: { id: 'cat-2', name: 'Salary', icon: '💰', color: '#059669', type: 'income' },
      created_by_user: { full_name: 'John Smith', username: 'johnsmith' }
    }
  ];

  const mockBudgets = [
    {
      id: 'fam-budget-1',
      family_group_id: 'group-1',
      name: 'Family Groceries',
      amount: 600.00,
      period: 'monthly' as const,
      period_start: '2024-01-01',
      period_end: '2024-01-31',
      spent: 450.00,
      remaining: 150.00,
      percentage_used: 75.0,
      status: 'on_track' as const,
      created_by: 'user-1',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-15T00:00:00Z',
      contributors: []
    }
  ];

  const mockGoals = [
    {
      id: 'fam-goal-1',
      family_group_id: 'group-1',
      name: 'Family Vacation',
      description: 'Disney World trip',
      target_amount: 8000.00,
      current_amount: 2500.00,
      target_date: '2024-07-01',
      status: 'active' as const,
      progress_percentage: 31.25,
      monthly_target: 1100.00,
      days_remaining: 150,
      currency: 'USD',
      created_by: 'user-1',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-15T00:00:00Z',
      contributors: []
    }
  ];

  const mockAnalytics = {
    overview: {
      total_balance: 8500.00,
      total_income: 6500.00,
      total_expenses: 4200.00,
      net_income: 2300.00,
      savings_rate: 35.4,
      group_count: 1,
      account_count: 2,
      active_budgets: 1,
      active_goals: 1,
      member_count: 3
    },
    spending_by_category: [],
    income_vs_expenses: [],
    budget_performance: [],
    goal_progress: [],
    member_contributions: [],
    monthly_trends: []
  };

  return {
    groups: mockGroups,
    accounts: mockAccounts,
    transactions: mockTransactions,
    budgets: mockBudgets,
    goals: mockGoals,
    analytics: mockAnalytics
  };
};

// Generate mock combined finance data
export const generateMockCombinedData = (): CombinedFinanceData => {
  const personalData = generateMockPersonalData();
  const familyData = generateMockFamilyData();

  return {
    personal: personalData,
    family: {
      groups: familyData.groups,
      total_balance: 8500.00,
      monthly_income: 6500.00,
      monthly_expenses: 4200.00,
      active_budgets: 1,
      active_goals: 1,
      recent_transactions: familyData.transactions.slice(0, 5).map(t => ({
        id: t.id,
        group_id: t.family_group_id,
        group_name: 'Smith Family',
        amount: t.amount,
        description: t.description,
        type: t.type,
        category: t.category?.name || 'Other',
        date: t.date,
        created_by: t.created_by,
        currency: t.currency
      }))
    },
    unified: {
      total_balance: 25150.50, // Personal + Family
      net_cash_flow: 4143.51, // Personal + Family net
      savings_rate: 46.5, // Combined savings rate
      personal_vs_family: {
        personal_balance: 16650.50,
        family_balance: 8500.00,
        personal_percentage: 66.2,
        family_percentage: 33.8
      },
      cross_account_insights: [],
      goal_alignment: {
        aligned_goals: 1,
        conflicting_goals: 0,
        total_goals: 3,
        alignment_score: 85
      }
    },
    user_preferences: {
      default_view: 'unified',
      finance_mode: 'combined' as const,
      show_family_data: true,
      currency: 'USD',
      date_format: 'MM/DD/YYYY',
      notifications: {
        budget_alerts: true,
        goal_reminders: true,
        family_updates: true
      }
    }
  };
};
