import { FinanceJourney, FinanceType, FinanceTypeCapabilities } from "../types";

export const financeJourneys: FinanceJourney[] = [
  {
    type: "personal",
    title: "Personal Finance Mastery",
    description:
      "Take control of your individual financial journey with clean, focused tools designed just for you.",
    benefits: [
      "Clean, distraction-free interface",
      "50% faster task completion",
      "Personal financial insights",
      "Goal-focused tracking",
      "Simplified budgeting tools",
    ],
    features: [
      "Personal accounts & transactions",
      "Individual budgets & goals",
      "Spending analytics",
      "Category management",
      "Financial reports",
    ],
    iconName: "User",
    color: "blue",
    recommended: false,
  },
  {
    type: "family",
    title: "Family Financial Harmony",
    description:
      "Collaborate seamlessly with your family members to achieve shared financial goals together.",
    benefits: [
      "Real-time collaboration",
      "Shared financial visibility",
      "Role-based permissions",
      "Family activity feeds",
      "Unified financial planning",
    ],
    features: [
      "Shared family accounts",
      "Collaborative budgeting",
      "Member management",
      "Family financial goals",
      "Group expense tracking",
    ],
    iconName: "Users",
    color: "green",
    recommended: true,
  },
  {
    type: "combined",
    title: "Complete Financial Control",
    description:
      "Power user experience with full access to both personal and family financial management.",
    benefits: [
      "Complete financial picture",
      "Advanced reporting & analytics",
      "Flexible mode switching",
      "Cross-cutting insights",
      "Custom configurations",
    ],
    features: [
      "Personal + family accounts",
      "Unified financial dashboard",
      "Advanced analytics",
      "Custom workflows",
      "Power user tools",
    ],
    iconName: "Settings",
    color: "purple",
    recommended: false,
  },
];

export const getCapabilitiesForType = (
  financeType: FinanceType
): FinanceTypeCapabilities => {
  switch (financeType) {
    case "personal":
      return {
        canAccessPersonal: true,
        canAccessFamily: false,
        canCreateFamilyGroups: false,
        canSwitchModes: false,
        showModeToggle: false,
        simplified: true,
        collaborationMode: false,
      };
    case "family":
      return {
        canAccessPersonal: false,
        canAccessFamily: true,
        canCreateFamilyGroups: true,
        canSwitchModes: false,
        showModeToggle: false,
        simplified: false,
        collaborationMode: true,
      };
    case "combined":
      return {
        canAccessPersonal: true,
        canAccessFamily: true,
        canCreateFamilyGroups: true,
        canSwitchModes: true,
        showModeToggle: true,
        simplified: false,
        collaborationMode: false,
      };
  }
};

export const getNavigationForType = (financeType: FinanceType) => {
  const baseNavigation = [
    { href: "/beta/dashboard", label: "Dashboard", icon: "LayoutDashboard" },
  ];

  switch (financeType) {
    case "personal":
      return [
        ...baseNavigation,
        {
          href: "/beta/dashboard/personal/accounts",
          label: "Accounts",
          icon: "CreditCard",
        },
        {
          href: "/beta/dashboard/personal/transactions",
          label: "Transactions",
          icon: "ArrowRightLeft",
        },
        {
          href: "/beta/dashboard/personal/budgets",
          label: "Budgets",
          icon: "PieChart",
        },
        {
          href: "/beta/dashboard/personal/goals",
          label: "Goals",
          icon: "Target",
        },
        {
          href: "/beta/dashboard/personal/analytics",
          label: "Analytics",
          icon: "BarChart3",
        },
      ];
    case "family":
      return [
        ...baseNavigation,
        {
          href: "/beta/dashboard/family/groups",
          label: "Family Groups",
          icon: "Users",
        },
        {
          href: "/beta/dashboard/family/accounts",
          label: "Shared Accounts",
          icon: "CreditCard",
        },
        {
          href: "/beta/dashboard/family/budgets",
          label: "Family Budgets",
          icon: "PieChart",
        },
        {
          href: "/beta/dashboard/family/goals",
          label: "Family Goals",
          icon: "Target",
        },
        {
          href: "/beta/dashboard/family/reports",
          label: "Family Reports",
          icon: "BarChart3",
        },
      ];
    case "combined":
      return [
        ...baseNavigation,
        { href: "/beta/dashboard/combined", label: "Combined", icon: "Layers" },
        { href: "/beta/dashboard/personal", label: "Personal", icon: "User" },
        { href: "/beta/dashboard/family", label: "Family", icon: "Users" },
        {
          href: "/beta/dashboard/unified",
          label: "Analytics",
          icon: "BarChart3",
        },
        {
          href: "/beta/dashboard/ai-insights",
          label: "AI Insights",
          icon: "TrendingUp",
        },
      ];
  }
};
