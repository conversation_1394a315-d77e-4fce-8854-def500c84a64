'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/shared/components/ui/card';
import { Button } from '@/shared/components/ui/button';
import { Badge } from '@/shared/components/ui/badge';
import { Check, User, Users, Settings, Star } from 'lucide-react';
import { FinanceType } from '../types';
import { financeJourneys } from '../config/finance-journeys';

interface FinanceJourneySelectorProps {
  selectedType?: FinanceType;
  onSelect: (type: FinanceType) => void;
  onContinue: () => void;
  className?: string;
}

const iconMap = {
  User,
  Users,
  Settings
};

export function FinanceJourneySelector({ 
  selectedType, 
  onSelect, 
  onContinue,
  className 
}: FinanceJourneySelectorProps) {
  const [hoveredType, setHoveredType] = useState<FinanceType | null>(null);

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="text-center space-y-2">
        <h2 className="text-3xl font-bold tracking-tight">Choose Your Financial Journey</h2>
        <p className="text-muted-foreground text-lg">
          Select the experience that best fits your financial management needs
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {financeJourneys.map((journey) => {
          const Icon = iconMap[journey.iconName as keyof typeof iconMap];
          const isSelected = selectedType === journey.type;
          const isHovered = hoveredType === journey.type;

          return (
            <Card
              key={journey.type}
              className={`relative cursor-pointer transition-all duration-200 hover:shadow-lg ${
                isSelected 
                  ? 'ring-2 ring-primary border-primary' 
                  : 'hover:border-primary/50'
              }`}
              onClick={() => onSelect(journey.type)}
              onMouseEnter={() => setHoveredType(journey.type)}
              onMouseLeave={() => setHoveredType(null)}
            >
              {journey.recommended && (
                <div className="absolute -top-2 left-1/2 -translate-x-1/2">
                  <Badge variant="secondary" className="bg-primary text-primary-foreground">
                    <Star className="w-3 h-3 mr-1" />
                    Recommended
                  </Badge>
                </div>
              )}
              
              {isSelected && (
                <div className="absolute -top-2 -right-2 bg-primary text-primary-foreground rounded-full p-1">
                  <Check className="w-4 h-4" />
                </div>
              )}

              <CardHeader className="text-center pb-4">
                <div className={`w-16 h-16 mx-auto rounded-full flex items-center justify-center mb-4 transition-colors ${
                  journey.color === 'blue' ? 'bg-blue-100 text-blue-600' :
                  journey.color === 'green' ? 'bg-green-100 text-green-600' :
                  'bg-purple-100 text-purple-600'
                }`}>
                  <Icon className="w-8 h-8" />
                </div>
                <CardTitle className="text-xl">{journey.title}</CardTitle>
                <CardDescription className="text-sm">
                  {journey.description}
                </CardDescription>
              </CardHeader>

              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-semibold text-sm mb-2">Key Benefits:</h4>
                  <ul className="space-y-1">
                    {journey.benefits.slice(0, 3).map((benefit, index) => (
                      <li key={index} className="text-sm text-muted-foreground flex items-start">
                        <Check className="w-3 h-3 mr-2 mt-0.5 text-green-500 flex-shrink-0" />
                        {benefit}
                      </li>
                    ))}
                  </ul>
                </div>

                {(isHovered || isSelected) && (
                  <div className="animate-in slide-in-from-top-2 duration-200">
                    <h4 className="font-semibold text-sm mb-2">Included Features:</h4>
                    <ul className="space-y-1">
                      {journey.features.slice(0, 3).map((feature, index) => (
                        <li key={index} className="text-xs text-muted-foreground flex items-start">
                          <div className="w-1 h-1 bg-current rounded-full mr-2 mt-1.5 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {selectedType && (
        <div className="flex justify-center animate-in slide-in-from-bottom-2 duration-300">
          <Button onClick={onContinue} size="lg" className="px-8">
            Continue with {financeJourneys.find(j => j.type === selectedType)?.title}
          </Button>
        </div>
      )}

      <div className="text-center text-sm text-muted-foreground">
        <p>Don't worry - you can always change your selection later in settings.</p>
      </div>
    </div>
  );
}