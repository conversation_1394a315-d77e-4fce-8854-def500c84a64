"use client";

import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/shared/components/ui/card";
import { Button } from "@/shared/components/ui/button";
import { Input } from "@/shared/components/ui/input";
import { Label } from "@/shared/components/ui/label";
import { Textarea } from "@/shared/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/shared/components/ui/select";
import { Checkbox } from "@/shared/components/ui/checkbox";
import { Progress } from "@/shared/components/ui/progress";
import {
  CheckCircle,
  ArrowRight,
  ArrowLeft,
  CreditCard,
  Users,
  Settings,
  Target,
  DollarSign,
} from "lucide-react";
import { OnboardingStep } from "../types";
import { useBetaFinanceType } from "../contexts/BetaFinanceTypeContext";
import { FinanceJourneySelector } from "./FinanceJourneySelector";
import { useToast } from "@/shared/hooks/use-toast";

interface BetaOnboardingFlowProps {
  onComplete: () => void;
}

interface OnboardingData {
  journeySelection: {
    financeType: string;
  };
  accountSetup: {
    accountName: string;
    accountType: string;
    initialBalance: string;
    currency: string;
  };
  familySetup: {
    groupName: string;
    memberEmails: string[];
    groupDescription: string;
  };
  preferences: {
    currency: string;
    budgetNotifications: boolean;
    goalNotifications: boolean;
    weeklyReports: boolean;
    monthlyReports: boolean;
    defaultBudgetPeriod: string;
  };
}

const onboardingSteps: OnboardingStep[] = [
  {
    id: "journey-selection",
    title: "Choose Your Journey",
    description: "Select the financial experience that best fits your needs",
    component: "FinanceJourneySelector",
    financeTypes: ["personal", "family", "combined"],
    required: true,
  },
  {
    id: "welcome",
    title: "Welcome to Beta",
    description: "Learn about the new features and how to provide feedback",
    component: "WelcomeStep",
    financeTypes: ["personal", "family", "combined"],
    required: true,
  },
  {
    id: "account-setup",
    title: "Account Setup",
    description: "Add your first account to start tracking finances",
    component: "AccountSetup",
    financeTypes: ["personal", "combined"],
    required: true,
  },
  {
    id: "family-setup",
    title: "Family Group Setup",
    description:
      "Create or join a family group for collaborative financial management",
    component: "FamilySetup",
    financeTypes: ["family", "combined"],
    required: true,
  },
  {
    id: "preferences",
    title: "Preferences",
    description: "Customize your dashboard and notification settings",
    component: "PreferencesStep",
    financeTypes: ["personal", "family", "combined"],
    required: false,
  },
];

export function BetaOnboardingFlow({ onComplete }: BetaOnboardingFlowProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<string[]>([]);
  const { financeType, setFinanceType } = useBetaFinanceType();
  const { toast } = useToast();

  // Onboarding data state
  const [onboardingData, setOnboardingData] = useState<OnboardingData>({
    journeySelection: {
      financeType: financeType,
    },
    accountSetup: {
      accountName: "",
      accountType: "",
      initialBalance: "",
      currency: "USD",
    },
    familySetup: {
      groupName: "",
      memberEmails: [""],
      groupDescription: "",
    },
    preferences: {
      currency: "USD",
      budgetNotifications: true,
      goalNotifications: true,
      weeklyReports: false,
      monthlyReports: true,
      defaultBudgetPeriod: "monthly",
    },
  });

  // Load data from localStorage on mount
  useEffect(() => {
    const savedData = localStorage.getItem("beta-onboarding-data");
    if (savedData) {
      try {
        const parsed = JSON.parse(savedData);
        setOnboardingData(parsed);
      } catch (error) {
        console.error("Failed to load onboarding data:", error);
      }
    }

    const savedStep = localStorage.getItem("beta-onboarding-step");
    if (savedStep) {
      setCurrentStep(parseInt(savedStep, 10));
    }

    const savedCompleted = localStorage.getItem("beta-onboarding-completed");
    if (savedCompleted) {
      try {
        setCompletedSteps(JSON.parse(savedCompleted));
      } catch (error) {
        console.error("Failed to load completed steps:", error);
      }
    }
  }, []);

  // Save data to localStorage
  const saveToLocalStorage = () => {
    localStorage.setItem(
      "beta-onboarding-data",
      JSON.stringify(onboardingData)
    );
    localStorage.setItem("beta-onboarding-step", currentStep.toString());
    localStorage.setItem(
      "beta-onboarding-completed",
      JSON.stringify(completedSteps)
    );
  };

  const availableSteps = onboardingSteps.filter((step) =>
    step.financeTypes.includes(financeType)
  );
  const progress = (currentStep / availableSteps.length) * 100;

  const updateOnboardingData = (section: keyof OnboardingData, data: any) => {
    setOnboardingData((prev) => ({
      ...prev,
      [section]: { ...prev[section], ...data },
    }));
  };

  const handleStepComplete = (stepId: string) => {
    if (!completedSteps.includes(stepId)) {
      const newCompleted = [...completedSteps, stepId];
      setCompletedSteps(newCompleted);
      saveToLocalStorage();
    }
  };

  const validateCurrentStep = (): boolean => {
    const currentStepData = availableSteps[currentStep];

    switch (currentStepData.component) {
      case "AccountSetup":
        return !!(
          onboardingData.accountSetup.accountName &&
          onboardingData.accountSetup.accountType &&
          onboardingData.accountSetup.initialBalance
        );
      case "FamilySetup":
        return !!onboardingData.familySetup.groupName;
      default:
        return true;
    }
  };

  const handleNext = () => {
    const currentStepData = availableSteps[currentStep];

    if (currentStepData.required && !validateCurrentStep()) {
      toast({
        title: "Please complete all required fields",
        description: "Fill in the required information before proceeding.",
        variant: "destructive",
      });
      return;
    }

    handleStepComplete(currentStepData.id);
    saveToLocalStorage();

    if (currentStep < availableSteps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      // Save final data and complete onboarding
      localStorage.setItem(
        "beta-onboarding-final-data",
        JSON.stringify(onboardingData)
      );
      localStorage.setItem(
        "beta-onboarding-completed-at",
        new Date().toISOString()
      );
      onComplete();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
      saveToLocalStorage();
    }
  };

  const currentStepData = availableSteps[currentStep];

  // Family setup handlers
  const addMemberEmail = () => {
    updateOnboardingData("familySetup", {
      memberEmails: [...onboardingData.familySetup.memberEmails, ""],
    });
  };

  const updateMemberEmail = (index: number, email: string) => {
    const newEmails = [...onboardingData.familySetup.memberEmails];
    newEmails[index] = email;
    updateOnboardingData("familySetup", { memberEmails: newEmails });
  };

  const removeMemberEmail = (index: number) => {
    if (onboardingData.familySetup.memberEmails.length > 1) {
      const newEmails = onboardingData.familySetup.memberEmails.filter(
        (_, i) => i !== index
      );
      updateOnboardingData("familySetup", { memberEmails: newEmails });
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Progress Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-2xl font-bold">Beta Program Onboarding</h1>
          <div className="text-sm text-muted-foreground">
            Step {currentStep + 1} of {availableSteps.length}
          </div>
        </div>
        <Progress value={progress} className="h-2" />
      </div>

      {/* Step Navigation */}
      <div className="flex items-center justify-center mb-8 space-x-4">
        {availableSteps.map((step, index) => {
          const isCompleted = completedSteps.includes(step.id);
          const isCurrent = index === currentStep;

          return (
            <div
              key={step.id}
              className={`flex items-center ${
                index < availableSteps.length - 1 ? "mr-4" : ""
              }`}
            >
              <div
                className={`flex items-center justify-center w-8 h-8 rounded-full border-2 transition-colors ${
                  isCompleted
                    ? "bg-green-500 border-green-500 text-white"
                    : isCurrent
                    ? "border-primary bg-primary text-primary-foreground"
                    : "border-muted-foreground/30 text-muted-foreground"
                }`}
              >
                {isCompleted ? (
                  <CheckCircle className="w-5 h-5" />
                ) : (
                  <span className="text-sm font-medium">{index + 1}</span>
                )}
              </div>
              <div
                className={`ml-2 text-sm ${
                  isCurrent ? "font-medium" : "text-muted-foreground"
                }`}
              >
                {step.title}
              </div>
              {index < availableSteps.length - 1 && (
                <ArrowRight className="w-4 h-4 ml-4 text-muted-foreground" />
              )}
            </div>
          );
        })}
      </div>

      {/* Step Content */}
      <Card className="min-h-[400px]">
        <CardHeader>
          <CardTitle>{currentStepData.title}</CardTitle>
          <CardDescription>{currentStepData.description}</CardDescription>
        </CardHeader>
        <CardContent>
          {currentStepData.component === "FinanceJourneySelector" && (
            <div className="space-y-6">
              <FinanceJourneySelector
                selectedType={financeType}
                onSelect={(type) => {
                  setFinanceType(type);
                  updateOnboardingData("journeySelection", {
                    financeType: type,
                  });
                  saveToLocalStorage();
                }}
                onContinue={handleNext}
              />
            </div>
          )}

          {currentStepData.component === "WelcomeStep" && (
            <div className="space-y-6 text-center">
              <div className="space-y-4">
                <h3 className="text-xl font-semibold">
                  Welcome to the Beta Program!
                </h3>
                <p className="text-muted-foreground max-w-2xl mx-auto">
                  You&apos;re now part of an exclusive group testing the future
                  of financial management. Your feedback will help shape the
                  final product.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl mx-auto">
                <div className="text-left space-y-2">
                  <h4 className="font-semibold">🎯 What to Expect</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• New features and improvements</li>
                    <li>• Regular updates and enhancements</li>
                    <li>• Direct communication with our team</li>
                  </ul>
                </div>
                <div className="text-left space-y-2">
                  <h4 className="font-semibold">💬 How to Help</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Share feedback regularly</li>
                    <li>• Report any bugs you encounter</li>
                    <li>• Suggest new features</li>
                  </ul>
                </div>
              </div>
            </div>
          )}

          {currentStepData.component === "AccountSetup" && (
            <div className="space-y-6">
              <div className="flex items-center gap-3 mb-4">
                <CreditCard className="h-6 w-6 text-blue-500" />
                <h3 className="text-xl font-semibold">
                  Set Up Your First Account
                </h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="accountName">Account Name *</Label>
                    <Input
                      id="accountName"
                      placeholder="e.g., Main Checking"
                      value={onboardingData.accountSetup.accountName}
                      onChange={(e) =>
                        updateOnboardingData("accountSetup", {
                          accountName: e.target.value,
                        })
                      }
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="accountType">Account Type *</Label>
                    <Select
                      value={onboardingData.accountSetup.accountType}
                      onValueChange={(value) =>
                        updateOnboardingData("accountSetup", {
                          accountType: value,
                        })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select account type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="checking">Checking</SelectItem>
                        <SelectItem value="savings">Savings</SelectItem>
                        <SelectItem value="credit">Credit Card</SelectItem>
                        <SelectItem value="investment">Investment</SelectItem>
                        <SelectItem value="cash">Cash</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="initialBalance">Initial Balance *</Label>
                    <div className="relative">
                      <DollarSign className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                      <Input
                        id="initialBalance"
                        type="number"
                        placeholder="0.00"
                        className="pl-10"
                        value={onboardingData.accountSetup.initialBalance}
                        onChange={(e) =>
                          updateOnboardingData("accountSetup", {
                            initialBalance: e.target.value,
                          })
                        }
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="currency">Currency</Label>
                    <Select
                      value={onboardingData.accountSetup.currency}
                      onValueChange={(value) =>
                        updateOnboardingData("accountSetup", {
                          currency: value,
                        })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="USD">USD - US Dollar</SelectItem>
                        <SelectItem value="EUR">EUR - Euro</SelectItem>
                        <SelectItem value="GBP">GBP - British Pound</SelectItem>
                        <SelectItem value="CAD">
                          CAD - Canadian Dollar
                        </SelectItem>
                        <SelectItem value="AUD">
                          AUD - Australian Dollar
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              <div className="bg-blue-50 p-4 rounded-lg">
                <p className="text-sm text-blue-700">
                  💡 Don&apos;t worry - you can add more accounts and modify
                  these details later in your dashboard.
                </p>
              </div>
            </div>
          )}

          {currentStepData.component === "FamilySetup" && (
            <div className="space-y-6">
              <div className="flex items-center gap-3 mb-4">
                <Users className="h-6 w-6 text-green-500" />
                <h3 className="text-xl font-semibold">
                  Create Your Family Group
                </h3>
              </div>

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="groupName">Family Group Name *</Label>
                  <Input
                    id="groupName"
                    placeholder="e.g., The Smith Family"
                    value={onboardingData.familySetup.groupName}
                    onChange={(e) =>
                      updateOnboardingData("familySetup", {
                        groupName: e.target.value,
                      })
                    }
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="groupDescription">
                    Description (Optional)
                  </Label>
                  <Textarea
                    id="groupDescription"
                    placeholder="Brief description of your family financial goals..."
                    value={onboardingData.familySetup.groupDescription}
                    onChange={(e) =>
                      updateOnboardingData("familySetup", {
                        groupDescription: e.target.value,
                      })
                    }
                  />
                </div>

                <div className="space-y-2">
                  <Label>Invite Family Members (Optional)</Label>
                  <div className="space-y-2">
                    {onboardingData.familySetup.memberEmails.map(
                      (email, index) => (
                        <div key={index} className="flex gap-2">
                          <Input
                            type="email"
                            placeholder="<EMAIL>"
                            value={email}
                            onChange={(e) =>
                              updateMemberEmail(index, e.target.value)
                            }
                          />
                          {onboardingData.familySetup.memberEmails.length >
                            1 && (
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => removeMemberEmail(index)}
                            >
                              Remove
                            </Button>
                          )}
                        </div>
                      )
                    )}
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={addMemberEmail}
                      className="w-full"
                    >
                      Add Another Member
                    </Button>
                  </div>
                </div>
              </div>

              <div className="bg-green-50 p-4 rounded-lg">
                <p className="text-sm text-green-700">
                  🎯 You can invite members now or skip this step and add them
                  later from your dashboard.
                </p>
              </div>
            </div>
          )}

          {currentStepData.component === "PreferencesStep" && (
            <div className="space-y-6">
              <div className="flex items-center gap-3 mb-4">
                <Settings className="h-6 w-6 text-purple-500" />
                <h3 className="text-xl font-semibold">
                  Customize Your Experience
                </h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="prefCurrency">Default Currency</Label>
                    <Select
                      value={onboardingData.preferences.currency}
                      onValueChange={(value) =>
                        updateOnboardingData("preferences", { currency: value })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="USD">USD - US Dollar</SelectItem>
                        <SelectItem value="EUR">EUR - Euro</SelectItem>
                        <SelectItem value="GBP">GBP - British Pound</SelectItem>
                        <SelectItem value="CAD">
                          CAD - Canadian Dollar
                        </SelectItem>
                        <SelectItem value="AUD">
                          AUD - Australian Dollar
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="budgetPeriod">Default Budget Period</Label>
                    <Select
                      value={onboardingData.preferences.defaultBudgetPeriod}
                      onValueChange={(value) =>
                        updateOnboardingData("preferences", {
                          defaultBudgetPeriod: value,
                        })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="weekly">Weekly</SelectItem>
                        <SelectItem value="monthly">Monthly</SelectItem>
                        <SelectItem value="quarterly">Quarterly</SelectItem>
                        <SelectItem value="yearly">Yearly</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="space-y-3">
                    <Label>Notification Preferences</Label>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="budgetNotifications"
                          checked={
                            onboardingData.preferences.budgetNotifications
                          }
                          onCheckedChange={(checked) =>
                            updateOnboardingData("preferences", {
                              budgetNotifications: checked,
                            })
                          }
                        />
                        <Label
                          htmlFor="budgetNotifications"
                          className="text-sm"
                        >
                          Budget alerts and overspending warnings
                        </Label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="goalNotifications"
                          checked={onboardingData.preferences.goalNotifications}
                          onCheckedChange={(checked) =>
                            updateOnboardingData("preferences", {
                              goalNotifications: checked,
                            })
                          }
                        />
                        <Label htmlFor="goalNotifications" className="text-sm">
                          Goal progress updates and milestones
                        </Label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="weeklyReports"
                          checked={onboardingData.preferences.weeklyReports}
                          onCheckedChange={(checked) =>
                            updateOnboardingData("preferences", {
                              weeklyReports: checked,
                            })
                          }
                        />
                        <Label htmlFor="weeklyReports" className="text-sm">
                          Weekly financial reports
                        </Label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="monthlyReports"
                          checked={onboardingData.preferences.monthlyReports}
                          onCheckedChange={(checked) =>
                            updateOnboardingData("preferences", {
                              monthlyReports: checked,
                            })
                          }
                        />
                        <Label htmlFor="monthlyReports" className="text-sm">
                          Monthly financial reports
                        </Label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-purple-50 p-4 rounded-lg">
                <p className="text-sm text-purple-700">
                  ⚙️ You can change these preferences anytime in your settings
                  dashboard.
                </p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Navigation Buttons */}
      {currentStepData.component !== "FinanceJourneySelector" && (
        <div className="flex justify-between mt-8">
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={currentStep === 0}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Previous
          </Button>

          <Button onClick={handleNext}>
            {currentStep === availableSteps.length - 1
              ? "Complete Setup"
              : "Next"}
            {currentStep < availableSteps.length - 1 && (
              <ArrowRight className="w-4 h-4 ml-2" />
            )}
          </Button>
        </div>
      )}

      {/* Debug Panel - Only show in development */}
      {process.env.NODE_ENV === "development" && (
        <Card className="mt-8 border-yellow-200 bg-yellow-50">
          <CardHeader>
            <CardTitle className="text-sm text-yellow-800">
              Development Debug
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-xs text-yellow-700">
              <p className="font-semibold mb-2">LocalStorage Data:</p>
              <div className="space-y-1">
                <p>Current Step: {currentStep}</p>
                <p>Finance Type: {financeType}</p>
                <p>Completed Steps: {completedSteps.join(", ")}</p>
                <details className="mt-2">
                  <summary className="cursor-pointer font-medium">
                    Onboarding Data
                  </summary>
                  <pre className="mt-1 text-xs bg-yellow-100 p-2 rounded overflow-auto">
                    {JSON.stringify(onboardingData, null, 2)}
                  </pre>
                </details>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    localStorage.removeItem("beta-onboarding-data");
                    localStorage.removeItem("beta-onboarding-step");
                    localStorage.removeItem("beta-onboarding-completed");
                    localStorage.removeItem("beta-onboarding-final-data");
                    localStorage.removeItem("beta-onboarding-completed-at");
                    toast({
                      title: "Storage cleared",
                      description:
                        "All onboarding data has been cleared from localStorage.",
                    });
                    window.location.reload();
                  }}
                  className="mt-2"
                >
                  Clear Storage & Reset
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
