// Beta program types

export type FinanceType = 'personal' | 'family' | 'combined';

export interface BetaUser {
  id: string;
  email?: string | null;
  full_name?: string | null;
  username?: string | null;
  avatar_url?: string | null;
  currency: string;
  finance_type: FinanceType;
  beta_user: boolean;
  beta_joined_at?: string;
  beta_feedback_count: number;
  onboarding_completed: boolean;
  enabled_features: Record<string, boolean>;
  dashboard_preference: 'personal' | 'family' | 'combined' | 'auto';
  created_at: string;
  updated_at: string;
}

export interface BetaFeedback {
  id: string;
  user_id: string;
  feedback_type: 'bug' | 'feature_request' | 'general' | 'rating';
  content?: string;
  rating?: number;
  created_at: string;
}

export interface FinanceTypeCapabilities {
  canAccessPersonal: boolean;
  canAccessFamily: boolean;
  canCreateFamilyGroups: boolean;
  canSwitchModes: boolean;
  showModeToggle: boolean;
  simplified: boolean;
  collaborationMode: boolean;
}

export interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  component: string;
  financeTypes: FinanceType[];
  required: boolean;
  completed?: boolean;
}

export interface FinanceJourney {
  type: FinanceType;
  title: string;
  description: string;
  benefits: string[];
  features: string[];
  iconName: string;
  color: string;
  recommended?: boolean;
}