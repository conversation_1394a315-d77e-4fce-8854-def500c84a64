-- WARNING: This script will delete all data from the specified tables.
-- <PERSON><PERSON><PERSON>E YOU HAVE A BACKUP BEFORE RUNNING THIS.

DO $$
DECLARE
    table_record RECORD;
BEGIN
    -- Truncate tables in the 'public' schema
    RAISE NOTICE 'Starting truncation of tables in public schema...';
    FOR table_record IN
        SELECT tablename
        FROM pg_tables
        WHERE schemaname = 'public'
          AND tablename NOT LIKE 'pg_%'       -- Exclude PostgreSQL system tables
          AND tablename NOT LIKE 'sql_%'      -- Exclude SQL standard system tables
          AND tablename <> 'schema_migrations' -- Keep migration history unless a full schema reset is intended
                                              -- and you plan to drop/recreate schema objects.
                                              -- For just clearing data, keeping this is fine.
    LOOP
        RAISE NOTICE 'Truncating public.% ...', table_record.tablename;
        EXECUTE 'TRUNCATE TABLE public.' || quote_ident(table_record.tablename) || ' RESTART IDENTITY CASCADE;';
    END LOOP;
    RAISE NOTICE 'Finished truncating tables in public schema.';

    -- Truncate key tables in the 'auth' schema
    -- This will delete all users, sessions, identities, etc.
    RAISE NOTICE 'Starting truncation of key tables in auth schema...';
    FOR table_record IN
        SELECT tablename
        FROM pg_tables
        WHERE schemaname = 'auth'
          AND tablename IN (
            'users',
            'identities',
            'sessions',
            'refresh_tokens',
            'mfa_factors',
            'mfa_challenges',
            'saml_providers',
            'saml_relay_states',
            'sso_providers',
            'sso_domains',
            'audit_log_entries' -- Clears authentication audit logs
            -- Note: Other auth tables like 'instances', 'schemas', 'migrations'
            -- are generally internal and should not be truncated directly unless by Supabase support.
          )
    LOOP
        RAISE NOTICE 'Truncating auth.% ...', table_record.tablename;
        EXECUTE 'TRUNCATE TABLE auth.' || quote_ident(table_record.tablename) || ' CASCADE;';
    END LOOP;
    RAISE NOTICE 'Finished truncating key tables in auth schema.';

    RAISE NOTICE 'All specified tables in public and auth schemas have been truncated.';
    RAISE NOTICE 'Remember to clear Supabase Storage manually if needed.';
END $$;