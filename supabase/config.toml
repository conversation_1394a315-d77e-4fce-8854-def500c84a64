# Supabase project configuration
project_id = "ddwkbtkbcnbsabepsfsd"

[api]
port = 54321
schemas = ["public", "storage", "graphql"]
extra_search_path = ["extensions"]
max_rows = 1000

[db]
port = 54322
major_version = 15 # Assuming PostgreSQL 15, common for new Supabase projects

[studio]
port = 54323

[auth]
enabled = true
site_url = "http://localhost:3000"
additional_redirect_urls = []
jwt_expiry = 3600
enable_refresh_token_rotation = true
refresh_token_reuse_interval = 10

[auth.email]
enable_signup = true
double_confirm_changes = true
enable_confirmations = true
secure_password_change = false # Default, can be changed later if needed
max_frequency = "1m0s"
otp_length = 6
otp_expiry = 3600

# To add external OAuth providers, see https://supabase.com/docs/guides/auth/social-login
# Example for GitHub:
# [auth.external.github]
# enabled = false
# client_id = ""
# secret = ""
# redirect_uri = ""

# For more configuration options, see https://supabase.com/docs/reference/cli/config
