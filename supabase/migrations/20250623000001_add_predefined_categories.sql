-- Add predefined categories for new users
-- This migration creates a system to automatically add common categories when users sign up

-- Create a table to store predefined category templates
CREATE TABLE IF NOT EXISTS public.predefined_categories (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('income', 'expense')),
  icon TEXT,
  color TEXT NOT NULL,
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

COMMENT ON TABLE public.predefined_categories IS 'Template categories that are automatically created for new users';

-- Insert predefined expense categories
INSERT INTO public.predefined_categories (name, type, icon, color, sort_order) VALUES
-- Essential Categories
('Groceries', 'expense', 'shopping-cart', '#10b981', 1),
('Transportation', 'expense', 'car', '#3b82f6', 2),
('Utilities', 'expense', 'zap', '#f59e0b', 3),
('Housing', 'expense', 'home', '#ef4444', 4),
('Healthcare', 'expense', 'heart-pulse', '#ec4899', 5),

-- Lifestyle Categories  
('Dining Out', 'expense', 'utensils', '#8b5cf6', 6),
('Entertainment', 'expense', 'clapperboard', '#f97316', 7),
('Shopping', 'expense', 'shopping-bag', '#14b8a6', 8),
('Subscriptions', 'expense', 'smartphone', '#6366f1', 9),
('Personal Care', 'expense', 'sparkles', '#84cc16', 10),

-- Financial Categories
('Insurance', 'expense', 'shield', '#0ea5e9', 11),
('Taxes', 'expense', 'calculator', '#64748b', 12),
('Debt Payment', 'expense', 'credit-card', '#dc2626', 13),

-- Other
('Gifts', 'expense', 'gift', '#f472b6', 14),
('Travel', 'expense', 'plane', '#06b6d4', 15),
('Education', 'expense', 'graduation-cap', '#7c3aed', 16),
('Miscellaneous', 'expense', 'package', '#6b7280', 17);

-- Insert predefined income categories
INSERT INTO public.predefined_categories (name, type, icon, color, sort_order) VALUES
('Salary', 'income', 'briefcase', '#10b981', 1),
('Freelancing', 'income', 'laptop', '#3b82f6', 2),
('Business', 'income', 'building-2', '#f59e0b', 3),
('Investments', 'income', 'trending-up', '#8b5cf6', 4),
('Side Hustle', 'income', 'zap', '#ec4899', 5),
('Rental Income', 'income', 'home', '#14b8a6', 6),
('Interest', 'income', 'landmark', '#6366f1', 7),
('Dividends', 'income', 'coins', '#84cc16', 8),
('Bonus', 'income', 'target', '#f97316', 9),
('Gifts Received', 'income', 'gift', '#ef4444', 10),
('Refunds', 'income', 'undo-2', '#0ea5e9', 11),
('Other Income', 'income', 'banknote', '#6b7280', 12);

-- Function to create default categories for a new user
CREATE OR REPLACE FUNCTION public.create_default_categories_for_user(user_id UUID)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = public
AS $$
BEGIN
  -- Insert all active predefined categories for the new user
  INSERT INTO public.categories (user_id, name, type, icon, color)
  SELECT 
    user_id,
    pc.name,
    pc.type,
    pc.icon,
    pc.color
  FROM public.predefined_categories pc
  WHERE pc.is_active = true
  ORDER BY pc.sort_order;
  
  -- Log that categories were created
  RAISE NOTICE 'Created default categories for user: %', user_id;
END;
$$;

COMMENT ON FUNCTION public.create_default_categories_for_user(UUID) IS 'Creates default categories for a new user based on predefined templates';

-- Update the handle_new_user function to include creating default categories
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = public
AS $$
BEGIN
  -- First, create the user profile
  INSERT INTO public.user_profiles (id, email, full_name, username, avatar_url, currency, language)
  VALUES (
    NEW.id,
    NEW.email,
    NEW.raw_user_meta_data->>'full_name',
    NEW.raw_user_meta_data->>'username',
    NEW.raw_user_meta_data->>'avatar_url',
    COALESCE(NEW.raw_user_meta_data->>'currency', 'USD'),
    COALESCE(NEW.raw_user_meta_data->>'language', 'en')
  )
  ON CONFLICT (id) DO NOTHING;
  
  -- Then, create default categories for the user
  PERFORM public.create_default_categories_for_user(NEW.id);
  
  RETURN NEW;
END;
$$;

COMMENT ON FUNCTION public.handle_new_user() IS 'Handles creating a user profile and default categories upon new user signup';

-- Create an index for better performance on predefined_categories
CREATE INDEX IF NOT EXISTS idx_predefined_categories_active_sort ON public.predefined_categories(is_active, sort_order) WHERE is_active = true;

-- Add RLS to predefined_categories (read-only for all authenticated users)
ALTER TABLE public.predefined_categories ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Authenticated users can view predefined categories" ON public.predefined_categories
  FOR SELECT USING (auth.role() = 'authenticated');

-- Function to get available predefined categories (useful for admin or future features)
CREATE OR REPLACE FUNCTION public.get_predefined_categories()
RETURNS TABLE (
  id UUID,
  name TEXT,
  type TEXT,
  icon TEXT,
  color TEXT,
  sort_order INTEGER
)
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = public
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    pc.id,
    pc.name,
    pc.type,
    pc.icon,
    pc.color,
    pc.sort_order
  FROM public.predefined_categories pc
  WHERE pc.is_active = true
  ORDER BY pc.type, pc.sort_order;
END;
$$;

COMMENT ON FUNCTION public.get_predefined_categories() IS 'Returns all active predefined categories sorted by type and order';