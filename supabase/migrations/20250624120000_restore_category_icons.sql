-- Restore Lucide React icons for predefined categories
-- This migration fixes the issue where all category icons were cleared by the remove_category_emojis migration

-- Restore predefined_categories icons with proper Lucide icon names
UPDATE public.predefined_categories SET icon = 'shopping-cart' WHERE name = 'Groceries' AND type = 'expense';
UPDATE public.predefined_categories SET icon = 'car' WHERE name = 'Transportation' AND type = 'expense';
UPDATE public.predefined_categories SET icon = 'zap' WHERE name = 'Utilities' AND type = 'expense';
UPDATE public.predefined_categories SET icon = 'home' WHERE name = 'Housing' AND type = 'expense';
UPDATE public.predefined_categories SET icon = 'heart-pulse' WHERE name = 'Healthcare' AND type = 'expense';
UPDATE public.predefined_categories SET icon = 'utensils' WHERE name = 'Dining Out' AND type = 'expense';
UPDATE public.predefined_categories SET icon = 'clapperboard' WHERE name = 'Entertainment' AND type = 'expense';
UPDATE public.predefined_categories SET icon = 'shopping-bag' WHERE name = 'Shopping' AND type = 'expense';
UPDATE public.predefined_categories SET icon = 'smartphone' WHERE name = 'Subscriptions' AND type = 'expense';
UPDATE public.predefined_categories SET icon = 'sparkles' WHERE name = 'Personal Care' AND type = 'expense';
UPDATE public.predefined_categories SET icon = 'shield' WHERE name = 'Insurance' AND type = 'expense';
UPDATE public.predefined_categories SET icon = 'calculator' WHERE name = 'Taxes' AND type = 'expense';
UPDATE public.predefined_categories SET icon = 'credit-card' WHERE name = 'Debt Payment' AND type = 'expense';
UPDATE public.predefined_categories SET icon = 'gift' WHERE name = 'Gifts' AND type = 'expense';
UPDATE public.predefined_categories SET icon = 'plane' WHERE name = 'Travel' AND type = 'expense';
UPDATE public.predefined_categories SET icon = 'graduation-cap' WHERE name = 'Education' AND type = 'expense';
UPDATE public.predefined_categories SET icon = 'package' WHERE name = 'Miscellaneous' AND type = 'expense';

-- Restore income category icons
UPDATE public.predefined_categories SET icon = 'briefcase' WHERE name = 'Salary' AND type = 'income';
UPDATE public.predefined_categories SET icon = 'laptop' WHERE name = 'Freelancing' AND type = 'income';
UPDATE public.predefined_categories SET icon = 'building-2' WHERE name = 'Business' AND type = 'income';
UPDATE public.predefined_categories SET icon = 'trending-up' WHERE name = 'Investments' AND type = 'income';
UPDATE public.predefined_categories SET icon = 'zap' WHERE name = 'Side Hustle' AND type = 'income';
UPDATE public.predefined_categories SET icon = 'home' WHERE name = 'Rental Income' AND type = 'income';
UPDATE public.predefined_categories SET icon = 'landmark' WHERE name = 'Interest' AND type = 'income';
UPDATE public.predefined_categories SET icon = 'coins' WHERE name = 'Dividends' AND type = 'income';
UPDATE public.predefined_categories SET icon = 'target' WHERE name = 'Bonus' AND type = 'income';
UPDATE public.predefined_categories SET icon = 'gift' WHERE name = 'Gifts Received' AND type = 'income';
UPDATE public.predefined_categories SET icon = 'undo-2' WHERE name = 'Refunds' AND type = 'income';
UPDATE public.predefined_categories SET icon = 'banknote' WHERE name = 'Other Income' AND type = 'income';

-- Update existing user categories that were created from predefined templates
-- Match by name and type to restore appropriate icons
UPDATE public.categories SET icon = 'shopping-cart' WHERE name = 'Groceries' AND type = 'expense' AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'car' WHERE name = 'Transportation' AND type = 'expense' AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'zap' WHERE name = 'Utilities' AND type = 'expense' AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'home' WHERE name = 'Housing' AND type = 'expense' AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'heart-pulse' WHERE name = 'Healthcare' AND type = 'expense' AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'utensils' WHERE name = 'Dining Out' AND type = 'expense' AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'clapperboard' WHERE name = 'Entertainment' AND type = 'expense' AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'shopping-bag' WHERE name = 'Shopping' AND type = 'expense' AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'smartphone' WHERE name = 'Subscriptions' AND type = 'expense' AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'sparkles' WHERE name = 'Personal Care' AND type = 'expense' AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'shield' WHERE name = 'Insurance' AND type = 'expense' AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'calculator' WHERE name = 'Taxes' AND type = 'expense' AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'credit-card' WHERE name = 'Debt Payment' AND type = 'expense' AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'gift' WHERE name = 'Gifts' AND type = 'expense' AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'plane' WHERE name = 'Travel' AND type = 'expense' AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'graduation-cap' WHERE name = 'Education' AND type = 'expense' AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'package' WHERE name = 'Miscellaneous' AND type = 'expense' AND (icon IS NULL OR icon = '');

-- Update income categories
UPDATE public.categories SET icon = 'briefcase' WHERE name = 'Salary' AND type = 'income' AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'laptop' WHERE name = 'Freelancing' AND type = 'income' AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'building-2' WHERE name = 'Business' AND type = 'income' AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'trending-up' WHERE name = 'Investments' AND type = 'income' AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'zap' WHERE name = 'Side Hustle' AND type = 'income' AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'home' WHERE name = 'Rental Income' AND type = 'income' AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'landmark' WHERE name = 'Interest' AND type = 'income' AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'coins' WHERE name = 'Dividends' AND type = 'income' AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'target' WHERE name = 'Bonus' AND type = 'income' AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'gift' WHERE name = 'Gifts Received' AND type = 'income' AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'undo-2' WHERE name = 'Refunds' AND type = 'income' AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'banknote' WHERE name = 'Other Income' AND type = 'income' AND (icon IS NULL OR icon = '');

-- Also update family group categories with same logic
UPDATE public.categories SET icon = 'shopping-cart' WHERE name = 'Groceries' AND type = 'expense' AND family_group_id IS NOT NULL AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'car' WHERE name = 'Transportation' AND type = 'expense' AND family_group_id IS NOT NULL AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'zap' WHERE name = 'Utilities' AND type = 'expense' AND family_group_id IS NOT NULL AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'home' WHERE name = 'Housing' AND type = 'expense' AND family_group_id IS NOT NULL AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'heart-pulse' WHERE name = 'Healthcare' AND type = 'expense' AND family_group_id IS NOT NULL AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'utensils' WHERE name = 'Dining Out' AND type = 'expense' AND family_group_id IS NOT NULL AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'clapperboard' WHERE name = 'Entertainment' AND type = 'expense' AND family_group_id IS NOT NULL AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'shopping-bag' WHERE name = 'Shopping' AND type = 'expense' AND family_group_id IS NOT NULL AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'smartphone' WHERE name = 'Subscriptions' AND type = 'expense' AND family_group_id IS NOT NULL AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'sparkles' WHERE name = 'Personal Care' AND type = 'expense' AND family_group_id IS NOT NULL AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'shield' WHERE name = 'Insurance' AND type = 'expense' AND family_group_id IS NOT NULL AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'calculator' WHERE name = 'Taxes' AND type = 'expense' AND family_group_id IS NOT NULL AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'credit-card' WHERE name = 'Debt Payment' AND type = 'expense' AND family_group_id IS NOT NULL AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'gift' WHERE name = 'Gifts' AND type = 'expense' AND family_group_id IS NOT NULL AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'plane' WHERE name = 'Travel' AND type = 'expense' AND family_group_id IS NOT NULL AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'graduation-cap' WHERE name = 'Education' AND type = 'expense' AND family_group_id IS NOT NULL AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'package' WHERE name = 'Miscellaneous' AND type = 'expense' AND family_group_id IS NOT NULL AND (icon IS NULL OR icon = '');

-- Family group income categories  
UPDATE public.categories SET icon = 'briefcase' WHERE name = 'Salary' AND type = 'income' AND family_group_id IS NOT NULL AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'laptop' WHERE name = 'Freelancing' AND type = 'income' AND family_group_id IS NOT NULL AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'building-2' WHERE name = 'Business' AND type = 'income' AND family_group_id IS NOT NULL AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'trending-up' WHERE name = 'Investments' AND type = 'income' AND family_group_id IS NOT NULL AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'zap' WHERE name = 'Side Hustle' AND type = 'income' AND family_group_id IS NOT NULL AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'home' WHERE name = 'Rental Income' AND type = 'income' AND family_group_id IS NOT NULL AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'landmark' WHERE name = 'Interest' AND type = 'income' AND family_group_id IS NOT NULL AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'coins' WHERE name = 'Dividends' AND type = 'income' AND family_group_id IS NOT NULL AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'target' WHERE name = 'Bonus' AND type = 'income' AND family_group_id IS NOT NULL AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'gift' WHERE name = 'Gifts Received' AND type = 'income' AND family_group_id IS NOT NULL AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'undo-2' WHERE name = 'Refunds' AND type = 'income' AND family_group_id IS NOT NULL AND (icon IS NULL OR icon = '');
UPDATE public.categories SET icon = 'banknote' WHERE name = 'Other Income' AND type = 'income' AND family_group_id IS NOT NULL AND (icon IS NULL OR icon = '');

-- Log the restoration
DO $$
DECLARE
  predefined_count INTEGER;
  user_count INTEGER;
  family_count INTEGER;
BEGIN
  -- Count updated records
  SELECT COUNT(*) INTO predefined_count FROM public.predefined_categories WHERE icon IS NOT NULL AND icon != '';
  SELECT COUNT(*) INTO user_count FROM public.categories WHERE icon IS NOT NULL AND icon != '' AND family_group_id IS NULL;
  SELECT COUNT(*) INTO family_count FROM public.categories WHERE icon IS NOT NULL AND icon != '' AND family_group_id IS NOT NULL;
  
  RAISE NOTICE 'Restored Lucide icons: % predefined categories, % user categories, % family categories', 
    predefined_count, user_count, family_count;
END
$$;