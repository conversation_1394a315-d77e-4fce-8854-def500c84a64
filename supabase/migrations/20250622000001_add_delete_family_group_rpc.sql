-- Add delete_family_group RPC function
-- This function allows a family group admin to completely delete a family group and all associated data

CREATE OR REPLACE FUNCTION delete_family_group(p_group_id UUID)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
    v_current_user_id UUID;
    v_is_admin BOOLEAN;
    v_group_exists BOOLEAN;
BEGIN
    -- Get the current authenticated user ID
    v_current_user_id := auth.uid();
    
    -- Check if user is authenticated
    IF v_current_user_id IS NULL THEN
        RAISE EXCEPTION 'Authentication required';
    END IF;
    
    -- Validate that group_id is provided
    IF p_group_id IS NULL THEN
        RAISE EXCEPTION 'Group ID is required';
    END IF;
    
    -- Check if the group exists
    SELECT TRUE INTO v_group_exists
    FROM family_groups
    WHERE id = p_group_id;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Family group not found';
    END IF;
    
    -- Check if the current user is an admin of the group
    SELECT is_family_group_admin(p_group_id, v_current_user_id) INTO v_is_admin;
    
    IF NOT v_is_admin THEN
        RAISE EXCEPTION 'Only group administrators can delete the family group';
    END IF;
    
    -- Start the deletion process
    -- Delete in order to handle foreign key constraints properly
    
    -- 1. Delete family group invitation audit logs
    DELETE FROM family_group_invitation_audit
    WHERE invitation_id IN (
        SELECT id FROM family_group_invitations 
        WHERE group_id = p_group_id
    );
    
    -- 2. Delete family group invitations
    DELETE FROM family_group_invitations
    WHERE group_id = p_group_id;
    
    -- 3. Delete financial data associated with the group
    -- Delete transactions first (they reference accounts, categories, budgets, goals)
    DELETE FROM transactions
    WHERE group_id = p_group_id;
    
    -- Delete accounts
    DELETE FROM accounts
    WHERE group_id = p_group_id;
    
    -- Delete budgets
    DELETE FROM budgets
    WHERE group_id = p_group_id;
    
    -- Delete financial goals
    DELETE FROM financial_goals
    WHERE group_id = p_group_id;
    
    -- Delete categories
    DELETE FROM categories
    WHERE group_id = p_group_id;
    
    -- 4. Delete family group members
    DELETE FROM family_group_members
    WHERE group_id = p_group_id;
    
    -- 5. Finally, delete the family group itself
    DELETE FROM family_groups
    WHERE id = p_group_id;
    
EXCEPTION
    WHEN OTHERS THEN
        -- Log the error and re-raise with a user-friendly message
        RAISE EXCEPTION 'Failed to delete family group: %', SQLERRM;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION delete_family_group(UUID) TO authenticated;

-- Add helpful comment
COMMENT ON FUNCTION delete_family_group(UUID) IS 'Allows a family group admin to completely delete a family group and all associated data including members, invitations, and financial records';