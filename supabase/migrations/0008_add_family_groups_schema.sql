-- Migration: Create schema for Family Groups feature

-- Table: family_groups
-- Stores information about each family group.
CREATE TABLE IF NOT EXISTS public.family_groups (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    owner_user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE RESTRICT, -- Prevent deleting a user if they own a group without explicit handling
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT now() NOT NULL
);

-- Add comments to family_groups table and columns
COMMENT ON TABLE public.family_groups IS 'Stores information about each family group.';
COMMENT ON COLUMN public.family_groups.id IS 'Unique identifier for the family group.';
COMMENT ON COLUMN public.family_groups.name IS 'Name of the family group (e.g., "The Millers Budget").';
COMMENT ON COLUMN public.family_groups.owner_user_id IS 'The user_id of the user who created and owns the group.';
COMMENT ON COLUMN public.family_groups.created_at IS 'Timestamp of when the group was created.';
COMMENT ON COLUMN public.family_groups.updated_at IS 'Timestamp of when the group was last updated.';

-- Table: family_group_members
-- Stores the members of each family group and their roles.
CREATE TABLE IF NOT EXISTS public.family_group_members (
    group_id UUID NOT NULL REFERENCES public.family_groups(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    role TEXT NOT NULL DEFAULT 'member', -- Default role 'member'. Can be extended later.
    joined_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    PRIMARY KEY (group_id, user_id) -- Composite primary key
);

-- Add comments to family_group_members table and columns
COMMENT ON TABLE public.family_group_members IS 'Stores the members of each family group and their roles.';
COMMENT ON COLUMN public.family_group_members.group_id IS 'Foreign key referencing the family group.';
COMMENT ON COLUMN public.family_group_members.user_id IS 'Foreign key referencing the user who is a member.';
COMMENT ON COLUMN public.family_group_members.role IS 'Role of the user within the group (e.g., admin).';
COMMENT ON COLUMN public.family_group_members.joined_at IS 'Timestamp of when the user joined the group.';

-- Table: family_group_invitations
-- Stores invitations sent to users to join family groups.
CREATE TABLE IF NOT EXISTS public.family_group_invitations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    group_id UUID NOT NULL REFERENCES public.family_groups(id) ON DELETE CASCADE,
    invited_by_user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE NO ACTION, -- If inviter is deleted, invitation can still exist
    invitee_email TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending', -- e.g., 'pending', 'accepted', 'declined', 'expired'
    token TEXT UNIQUE NOT NULL, -- Secure token for the invitation link
    expires_at TIMESTAMPTZ NOT NULL, -- e.g., 7 days from creation
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL
);

-- Add comments to family_group_invitations table and columns
COMMENT ON TABLE public.family_group_invitations IS 'Stores invitations sent to users to join family groups.';
COMMENT ON COLUMN public.family_group_invitations.id IS 'Unique identifier for the invitation.';
COMMENT ON COLUMN public.family_group_invitations.group_id IS 'Foreign key referencing the group for which the invitation is made.';
COMMENT ON COLUMN public.family_group_invitations.invited_by_user_id IS 'Foreign key referencing the user who sent the invitation.';
COMMENT ON COLUMN public.family_group_invitations.invitee_email IS 'Email address of the user being invited.';
COMMENT ON COLUMN public.family_group_invitations.status IS 'Current status of the invitation (pending, accepted, declined, expired).';
COMMENT ON COLUMN public.family_group_invitations.token IS 'Secure, unique token for the invitation link.';
COMMENT ON COLUMN public.family_group_invitations.expires_at IS 'Timestamp when the invitation expires.';
COMMENT ON COLUMN public.family_group_invitations.created_at IS 'Timestamp of when the invitation was created.';

-- Enable RLS for the new tables (policies will be defined in a subsequent step/migration)
ALTER TABLE public.family_groups ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.family_group_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.family_group_invitations ENABLE ROW LEVEL SECURITY;

-- Function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger for family_groups updated_at
CREATE TRIGGER handle_family_groups_updated_at
BEFORE UPDATE ON public.family_groups
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();
