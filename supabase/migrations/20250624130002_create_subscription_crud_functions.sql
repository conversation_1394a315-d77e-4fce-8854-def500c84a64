-- Create subscription CRUD RPC functions
-- This migration creates functions for creating, updating, and deleting subscriptions

-- Function to create a new subscription
CREATE OR REPLACE FUNCTION public.create_subscription(
  subscription_name TEXT,
  subscription_amount DECIMAL,
  subscription_billing_cycle TEXT,
  subscription_start_date TEXT,
  subscription_description TEXT DEFAULT NULL,
  subscription_currency TEXT DEFAULT 'USD',
  subscription_end_date TEXT DEFAULT NULL,
  subscription_category_id UUID DEFAULT NULL,
  subscription_account_id UUID DEFAULT NULL,
  subscription_provider TEXT DEFAULT NULL,
  subscription_payment_method TEXT DEFAULT NULL,
  subscription_reminder_days INTEGER DEFAULT 3,
  subscription_notes TEXT DEFAULT NULL,
  subscription_family_group_id UUID DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = public
AS $$
DECLARE
  new_subscription_id UUID;
  parsed_start_date DATE;
  parsed_end_date DATE;
  calculated_next_billing_date DATE;
BEGIN
  -- Validate inputs
  IF subscription_name IS NULL OR TRIM(subscription_name) = '' THEN
    RAISE EXCEPTION 'Subscription name is required';
  END IF;
  
  IF subscription_amount <= 0 THEN
    RAISE EXCEPTION 'Subscription amount must be greater than 0';
  END IF;
  
  IF subscription_billing_cycle NOT IN ('weekly', 'monthly', 'quarterly', 'yearly') THEN
    RAISE EXCEPTION 'Invalid billing cycle. Must be weekly, monthly, quarterly, or yearly';
  END IF;
  
  -- Parse dates
  BEGIN
    parsed_start_date := subscription_start_date::DATE;
  EXCEPTION WHEN OTHERS THEN
    RAISE EXCEPTION 'Invalid start date format';
  END;
  
  IF subscription_end_date IS NOT NULL THEN
    BEGIN
      parsed_end_date := subscription_end_date::DATE;
    EXCEPTION WHEN OTHERS THEN
      RAISE EXCEPTION 'Invalid end date format';
    END;
    
    IF parsed_end_date <= parsed_start_date THEN
      RAISE EXCEPTION 'End date must be after start date';
    END IF;
  END IF;
  
  -- Calculate next billing date
  calculated_next_billing_date := public.calculate_next_billing_date(parsed_start_date, subscription_billing_cycle);
  
  -- If family group is specified, verify user is a member
  IF subscription_family_group_id IS NOT NULL THEN
    IF NOT EXISTS (
      SELECT 1 FROM public.family_group_members fgm
      WHERE fgm.group_id = subscription_family_group_id
      AND fgm.user_id = auth.uid()
          ) THEN
      RAISE EXCEPTION 'User is not a member of the specified family group';
    END IF;
  END IF;
  
  -- Verify category belongs to user or family group (if specified)
  IF subscription_category_id IS NOT NULL THEN
    IF subscription_family_group_id IS NOT NULL THEN
      -- Family group subscription - category must belong to family group or user
      IF NOT EXISTS (
        SELECT 1 FROM public.categories c
        WHERE c.id = subscription_category_id
        AND (c.user_id = auth.uid() OR c.family_group_id = subscription_family_group_id)
      ) THEN
        RAISE EXCEPTION 'Category does not exist or is not accessible';
      END IF;
    ELSE
      -- Personal subscription - category must belong to user
      IF NOT EXISTS (
        SELECT 1 FROM public.categories c
        WHERE c.id = subscription_category_id
        AND c.user_id = auth.uid()
      ) THEN
        RAISE EXCEPTION 'Category does not exist or is not accessible';
      END IF;
    END IF;
  END IF;
  
  -- Verify account belongs to user or family group (if specified)
  IF subscription_account_id IS NOT NULL THEN
    IF subscription_family_group_id IS NOT NULL THEN
      -- Family group subscription - account must belong to family group or user
      IF NOT EXISTS (
        SELECT 1 FROM public.accounts a
        WHERE a.id = subscription_account_id
        AND (a.user_id = auth.uid() OR a.family_group_id = subscription_family_group_id)
      ) THEN
        RAISE EXCEPTION 'Account does not exist or is not accessible';
      END IF;
    ELSE
      -- Personal subscription - account must belong to user
      IF NOT EXISTS (
        SELECT 1 FROM public.accounts a
        WHERE a.id = subscription_account_id
        AND a.user_id = auth.uid()
      ) THEN
        RAISE EXCEPTION 'Account does not exist or is not accessible';
      END IF;
    END IF;
  END IF;
  
  -- Insert subscription
  INSERT INTO public.subscriptions (
    user_id,
    family_group_id,
    name,
    description,
    category_id,
    account_id,
    amount,
    currency,
    billing_cycle,
    start_date,
    end_date,
    next_billing_date,
    provider,
    payment_method,
    reminder_days,
    notes
  ) VALUES (
    auth.uid(),
    subscription_family_group_id,
    TRIM(subscription_name),
    CASE WHEN TRIM(subscription_description) = '' THEN NULL ELSE TRIM(subscription_description) END,
    subscription_category_id,
    subscription_account_id,
    subscription_amount,
    UPPER(subscription_currency),
    subscription_billing_cycle,
    parsed_start_date,
    parsed_end_date,
    calculated_next_billing_date,
    CASE WHEN TRIM(subscription_provider) = '' THEN NULL ELSE TRIM(subscription_provider) END,
    CASE WHEN TRIM(subscription_payment_method) = '' THEN NULL ELSE TRIM(subscription_payment_method) END,
    COALESCE(subscription_reminder_days, 3),
    CASE WHEN TRIM(subscription_notes) = '' THEN NULL ELSE TRIM(subscription_notes) END
  ) RETURNING id INTO new_subscription_id;
  
  RETURN new_subscription_id;
END;
$$;

-- Function to update an existing subscription
CREATE OR REPLACE FUNCTION public.update_subscription(
  subscription_id UUID,
  subscription_name TEXT DEFAULT NULL,
  subscription_description TEXT DEFAULT NULL,
  subscription_amount DECIMAL DEFAULT NULL,
  subscription_currency TEXT DEFAULT NULL,
  subscription_billing_cycle TEXT DEFAULT NULL,
  subscription_start_date TEXT DEFAULT NULL,
  subscription_end_date TEXT DEFAULT NULL,
  subscription_category_id UUID DEFAULT NULL,
  subscription_account_id UUID DEFAULT NULL,
  subscription_provider TEXT DEFAULT NULL,
  subscription_payment_method TEXT DEFAULT NULL,
  subscription_reminder_days INTEGER DEFAULT NULL,
  subscription_notes TEXT DEFAULT NULL,
  subscription_is_active BOOLEAN DEFAULT NULL,
  subscription_auto_renew BOOLEAN DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = public
AS $$
DECLARE
  existing_subscription RECORD;
  parsed_start_date DATE;
  parsed_end_date DATE;
  calculated_next_billing_date DATE;
  needs_next_billing_update BOOLEAN := false;
BEGIN
  -- Get existing subscription and verify access
  SELECT * INTO existing_subscription
  FROM public.subscriptions s
  WHERE s.id = subscription_id
    AND (
      -- Personal subscription
      (s.user_id = auth.uid() AND s.family_group_id IS NULL)
      OR
      -- Family group subscription where user is a member
      (s.family_group_id IS NOT NULL AND EXISTS (
        SELECT 1 FROM public.family_group_members fgm
        WHERE fgm.group_id = s.family_group_id
        AND fgm.user_id = auth.uid()
              ))
    );
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Subscription not found or access denied';
  END IF;
  
  -- Validate inputs that are being updated
  IF subscription_name IS NOT NULL AND TRIM(subscription_name) = '' THEN
    RAISE EXCEPTION 'Subscription name cannot be empty';
  END IF;
  
  IF subscription_amount IS NOT NULL AND subscription_amount <= 0 THEN
    RAISE EXCEPTION 'Subscription amount must be greater than 0';
  END IF;
  
  IF subscription_billing_cycle IS NOT NULL AND subscription_billing_cycle NOT IN ('weekly', 'monthly', 'quarterly', 'yearly') THEN
    RAISE EXCEPTION 'Invalid billing cycle. Must be weekly, monthly, quarterly, or yearly';
  END IF;
  
  -- Handle date updates
  parsed_start_date := existing_subscription.start_date;
  parsed_end_date := existing_subscription.end_date;
  
  IF subscription_start_date IS NOT NULL THEN
    BEGIN
      parsed_start_date := subscription_start_date::DATE;
      needs_next_billing_update := true;
    EXCEPTION WHEN OTHERS THEN
      RAISE EXCEPTION 'Invalid start date format';
    END;
  END IF;
  
  IF subscription_end_date IS NOT NULL THEN
    IF subscription_end_date = '' THEN
      parsed_end_date := NULL;
    ELSE
      BEGIN
        parsed_end_date := subscription_end_date::DATE;
      EXCEPTION WHEN OTHERS THEN
        RAISE EXCEPTION 'Invalid end date format';
      END;
    END IF;
  END IF;
  
  IF parsed_end_date IS NOT NULL AND parsed_end_date <= parsed_start_date THEN
    RAISE EXCEPTION 'End date must be after start date';
  END IF;
  
  -- Check if billing cycle changed
  IF subscription_billing_cycle IS NOT NULL AND subscription_billing_cycle != existing_subscription.billing_cycle THEN
    needs_next_billing_update := true;
  END IF;
  
  -- Calculate new next billing date if needed
  IF needs_next_billing_update THEN
    calculated_next_billing_date := public.calculate_next_billing_date(
      parsed_start_date, 
      COALESCE(subscription_billing_cycle, existing_subscription.billing_cycle)
    );
  ELSE
    calculated_next_billing_date := existing_subscription.next_billing_date;
  END IF;
  
  -- Verify category access if being updated
  IF subscription_category_id IS NOT NULL THEN
    IF existing_subscription.family_group_id IS NOT NULL THEN
      -- Family group subscription
      IF NOT EXISTS (
        SELECT 1 FROM public.categories c
        WHERE c.id = subscription_category_id
        AND (c.user_id = auth.uid() OR c.family_group_id = existing_subscription.family_group_id)
      ) THEN
        RAISE EXCEPTION 'Category does not exist or is not accessible';
      END IF;
    ELSE
      -- Personal subscription
      IF NOT EXISTS (
        SELECT 1 FROM public.categories c
        WHERE c.id = subscription_category_id
        AND c.user_id = auth.uid()
      ) THEN
        RAISE EXCEPTION 'Category does not exist or is not accessible';
      END IF;
    END IF;
  END IF;
  
  -- Verify account access if being updated
  IF subscription_account_id IS NOT NULL THEN
    IF existing_subscription.family_group_id IS NOT NULL THEN
      -- Family group subscription
      IF NOT EXISTS (
        SELECT 1 FROM public.accounts a
        WHERE a.id = subscription_account_id
        AND (a.user_id = auth.uid() OR a.family_group_id = existing_subscription.family_group_id)
      ) THEN
        RAISE EXCEPTION 'Account does not exist or is not accessible';
      END IF;
    ELSE
      -- Personal subscription
      IF NOT EXISTS (
        SELECT 1 FROM public.accounts a
        WHERE a.id = subscription_account_id
        AND a.user_id = auth.uid()
      ) THEN
        RAISE EXCEPTION 'Account does not exist or is not accessible';
      END IF;
    END IF;
  END IF;
  
  -- Update subscription
  UPDATE public.subscriptions SET
    name = COALESCE(NULLIF(TRIM(subscription_name), ''), name),
    description = CASE 
      WHEN subscription_description IS NOT NULL THEN 
        CASE WHEN TRIM(subscription_description) = '' THEN NULL ELSE TRIM(subscription_description) END
      ELSE description 
    END,
    amount = COALESCE(subscription_amount, amount),
    currency = COALESCE(UPPER(subscription_currency), currency),
    billing_cycle = COALESCE(subscription_billing_cycle, billing_cycle),
    start_date = parsed_start_date,
    end_date = parsed_end_date,
    next_billing_date = calculated_next_billing_date,
    category_id = COALESCE(subscription_category_id, category_id),
    account_id = COALESCE(subscription_account_id, account_id),
    provider = CASE 
      WHEN subscription_provider IS NOT NULL THEN 
        CASE WHEN TRIM(subscription_provider) = '' THEN NULL ELSE TRIM(subscription_provider) END
      ELSE provider 
    END,
    payment_method = CASE 
      WHEN subscription_payment_method IS NOT NULL THEN 
        CASE WHEN TRIM(subscription_payment_method) = '' THEN NULL ELSE TRIM(subscription_payment_method) END
      ELSE payment_method 
    END,
    reminder_days = COALESCE(subscription_reminder_days, reminder_days),
    notes = CASE 
      WHEN subscription_notes IS NOT NULL THEN 
        CASE WHEN TRIM(subscription_notes) = '' THEN NULL ELSE TRIM(subscription_notes) END
      ELSE notes 
    END,
    is_active = COALESCE(subscription_is_active, is_active),
    auto_renew = COALESCE(subscription_auto_renew, auto_renew)
  WHERE id = subscription_id;
  
  RETURN true;
END;
$$;

-- Function to delete a subscription
CREATE OR REPLACE FUNCTION public.delete_subscription(subscription_id UUID)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = public
AS $$
BEGIN
  -- Delete subscription (RLS policies will ensure proper access control)
  DELETE FROM public.subscriptions
  WHERE id = subscription_id
    AND (
      -- Personal subscription
      (user_id = auth.uid() AND family_group_id IS NULL)
      OR
      -- Family group subscription where user is a member
      (family_group_id IS NOT NULL AND EXISTS (
        SELECT 1 FROM public.family_group_members fgm
        WHERE fgm.group_id = subscriptions.family_group_id
        AND fgm.user_id = auth.uid()
              ))
    );
  
  -- Return whether a row was deleted
  RETURN FOUND;
END;
$$;

-- Add comments for documentation
COMMENT ON FUNCTION public.create_subscription(TEXT, DECIMAL, TEXT, TEXT, TEXT, TEXT, TEXT, UUID, UUID, TEXT, TEXT, INTEGER, TEXT, UUID) IS 'Creates a new subscription with validation and proper access control';
COMMENT ON FUNCTION public.update_subscription(UUID, TEXT, TEXT, DECIMAL, TEXT, TEXT, TEXT, TEXT, UUID, UUID, TEXT, TEXT, INTEGER, TEXT, BOOLEAN, BOOLEAN) IS 'Updates an existing subscription with validation and proper access control';
COMMENT ON FUNCTION public.delete_subscription(UUID) IS 'Deletes a subscription with proper access control';