-- Migration: Add RPC functions for family group financial management

-- RPC Function: get_family_group_accounts
-- Returns all accounts for a specific family group
CREATE OR REPLACE FUNCTION public.get_family_group_accounts(p_group_id UUID)
RETURNS TABLE (
    id UUID,
    name TEXT,
    type TEXT,
    balance DECIMAL(19, 4),
    currency TEXT,
    icon TEXT,
    color TEXT,
    created_by_user_id UUID,
    created_by_email TEXT,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ
) AS $$
DECLARE
    v_user_id UUID := auth.uid();
BEGIN
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'User must be authenticated to view family group accounts.';
    END IF;

    -- Check if user is a member of the group
    IF NOT EXISTS (
        SELECT 1 FROM public.family_group_members
        WHERE group_id = p_group_id AND user_id = v_user_id
    ) THEN
        RAISE EXCEPTION 'Access denied: You are not a member of this family group.';
    END IF;

    RETURN QUERY
    SELECT
        a.id,
        a.name,
        a.type,
        a.balance,
        a.currency,
        a.icon,
        a.color,
        a.created_by_user_id,
        u.email AS created_by_email,
        a.created_at,
        a.updated_at
    FROM public.accounts a
    LEFT JOIN auth.users u ON a.created_by_user_id = u.id
    WHERE a.family_group_id = p_group_id
    ORDER BY a.created_at DESC;
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER;

COMMENT ON FUNCTION public.get_family_group_accounts(UUID) IS 'Returns all accounts for a specific family group. Only accessible to group members.';

-- RPC Function: get_family_group_transactions
-- Returns transactions for a specific family group with pagination
CREATE OR REPLACE FUNCTION public.get_family_group_transactions(
    p_group_id UUID,
    p_limit INTEGER DEFAULT 50,
    p_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    account_id UUID,
    account_name TEXT,
    category_id UUID,
    category_name TEXT,
    amount DECIMAL(19, 4),
    type TEXT,
    description TEXT,
    date TIMESTAMPTZ,
    notes TEXT,
    created_by_user_id UUID,
    created_by_email TEXT,
    created_at TIMESTAMPTZ
) AS $$
DECLARE
    v_user_id UUID := auth.uid();
BEGIN
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'User must be authenticated to view family group transactions.';
    END IF;

    -- Check if user is a member of the group
    IF NOT EXISTS (
        SELECT 1 FROM public.family_group_members
        WHERE group_id = p_group_id AND user_id = v_user_id
    ) THEN
        RAISE EXCEPTION 'Access denied: You are not a member of this family group.';
    END IF;

    RETURN QUERY
    SELECT
        t.id,
        t.account_id,
        a.name AS account_name,
        t.category_id,
        c.name AS category_name,
        t.amount,
        t.type,
        t.description,
        t.date,
        t.notes,
        t.created_by_user_id,
        u.email AS created_by_email,
        t.created_at
    FROM public.transactions t
    LEFT JOIN public.accounts a ON t.account_id = a.id
    LEFT JOIN public.categories c ON t.category_id = c.id
    LEFT JOIN auth.users u ON t.created_by_user_id = u.id
    WHERE t.family_group_id = p_group_id
    ORDER BY t.date DESC, t.created_at DESC
    LIMIT p_limit OFFSET p_offset;
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER;

COMMENT ON FUNCTION public.get_family_group_transactions(UUID, INTEGER, INTEGER) IS 'Returns transactions for a specific family group with pagination. Only accessible to group members.';

-- RPC Function: get_family_group_budgets
-- Returns all budgets for a specific family group
CREATE OR REPLACE FUNCTION public.get_family_group_budgets(p_group_id UUID)
RETURNS TABLE (
    id UUID,
    category_id UUID,
    category_name TEXT,
    amount DECIMAL(19, 4),
    period TEXT,
    start_date TIMESTAMPTZ,
    end_date TIMESTAMPTZ,
    spent_amount DECIMAL(19, 4),
    created_by_user_id UUID,
    created_by_email TEXT,
    created_at TIMESTAMPTZ
) AS $$
DECLARE
    v_user_id UUID := auth.uid();
BEGIN
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'User must be authenticated to view family group budgets.';
    END IF;

    -- Check if user is a member of the group
    IF NOT EXISTS (
        SELECT 1 FROM public.family_group_members
        WHERE group_id = p_group_id AND user_id = v_user_id
    ) THEN
        RAISE EXCEPTION 'Access denied: You are not a member of this family group.';
    END IF;

    RETURN QUERY
    SELECT
        b.id,
        b.category_id,
        c.name AS category_name,
        b.amount,
        b.period,
        b.start_date,
        b.end_date,
        COALESCE(
            (SELECT SUM(ABS(t.amount)) 
             FROM public.transactions t 
             WHERE t.family_group_id = p_group_id 
             AND t.category_id = b.category_id 
             AND t.type = 'expense'
             AND t.date >= b.start_date 
             AND (b.end_date IS NULL OR t.date <= b.end_date)
            ), 0
        ) AS spent_amount,
        b.created_by_user_id,
        u.email AS created_by_email,
        b.created_at
    FROM public.budgets b
    LEFT JOIN public.categories c ON b.category_id = c.id
    LEFT JOIN auth.users u ON b.created_by_user_id = u.id
    WHERE b.family_group_id = p_group_id
    ORDER BY b.created_at DESC;
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER;

COMMENT ON FUNCTION public.get_family_group_budgets(UUID) IS 'Returns all budgets for a specific family group with spent amounts. Only accessible to group members.';

-- RPC Function: get_family_group_goals
-- Returns all financial goals for a specific family group
CREATE OR REPLACE FUNCTION public.get_family_group_goals(p_group_id UUID)
RETURNS TABLE (
    id UUID,
    name TEXT,
    target_amount DECIMAL(19, 4),
    current_amount DECIMAL(19, 4),
    target_date TIMESTAMPTZ,
    status TEXT,
    icon TEXT,
    color TEXT,
    progress_percentage DECIMAL(5, 2),
    created_by_user_id UUID,
    created_by_email TEXT,
    created_at TIMESTAMPTZ
) AS $$
DECLARE
    v_user_id UUID := auth.uid();
BEGIN
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'User must be authenticated to view family group goals.';
    END IF;

    -- Check if user is a member of the group
    IF NOT EXISTS (
        SELECT 1 FROM public.family_group_members
        WHERE group_id = p_group_id AND user_id = v_user_id
    ) THEN
        RAISE EXCEPTION 'Access denied: You are not a member of this family group.';
    END IF;

    RETURN QUERY
    SELECT
        fg.id,
        fg.name,
        fg.target_amount,
        fg.current_amount,
        fg.target_date,
        fg.status,
        fg.icon,
        fg.color,
        CASE 
            WHEN fg.target_amount > 0 THEN 
                ROUND((fg.current_amount / fg.target_amount * 100)::DECIMAL, 2)
            ELSE 0
        END AS progress_percentage,
        fg.created_by_user_id,
        u.email AS created_by_email,
        fg.created_at
    FROM public.financial_goals fg
    LEFT JOIN auth.users u ON fg.created_by_user_id = u.id
    WHERE fg.family_group_id = p_group_id
    ORDER BY fg.created_at DESC;
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER;

COMMENT ON FUNCTION public.get_family_group_goals(UUID) IS 'Returns all financial goals for a specific family group with progress calculations. Only accessible to group members.';

-- RPC Function: get_family_group_categories
-- Returns all categories for a specific family group
CREATE OR REPLACE FUNCTION public.get_family_group_categories(p_group_id UUID)
RETURNS TABLE (
    id UUID,
    name TEXT,
    type TEXT,
    icon TEXT,
    color TEXT,
    parent_id UUID,
    parent_name TEXT,
    created_by_user_id UUID,
    created_by_email TEXT,
    created_at TIMESTAMPTZ
) AS $$
DECLARE
    v_user_id UUID := auth.uid();
BEGIN
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'User must be authenticated to view family group categories.';
    END IF;

    -- Check if user is a member of the group
    IF NOT EXISTS (
        SELECT 1 FROM public.family_group_members
        WHERE group_id = p_group_id AND user_id = v_user_id
    ) THEN
        RAISE EXCEPTION 'Access denied: You are not a member of this family group.';
    END IF;

    RETURN QUERY
    SELECT
        c.id,
        c.name,
        c.type,
        c.icon,
        c.color,
        c.parent_id,
        pc.name AS parent_name,
        c.created_by_user_id,
        u.email AS created_by_email,
        c.created_at
    FROM public.categories c
    LEFT JOIN public.categories pc ON c.parent_id = pc.id
    LEFT JOIN auth.users u ON c.created_by_user_id = u.id
    WHERE c.family_group_id = p_group_id
    ORDER BY c.type, c.name;
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER;

COMMENT ON FUNCTION public.get_family_group_categories(UUID) IS 'Returns all categories for a specific family group. Only accessible to group members.';

-- RPC Function: get_family_group_financial_summary
-- Returns a financial summary for a specific family group
CREATE OR REPLACE FUNCTION public.get_family_group_financial_summary(p_group_id UUID)
RETURNS JSONB AS $$
DECLARE
    v_user_id UUID := auth.uid();
    v_total_balance DECIMAL(19, 4);
    v_monthly_income DECIMAL(19, 4);
    v_monthly_expenses DECIMAL(19, 4);
    v_active_budgets INTEGER;
    v_active_goals INTEGER;
    v_accounts_count INTEGER;
BEGIN
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'User must be authenticated to view family group financial summary.';
    END IF;

    -- Check if user is a member of the group
    IF NOT EXISTS (
        SELECT 1 FROM public.family_group_members
        WHERE group_id = p_group_id AND user_id = v_user_id
    ) THEN
        RAISE EXCEPTION 'Access denied: You are not a member of this family group.';
    END IF;

    -- Calculate total balance across all accounts
    SELECT COALESCE(SUM(balance), 0) INTO v_total_balance
    FROM public.accounts
    WHERE family_group_id = p_group_id;

    -- Calculate monthly income (current month)
    SELECT COALESCE(SUM(amount), 0) INTO v_monthly_income
    FROM public.transactions
    WHERE family_group_id = p_group_id
    AND type = 'income'
    AND date >= date_trunc('month', CURRENT_DATE)
    AND date < date_trunc('month', CURRENT_DATE) + INTERVAL '1 month';

    -- Calculate monthly expenses (current month)
    SELECT COALESCE(SUM(ABS(amount)), 0) INTO v_monthly_expenses
    FROM public.transactions
    WHERE family_group_id = p_group_id
    AND type = 'expense'
    AND date >= date_trunc('month', CURRENT_DATE)
    AND date < date_trunc('month', CURRENT_DATE) + INTERVAL '1 month';

    -- Count active budgets
    SELECT COUNT(*) INTO v_active_budgets
    FROM public.budgets
    WHERE family_group_id = p_group_id
    AND (end_date IS NULL OR end_date >= CURRENT_DATE);

    -- Count active goals
    SELECT COUNT(*) INTO v_active_goals
    FROM public.financial_goals
    WHERE family_group_id = p_group_id
    AND status = 'active';

    -- Count accounts
    SELECT COUNT(*) INTO v_accounts_count
    FROM public.accounts
    WHERE family_group_id = p_group_id;

    RETURN jsonb_build_object(
        'total_balance', v_total_balance,
        'monthly_income', v_monthly_income,
        'monthly_expenses', v_monthly_expenses,
        'monthly_net', v_monthly_income - v_monthly_expenses,
        'active_budgets', v_active_budgets,
        'active_goals', v_active_goals,
        'accounts_count', v_accounts_count
    );
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER;

COMMENT ON FUNCTION public.get_family_group_financial_summary(UUID) IS 'Returns a comprehensive financial summary for a specific family group. Only accessible to group members.';