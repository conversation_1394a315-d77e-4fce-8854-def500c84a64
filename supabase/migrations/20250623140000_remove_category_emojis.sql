-- Remove emoji icons from predefined categories
-- This migration removes emoji icons from all category data

-- Update predefined_categories to remove emoji icons
UPDATE public.predefined_categories SET icon = '' WHERE icon IS NOT NULL;

-- Update existing user categories to remove emoji icons
UPDATE public.categories SET icon = '' WHER<PERSON> icon IS NOT NULL;

-- Update any family group categories to remove emoji icons
-- (in case there are any existing family categories with emojis)
UPDATE public.categories SET icon = '' WHERE family_group_id IS NOT NULL AND icon IS NOT NULL;

-- Log the changes
DO $$
BEGIN
  RAISE NOTICE 'Removed emoji icons from all category data';
END
$$;