-- Complete subscription system migration
-- This migration creates the entire subscription system in one go

-- Drop existing incomplete subscriptions table if it exists
DROP TABLE IF EXISTS public.subscriptions CASCADE;
DROP TABLE IF EXISTS public.subscription_notifications CASCADE;

-- Drop any existing subscription functions
DROP FUNCTION IF EXISTS public.calculate_next_billing_date(DATE, TEXT) CASCADE;
DROP FUNCTION IF EXISTS public.calculate_monthly_cost(DECIMAL, TEXT) CASCADE;
DROP FUNCTION IF EXISTS public.calculate_days_until_payment(DATE) CASCADE;
DROP FUNCTION IF EXISTS public.get_user_subscriptions() CASCADE;
DROP FUNCTION IF EXISTS public.get_family_group_subscriptions(UUID) CASCADE;
DROP FUNCTION IF EXISTS public.get_subscription_summary() CASCADE;
DROP FUNCTION IF EXISTS public.get_family_subscription_summary(UUID) CASCADE;
DROP FUNCTION IF EXISTS public.get_upcoming_payments(INTEGER) CASCADE;
DROP FUNCTION IF EXISTS public.create_subscription(TEXT, TEXT, DECIMAL, TEXT, TEXT, TEXT, TEXT, UUID, UUID, TEXT, TEXT, INTEGER, TEXT, UUID) CASCADE;
DROP FUNCTION IF EXISTS public.update_subscription(UUID, TEXT, TEXT, DECIMAL, TEXT, TEXT, TEXT, TEXT, UUID, UUID, TEXT, TEXT, INTEGER, TEXT, BOOLEAN, BOOLEAN) CASCADE;
DROP FUNCTION IF EXISTS public.delete_subscription(UUID) CASCADE;

-- Create subscriptions table with all fields from TypeScript interface
CREATE TABLE public.subscriptions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES public.user_profiles(id) ON DELETE CASCADE,
  family_group_id UUID REFERENCES public.family_groups(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  category_id UUID REFERENCES public.categories(id) ON DELETE SET NULL,
  account_id UUID REFERENCES public.accounts(id) ON DELETE SET NULL,
  amount DECIMAL(19, 4) NOT NULL CHECK (amount > 0),
  currency TEXT NOT NULL DEFAULT 'USD',
  billing_cycle TEXT NOT NULL CHECK (billing_cycle IN ('weekly', 'monthly', 'quarterly', 'yearly')),
  start_date DATE NOT NULL,
  end_date DATE,
  next_billing_date DATE NOT NULL,
  is_active BOOLEAN NOT NULL DEFAULT true,
  auto_renew BOOLEAN NOT NULL DEFAULT true,
  provider TEXT,
  payment_method TEXT,
  reminder_days INTEGER NOT NULL DEFAULT 3 CHECK (reminder_days >= 0),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  
  -- Constraints
  CONSTRAINT subscriptions_end_date_after_start CHECK (end_date IS NULL OR end_date > start_date),
  CONSTRAINT subscriptions_next_billing_after_start CHECK (next_billing_date >= start_date)
);

-- Create subscription_notifications table for future reminder functionality
CREATE TABLE public.subscription_notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  subscription_id UUID NOT NULL REFERENCES public.subscriptions(id) ON DELETE CASCADE,
  notification_type TEXT NOT NULL CHECK (notification_type IN ('upcoming_payment', 'payment_failed', 'renewal_reminder', 'cancellation_reminder')),
  scheduled_for TIMESTAMP WITH TIME ZONE NOT NULL,
  sent_at TIMESTAMP WITH TIME ZONE,
  is_sent BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Add comments for documentation
COMMENT ON TABLE public.subscriptions IS 'Stores user and family group subscription information for recurring payments and bills';
COMMENT ON COLUMN public.subscriptions.family_group_id IS 'If set, subscription is shared with family group members';
COMMENT ON COLUMN public.subscriptions.billing_cycle IS 'Frequency of billing: weekly, monthly, quarterly, yearly';
COMMENT ON COLUMN public.subscriptions.next_billing_date IS 'Next expected payment date for this subscription';
COMMENT ON COLUMN public.subscriptions.reminder_days IS 'Number of days before payment to send reminder';

COMMENT ON TABLE public.subscription_notifications IS 'Stores scheduled notifications for subscription reminders and alerts';

-- Enable RLS on both tables
ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscription_notifications ENABLE ROW LEVEL SECURITY;

-- RLS Policies for subscriptions table

-- Users can view their own subscriptions
CREATE POLICY "Users can view own subscriptions" ON public.subscriptions
  FOR SELECT USING (auth.uid() = user_id);

-- Users can view family group subscriptions if they are members
CREATE POLICY "Users can view family group subscriptions" ON public.subscriptions
  FOR SELECT USING (
    family_group_id IS NOT NULL AND
    EXISTS (
      SELECT 1 FROM public.family_group_members fgm
      WHERE fgm.group_id = subscriptions.family_group_id
      AND fgm.user_id = auth.uid()
    )
  );

-- Users can insert their own subscriptions
CREATE POLICY "Users can create own subscriptions" ON public.subscriptions
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can insert family group subscriptions if they are members
CREATE POLICY "Users can create family group subscriptions" ON public.subscriptions
  FOR INSERT WITH CHECK (
    family_group_id IS NOT NULL AND
    auth.uid() = user_id AND
    EXISTS (
      SELECT 1 FROM public.family_group_members fgm
      WHERE fgm.group_id = subscriptions.family_group_id
      AND fgm.user_id = auth.uid()
    )
  );

-- Users can update their own subscriptions
CREATE POLICY "Users can update own subscriptions" ON public.subscriptions
  FOR UPDATE USING (auth.uid() = user_id) WITH CHECK (auth.uid() = user_id);

-- Users can update family group subscriptions if they are members
CREATE POLICY "Users can update family group subscriptions" ON public.subscriptions
  FOR UPDATE USING (
    family_group_id IS NOT NULL AND
    EXISTS (
      SELECT 1 FROM public.family_group_members fgm
      WHERE fgm.group_id = subscriptions.family_group_id
      AND fgm.user_id = auth.uid()
    )
  ) WITH CHECK (
    family_group_id IS NOT NULL AND
    EXISTS (
      SELECT 1 FROM public.family_group_members fgm
      WHERE fgm.group_id = subscriptions.family_group_id
      AND fgm.user_id = auth.uid()
    )
  );

-- Users can delete their own subscriptions
CREATE POLICY "Users can delete own subscriptions" ON public.subscriptions
  FOR DELETE USING (auth.uid() = user_id);

-- Users can delete family group subscriptions if they are members
CREATE POLICY "Users can delete family group subscriptions" ON public.subscriptions
  FOR DELETE USING (
    family_group_id IS NOT NULL AND
    EXISTS (
      SELECT 1 FROM public.family_group_members fgm
      WHERE fgm.group_id = subscriptions.family_group_id
      AND fgm.user_id = auth.uid()
    )
  );

-- RLS Policies for subscription_notifications table

-- Users can view notifications for their own subscriptions
CREATE POLICY "Users can view own subscription notifications" ON public.subscription_notifications
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.subscriptions s
      WHERE s.id = subscription_notifications.subscription_id
      AND s.user_id = auth.uid()
    )
  );

-- Users can view notifications for family group subscriptions
CREATE POLICY "Users can view family subscription notifications" ON public.subscription_notifications
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.subscriptions s
      WHERE s.id = subscription_notifications.subscription_id
      AND s.family_group_id IS NOT NULL
      AND EXISTS (
        SELECT 1 FROM public.family_group_members fgm
        WHERE fgm.group_id = s.family_group_id
        AND fgm.user_id = auth.uid()
      )
    )
  );

-- Create indexes for better performance
CREATE INDEX idx_subscriptions_user_id ON public.subscriptions(user_id);
CREATE INDEX idx_subscriptions_family_group_id ON public.subscriptions(family_group_id) WHERE family_group_id IS NOT NULL;
CREATE INDEX idx_subscriptions_next_billing_date ON public.subscriptions(next_billing_date) WHERE is_active = true;
CREATE INDEX idx_subscriptions_category_id ON public.subscriptions(category_id) WHERE category_id IS NOT NULL;
CREATE INDEX idx_subscriptions_account_id ON public.subscriptions(account_id) WHERE account_id IS NOT NULL;
CREATE INDEX idx_subscriptions_active ON public.subscriptions(is_active, next_billing_date);

CREATE INDEX idx_subscription_notifications_subscription_id ON public.subscription_notifications(subscription_id);
CREATE INDEX idx_subscription_notifications_scheduled ON public.subscription_notifications(scheduled_for) WHERE is_sent = false;

-- Function to automatically update next_billing_date based on billing_cycle
CREATE OR REPLACE FUNCTION public.calculate_next_billing_date(
  base_date DATE,
  cycle TEXT
) RETURNS DATE
LANGUAGE plpgsql
IMMUTABLE
AS $$
BEGIN
  CASE cycle
    WHEN 'weekly' THEN
      RETURN base_date + INTERVAL '1 week';
    WHEN 'monthly' THEN
      RETURN base_date + INTERVAL '1 month';
    WHEN 'quarterly' THEN
      RETURN base_date + INTERVAL '3 months';
    WHEN 'yearly' THEN
      RETURN base_date + INTERVAL '1 year';
    ELSE
      RAISE EXCEPTION 'Invalid billing cycle: %', cycle;
  END CASE;
END;
$$;

-- Function to calculate monthly cost for comparison (matches TypeScript logic)
CREATE OR REPLACE FUNCTION public.calculate_monthly_cost(
  amount DECIMAL,
  cycle TEXT
) RETURNS DECIMAL
LANGUAGE plpgsql
IMMUTABLE
AS $$
BEGIN
  CASE cycle
    WHEN 'weekly' THEN
      RETURN amount * 4.33; -- 4.33 weeks per month on average
    WHEN 'monthly' THEN
      RETURN amount;
    WHEN 'quarterly' THEN
      RETURN amount / 3;
    WHEN 'yearly' THEN
      RETURN amount / 12;
    ELSE
      RAISE EXCEPTION 'Invalid billing cycle: %', cycle;
  END CASE;
END;
$$;

-- Function to calculate days until next payment
CREATE OR REPLACE FUNCTION public.calculate_days_until_payment(
  next_billing_date DATE
) RETURNS INTEGER
LANGUAGE plpgsql
IMMUTABLE
AS $$
BEGIN
  RETURN GREATEST(0, (next_billing_date - CURRENT_DATE)::INTEGER);
END;
$$;

-- Trigger to automatically update updated_at on subscriptions
CREATE TRIGGER update_subscriptions_updated_at
  BEFORE UPDATE ON public.subscriptions
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();