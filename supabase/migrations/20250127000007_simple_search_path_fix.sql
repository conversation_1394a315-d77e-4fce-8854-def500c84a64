-- Migration: Simple search path fix for existing functions
-- This migration updates existing functions to add SET search_path without changing their logic

-- Update generate_secure_token function
CREATE OR REPLACE FUNCTION public.generate_secure_token()
RETURNS TEXT AS $$
BEGIN
    RETURN encode(gen_random_bytes(16), 'hex'); -- Generates a 32-character hex token
END;
$$ LANGUAGE plpgsql VOLATILE SECURITY DEFINER SET search_path = public;

-- Update is_family_group_member_sd function
CREATE OR REPLACE FUNCTION public.is_family_group_member_sd(p_group_id UUID, p_user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1
        FROM public.family_group_members
        WHERE group_id = p_group_id
          AND user_id = p_user_id
    );
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER SET search_path = public;

-- Update create_family_group function
CREATE OR REPLACE FUNCTION public.create_family_group(p_group_name TEXT)
R<PERSON>URNS UUID AS $$
DECLARE
    v_user_id UUID := auth.uid();
    v_group_id UUID;
BEGIN
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'User must be authenticated to create a group.';
    END IF;

    IF p_group_name IS NULL OR trim(p_group_name) = '' THEN
        RAISE EXCEPTION 'Group name cannot be empty.';
    END IF;

    -- Create the group
    INSERT INTO public.family_groups (name, owner_user_id)
    VALUES (p_group_name, v_user_id)
    RETURNING id INTO v_group_id;

    -- Add the creator as an admin member
    INSERT INTO public.family_group_members (group_id, user_id, role)
    VALUES (v_group_id, v_user_id, 'admin');

    RETURN v_group_id;
END;
$$ LANGUAGE plpgsql VOLATILE SECURITY DEFINER SET search_path = public;

-- Note: Skipping invite_to_family_group and accept_family_group_invitation functions
-- as they have return type conflicts. These will need to be fixed manually in the database
-- or through a more complex migration approach.

-- The main security issue (missing SET search_path) has been addressed for the core functions above.
-- For the remaining functions, the search path can be added manually or through database admin tools.
