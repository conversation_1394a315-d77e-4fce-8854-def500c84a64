-- Migration: Fix remaining functions with search path by creating new versions and swapping
-- This approach avoids return type conflicts by creating new functions and then swapping them

-- Step 1: Create new versions of functions with search path

-- New invite_to_family_group function
CREATE OR REPLACE FUNCTION public.invite_to_family_group_new(p_group_id UUID, p_invitee_email TEXT)
RETURNS JSONB AS $$
DECLARE
    v_inviter_id UUID := auth.uid();
    v_invitation_id UUID;
    v_token TEXT;
    v_expires_at TIMESTAMPTZ;
BEGIN
    IF v_inviter_id IS NULL THEN
        RAISE EXCEPTION 'User must be authenticated to invite members.';
    END IF;

    IF NOT public.is_family_group_admin(p_group_id, v_inviter_id) THEN
        RAISE EXCEPTION 'Only admin members can invite new users to the group.';
    END IF;

    IF p_invitee_email IS NULL OR trim(p_invitee_email) = '' THEN
        RAISE EXCEPTION 'Invitee email cannot be empty.';
    END IF;
    
    -- Check if user is already a member
    IF EXISTS (
        SELECT 1 FROM auth.users u
        JOIN public.family_group_members fgm ON u.id = fgm.user_id
        WHERE u.email = p_invitee_email AND fgm.group_id = p_group_id
    ) THEN
        RAISE EXCEPTION 'User with email % is already a member of this group.', p_invitee_email;
    END IF;

    -- Check for existing pending invitation for this email and group
    IF EXISTS (
        SELECT 1 FROM public.family_group_invitations
        WHERE group_id = p_group_id
          AND invitee_email = p_invitee_email
          AND status = 'pending'
          AND expires_at > now()
    ) THEN
        RAISE EXCEPTION 'An active pending invitation already exists for this email and group.';
    END IF;

    v_token := public.generate_secure_token();
    v_expires_at := now() + INTERVAL '7 days';

    INSERT INTO public.family_group_invitations (group_id, invited_by_user_id, invitee_email, token, expires_at, status)
    VALUES (p_group_id, v_inviter_id, p_invitee_email, v_token, v_expires_at, 'pending')
    RETURNING id INTO v_invitation_id;

    -- Return invitation details including the invitation link
    RETURN jsonb_build_object(
        'invitation_id', v_invitation_id,
        'token', v_token,
        'invitation_link', format('https://your-domain.com/invite/%s', v_token),
        'expires_at', v_expires_at
    );
END;
$$ LANGUAGE plpgsql VOLATILE SECURITY DEFINER SET search_path = public;

-- New accept_family_group_invitation function
CREATE OR REPLACE FUNCTION public.accept_family_group_invitation_new(p_invitation_token TEXT)
RETURNS JSONB AS $$
DECLARE
    v_user_id UUID := auth.uid();
    v_invitation RECORD;
    v_group_id UUID;
    v_user_email TEXT;
BEGIN
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'User must be authenticated to accept an invitation.';
    END IF;

    SELECT email INTO v_user_email FROM auth.users WHERE id = v_user_id;

    SELECT * INTO v_invitation
    FROM public.family_group_invitations
    WHERE token = p_invitation_token;

    IF v_invitation IS NULL THEN
        RAISE EXCEPTION 'Invitation token is invalid.';
    END IF;

    IF v_invitation.status <> 'pending' THEN
        RAISE EXCEPTION 'Invitation is no longer pending. Status: %', v_invitation.status;
    END IF;

    IF v_invitation.expires_at < now() THEN
        RAISE EXCEPTION 'Invitation has expired.';
    END IF;

    IF v_invitation.invitee_email <> v_user_email THEN
        RAISE EXCEPTION 'This invitation is not for your email address.';
    END IF;

    v_group_id := v_invitation.group_id;

    -- Check if user is already a member
    IF EXISTS (
        SELECT 1 FROM public.family_group_members
        WHERE group_id = v_group_id AND user_id = v_user_id
    ) THEN
        RAISE EXCEPTION 'You are already a member of this group.';
    END IF;

    -- Add user to the group members
    INSERT INTO public.family_group_members (group_id, user_id, role)
    VALUES (v_group_id, v_user_id, 'member')
    ON CONFLICT (group_id, user_id) DO NOTHING;

    -- Update invitation status
    UPDATE public.family_group_invitations
    SET status = 'accepted'
    WHERE id = v_invitation.id;

    RETURN jsonb_build_object('status', 'success', 'message', 'Successfully joined the group.', 'group_id', v_group_id);
END;
$$ LANGUAGE plpgsql VOLATILE SECURITY DEFINER SET search_path = public;

-- Step 2: Drop old functions and rename new ones
DROP FUNCTION IF EXISTS public.invite_to_family_group(UUID, TEXT) CASCADE;
ALTER FUNCTION public.invite_to_family_group_new(UUID, TEXT) RENAME TO invite_to_family_group;

DROP FUNCTION IF EXISTS public.accept_family_group_invitation(TEXT) CASCADE;
ALTER FUNCTION public.accept_family_group_invitation_new(TEXT) RENAME TO accept_family_group_invitation;

-- Add comments
COMMENT ON FUNCTION public.invite_to_family_group(UUID, TEXT) IS 'Invites a user to join a family group by email. SECURITY DEFINER with search_path protection.';
COMMENT ON FUNCTION public.accept_family_group_invitation(TEXT) IS 'Allows an invited user to accept an invitation using a token. SECURITY DEFINER with search_path protection.';

-- Fix other functions that might need search path
CREATE OR REPLACE FUNCTION public.get_family_group_members(p_group_id UUID)
RETURNS TABLE (
    user_id UUID,
    email VARCHAR(255), 
    role TEXT,
    joined_at TIMESTAMPTZ
) AS $$
BEGIN
    -- Check if the current user is a member of the requested group
    IF NOT EXISTS (
        SELECT 1
        FROM public.family_group_members fgm
        WHERE fgm.group_id = p_group_id AND fgm.user_id = auth.uid()
    ) THEN
        RAISE EXCEPTION 'Access denied: You are not a member of this group.';
    END IF;

    RETURN QUERY
    SELECT
        fgm.user_id,
        u.email::VARCHAR(255),
        fgm.role,
        fgm.joined_at
    FROM public.family_group_members fgm
    LEFT JOIN auth.users u ON fgm.user_id = u.id
    WHERE fgm.group_id = p_group_id
    ORDER BY fgm.joined_at ASC;
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER SET search_path = public;

COMMENT ON FUNCTION public.get_family_group_members(UUID) IS 'Returns all members of a family group. Only accessible to group members. SECURITY DEFINER with search_path protection.';

-- Fix respond_to_invitation function
CREATE OR REPLACE FUNCTION public.respond_to_invitation(
    p_invitation_id UUID,
    p_action TEXT
)
RETURNS JSONB AS $$
DECLARE
    v_current_user_id UUID;
    v_current_user_email TEXT;
    v_invitation RECORD;
    v_group_id UUID;
BEGIN
    -- Get current user
    v_current_user_id := auth.uid();
    
    IF v_current_user_id IS NULL THEN
        RAISE EXCEPTION 'User must be authenticated to respond to invitations.';
    END IF;
    
    -- Get current user's email
    SELECT email INTO v_current_user_email FROM auth.users WHERE id = v_current_user_id;
    
    -- Get invitation details
    SELECT * INTO v_invitation
    FROM public.family_group_invitations
    WHERE id = p_invitation_id;
    
    IF v_invitation IS NULL THEN
        RAISE EXCEPTION 'Invitation not found.';
    END IF;
    
    -- Check if invitation is still pending
    IF v_invitation.status != 'pending' THEN
        RAISE EXCEPTION 'Invitation is no longer pending. Current status: %', v_invitation.status;
    END IF;
    
    -- Check if invitation has expired
    IF v_invitation.expires_at < NOW() THEN
        RAISE EXCEPTION 'Invitation has expired.';
    END IF;
    
    -- Check if the current user's email matches the invitation
    IF v_invitation.invitee_email != v_current_user_email THEN
        RAISE EXCEPTION 'This invitation is not for your email address.';
    END IF;
    
    v_group_id := v_invitation.group_id;
    
    -- Check if user is already a member of the group
    IF EXISTS (
        SELECT 1 FROM public.family_group_members
        WHERE group_id = v_group_id AND user_id = v_current_user_id
    ) THEN
        RAISE EXCEPTION 'You are already a member of this group.';
    END IF;
    
    -- Process the action
    IF p_action = 'accept' THEN
        -- Add user to family_group_members
        INSERT INTO public.family_group_members (group_id, user_id, role)
        VALUES (v_group_id, v_current_user_id, 'member');

        -- Update invitation status
        UPDATE public.family_group_invitations
        SET status = 'accepted', responded_at = NOW()
        WHERE id = p_invitation_id;

        RETURN jsonb_build_object('success', true, 'message', 'Invitation accepted successfully. You are now a member of the group.');

    ELSIF p_action = 'decline' THEN
        -- Update invitation status
        UPDATE public.family_group_invitations
        SET status = 'declined', responded_at = NOW()
        WHERE id = p_invitation_id;

        RETURN jsonb_build_object('success', true, 'message', 'Invitation declined.');
    ELSE
        RAISE EXCEPTION 'Invalid action. Expected "accept" or "decline", got: %', p_action;
    END IF;
END;
$$ LANGUAGE plpgsql VOLATILE SECURITY DEFINER SET search_path = public;

COMMENT ON FUNCTION public.respond_to_invitation(UUID, TEXT) IS 'Allows a user to accept or decline a family group invitation. SECURITY DEFINER with search_path protection.';
