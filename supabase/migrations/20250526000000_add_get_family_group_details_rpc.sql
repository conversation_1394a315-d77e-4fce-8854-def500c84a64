-- RPC Function: get_family_group_details
-- Returns detailed information about a family group including its members
-- Only accessible to group members
CREATE OR REPLACE FUNCTION public.get_family_group_details(p_group_id UUID)
RETURNS JSONB AS $$
DECLARE
    v_user_id UUID := auth.uid();
    v_group_info RECORD;
    v_members JSONB;
BEGIN
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'User must be authenticated to view group details.';
    END IF;

    -- Check if user is a member of the group
    IF NOT EXISTS (
        SELECT 1 FROM public.family_group_members
        WHERE group_id = p_group_id AND user_id = v_user_id
    ) THEN
        RETURN NULL; -- User is not a member, return null (handled by client)
    END IF;

    -- Get group information
    SELECT 
        fg.id,
        fg.name,
        fg.created_at,
        fg.owner_user_id
    INTO v_group_info
    FROM public.family_groups fg
    WHERE fg.id = p_group_id;

    IF v_group_info IS NULL THEN
        RETURN NULL; -- Group not found
    END IF;

    -- Get group members with their details
    SELECT jsonb_agg(
        jsonb_build_object(
            'user_id', fgm.user_id,
            'email', u.email,
            'role', fgm.role,
            'joined_at', fgm.joined_at
        )
        ORDER BY fgm.joined_at ASC
    ) INTO v_members
    FROM public.family_group_members fgm
    JOIN auth.users u ON fgm.user_id = u.id
    WHERE fgm.group_id = p_group_id;

    -- Return the complete group details
    RETURN jsonb_build_object(
        'group_id', v_group_info.id,
        'group_name', v_group_info.name,
        'group_created_at', v_group_info.created_at,
        'owner_user_id', v_group_info.owner_user_id,
        'members', COALESCE(v_members, '[]'::jsonb)
    );
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER;

COMMENT ON FUNCTION public.get_family_group_details(UUID) IS 'Returns detailed information about a family group including its members. Only accessible to group members.';