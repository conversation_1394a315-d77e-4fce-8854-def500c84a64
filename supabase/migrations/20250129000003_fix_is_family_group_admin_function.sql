-- Migration: Fix is_family_group_admin function signature issue
-- The frontend is getting an error about the function not being found in the schema cache
-- This recreates the function to ensure it's properly registered

-- Just recreate the function with the correct signature (don't drop due to RLS dependencies)
CREATE OR REPLACE FUNCTION public.is_family_group_admin(p_group_id UUID, p_user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    is_admin BOOLEAN;
BEGIN
    SELECT EXISTS (
        SELECT 1
        FROM public.family_group_members
        WHERE group_id = p_group_id
          AND user_id = p_user_id
          AND role = 'admin'
    ) INTO is_admin;
    RETURN is_admin;
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER SET search_path = public;

COMMENT ON FUNCTION public.is_family_group_admin(UUID, UUID) IS 'Checks if a given user is an admin of a specific family group. SECURITY DEFINER with search_path protection.';

-- Ensure the get_invitation_by_id function matches the expected return type
CREATE OR REPLACE FUNCTION public.get_invitation_by_id(p_invitation_id UUID)
RETURNS TABLE (
    invitation_id UUID,
    group_id UUID,
    status TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        fgi.id AS invitation_id,
        fgi.group_id,
        fgi.status
    FROM public.family_group_invitations fgi
    WHERE fgi.id = p_invitation_id;
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER SET search_path = public;

COMMENT ON FUNCTION public.get_invitation_by_id(UUID) IS 'Returns basic invitation details by ID. Used for checking invitation existence and group membership before operations.'; 