-- Migration: Verify and fix family group columns
-- This migration ensures all financial tables have the required family_group_id columns

-- Check and add family_group_id to accounts table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'accounts' 
        AND column_name = 'family_group_id'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.accounts 
        ADD COLUMN family_group_id UUID REFERENCES public.family_groups(id) ON DELETE CASCADE;
        
        CREATE INDEX IF NOT EXISTS idx_accounts_family_group_id ON public.accounts(family_group_id);
    END IF;
END $$;

-- Check and add created_by_user_id to accounts table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'accounts' 
        AND column_name = 'created_by_user_id'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.accounts 
        ADD COLUMN created_by_user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL;
        
        CREATE INDEX IF NOT EXISTS idx_accounts_created_by_user_id ON public.accounts(created_by_user_id);
    END IF;
END $$;

-- Check and add family_group_id to categories table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'categories' 
        AND column_name = 'family_group_id'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.categories 
        ADD COLUMN family_group_id UUID REFERENCES public.family_groups(id) ON DELETE CASCADE;
        
        CREATE INDEX IF NOT EXISTS idx_categories_family_group_id ON public.categories(family_group_id);
    END IF;
END $$;

-- Check and add created_by_user_id to categories table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'categories' 
        AND column_name = 'created_by_user_id'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.categories 
        ADD COLUMN created_by_user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL;
        
        CREATE INDEX IF NOT EXISTS idx_categories_created_by_user_id ON public.categories(created_by_user_id);
    END IF;
END $$;

-- Check and add family_group_id to transactions table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'transactions' 
        AND column_name = 'family_group_id'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.transactions 
        ADD COLUMN family_group_id UUID REFERENCES public.family_groups(id) ON DELETE CASCADE;
        
        CREATE INDEX IF NOT EXISTS idx_transactions_family_group_id ON public.transactions(family_group_id);
    END IF;
END $$;

-- Check and add created_by_user_id to transactions table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'transactions' 
        AND column_name = 'created_by_user_id'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.transactions 
        ADD COLUMN created_by_user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL;
        
        CREATE INDEX IF NOT EXISTS idx_transactions_created_by_user_id ON public.transactions(created_by_user_id);
    END IF;
END $$;

-- Check and add family_group_id to budgets table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'budgets' 
        AND column_name = 'family_group_id'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.budgets 
        ADD COLUMN family_group_id UUID REFERENCES public.family_groups(id) ON DELETE CASCADE;
        
        CREATE INDEX IF NOT EXISTS idx_budgets_family_group_id ON public.budgets(family_group_id);
    END IF;
END $$;

-- Check and add created_by_user_id to budgets table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'budgets' 
        AND column_name = 'created_by_user_id'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.budgets 
        ADD COLUMN created_by_user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL;
        
        CREATE INDEX IF NOT EXISTS idx_budgets_created_by_user_id ON public.budgets(created_by_user_id);
    END IF;
END $$;

-- Check and add family_group_id to financial_goals table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'financial_goals' 
        AND column_name = 'family_group_id'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.financial_goals 
        ADD COLUMN family_group_id UUID REFERENCES public.family_groups(id) ON DELETE CASCADE;
        
        CREATE INDEX IF NOT EXISTS idx_financial_goals_family_group_id ON public.financial_goals(family_group_id);
    END IF;
END $$;

-- Check and add created_by_user_id to financial_goals table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'financial_goals' 
        AND column_name = 'created_by_user_id'
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE public.financial_goals 
        ADD COLUMN created_by_user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL;
        
        CREATE INDEX IF NOT EXISTS idx_financial_goals_created_by_user_id ON public.financial_goals(created_by_user_id);
    END IF;
END $$;

-- Update RLS policies to include family group access (recreate them to be safe)

-- Drop existing policies
DROP POLICY IF EXISTS "Users can manage their own accounts and family group accounts" ON public.accounts;
DROP POLICY IF EXISTS "Users can manage their own categories and family group categories" ON public.categories;
DROP POLICY IF EXISTS "Users can manage their own transactions and family group transactions" ON public.transactions;
DROP POLICY IF EXISTS "Users can manage their own budgets and family group budgets" ON public.budgets;
DROP POLICY IF EXISTS "Users can manage their own financial goals and family group goals" ON public.financial_goals;

-- Recreate policies with family group support
CREATE POLICY "Users can manage their own accounts and family group accounts" ON public.accounts
FOR ALL USING (
  auth.uid() = user_id OR 
  (family_group_id IS NOT NULL AND EXISTS (
    SELECT 1 FROM public.family_group_members fgm 
    WHERE fgm.group_id = accounts.family_group_id 
    AND fgm.user_id = auth.uid()
  ))
);

CREATE POLICY "Users can manage their own categories and family group categories" ON public.categories
FOR ALL USING (
  auth.uid() = user_id OR 
  (family_group_id IS NOT NULL AND EXISTS (
    SELECT 1 FROM public.family_group_members fgm 
    WHERE fgm.group_id = categories.family_group_id 
    AND fgm.user_id = auth.uid()
  ))
);

CREATE POLICY "Users can manage their own transactions and family group transactions" ON public.transactions
FOR ALL USING (
  auth.uid() = user_id OR 
  (family_group_id IS NOT NULL AND EXISTS (
    SELECT 1 FROM public.family_group_members fgm 
    WHERE fgm.group_id = transactions.family_group_id 
    AND fgm.user_id = auth.uid()
  ))
);

CREATE POLICY "Users can manage their own budgets and family group budgets" ON public.budgets
FOR ALL USING (
  auth.uid() = user_id OR 
  (family_group_id IS NOT NULL AND EXISTS (
    SELECT 1 FROM public.family_group_members fgm 
    WHERE fgm.group_id = budgets.family_group_id 
    AND fgm.user_id = auth.uid()
  ))
);

CREATE POLICY "Users can manage their own financial goals and family group goals" ON public.financial_goals
FOR ALL USING (
  auth.uid() = user_id OR 
  (family_group_id IS NOT NULL AND EXISTS (
    SELECT 1 FROM public.family_group_members fgm 
    WHERE fgm.group_id = financial_goals.family_group_id 
    AND fgm.user_id = auth.uid()
  ))
);