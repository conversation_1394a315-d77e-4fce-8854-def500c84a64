-- Update Transactions Table
DO $$
BEGIN
  -- Add missing columns if they don't exist
  BEGIN
    ALTER TABLE public.transactions ADD COLUMN IF NOT EXISTS icon TEXT;
  EXCEPTION WHEN others THEN
    -- Column might already exist or other error, continue
    RAISE NOTICE 'Column icon already exists or could not be added to transactions table';
  END;
  BEGIN
    ALTER TABLE public.transactions ADD COLUMN IF NOT EXISTS color TEXT;
  EXCEPTION WHEN others THEN
    -- Column might already exist or other error, continue
    RAISE NOTICE 'Column color already exists or could not be added to transactions table';
  END;
  BEGIN
    ALTER TABLE public.transactions ADD COLUMN IF NOT EXISTS category TEXT;
  EXCEPTION WHEN others THEN
    -- Column might already exist or other error, continue
    RAISE NOTICE 'Column category already exists or could not be added to transactions table';
  END;
  -- Ensure all necessary fields are present
  -- You can add more fields here if needed
END
$$;

COMMENT ON COLUMN public.transactions.icon IS 'Icon associated with the transaction for UI display';
COMMENT ON COLUMN public.transactions.color IS 'Color associated with the transaction for UI display';
COMMENT ON COLUMN public.transactions.category IS 'Category of the transaction';

-- Update Budgets Table
DO $$
BEGIN
  -- Add missing columns if they don't exist
  BEGIN
    ALTER TABLE public.budgets ADD COLUMN IF NOT EXISTS icon TEXT;
  EXCEPTION WHEN others THEN
    RAISE NOTICE 'Column icon already exists or could not be added to budgets table';
  END;
  BEGIN
    ALTER TABLE public.budgets ADD COLUMN IF NOT EXISTS color TEXT;
  EXCEPTION WHEN others THEN
    RAISE NOTICE 'Column color already exists or could not be added to budgets table';
  END;
  -- Ensure all necessary fields are present
  -- You can add more fields here if needed
END
$$;

COMMENT ON COLUMN public.budgets.icon IS 'Icon associated with the budget for UI display';
COMMENT ON COLUMN public.budgets.color IS 'Color associated with the budget for UI display';

-- Update Categories Table
DO $$
BEGIN
  -- Add missing columns if they don't exist
  BEGIN
    ALTER TABLE public.categories ADD COLUMN IF NOT EXISTS icon TEXT;
  EXCEPTION WHEN others THEN
    RAISE NOTICE 'Column icon already exists or could not be added to categories table';
  END;
  BEGIN
    ALTER TABLE public.categories ADD COLUMN IF NOT EXISTS color TEXT;
  EXCEPTION WHEN others THEN
    RAISE NOTICE 'Column color already exists or could not be added to categories table';
  END;
  -- Ensure all necessary fields are present
  -- You can add more fields here if needed
END
$$;

COMMENT ON COLUMN public.categories.icon IS 'Icon associated with the category for UI display';
COMMENT ON COLUMN public.categories.color IS 'Color associated with the category for UI display';

-- Update Financial Goals Table
DO $$
BEGIN
  -- Check if the table exists, if not create it
  IF NOT EXISTS (SELECT FROM pg_tables WHERE tablename = 'financial_goals') THEN
    CREATE TABLE public.financial_goals (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
      name TEXT NOT NULL,
      target_amount DECIMAL(19, 4) NOT NULL,
      current_amount DECIMAL(19, 4) DEFAULT 0.0,
      currency TEXT NOT NULL,
      deadline DATE,
      icon TEXT,
      color TEXT,
      is_active BOOLEAN DEFAULT true,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    
    -- Add RLS policy
    BEGIN
      IF NOT EXISTS (
        SELECT 1 
        FROM pg_policies 
        WHERE policyname = 'Users can manage their own goals' 
        AND tablename = 'financial_goals'
      ) THEN
        CREATE POLICY "Users can manage their own goals" ON public.financial_goals
          FOR ALL USING (auth.uid() = user_id);
      END IF;
    EXCEPTION WHEN others THEN
      RAISE NOTICE 'Policy for financial_goals already exists or could not be created';
    END;
  ELSE
    -- Add missing columns if they don't exist
    BEGIN
      ALTER TABLE public.financial_goals ADD COLUMN IF NOT EXISTS icon TEXT;
    EXCEPTION WHEN others THEN
      RAISE NOTICE 'Column icon already exists or could not be added to financial_goals table';
    END;
    BEGIN
      ALTER TABLE public.financial_goals ADD COLUMN IF NOT EXISTS color TEXT;
    EXCEPTION WHEN others THEN
      RAISE NOTICE 'Column color already exists or could not be added to financial_goals table';
    END;
    BEGIN
      ALTER TABLE public.financial_goals ADD COLUMN IF NOT EXISTS currency TEXT NOT NULL DEFAULT 'USD'; 
    EXCEPTION WHEN others THEN
      RAISE NOTICE 'Column currency already exists or could not be added to financial_goals table';
    END;
    BEGIN
      ALTER TABLE public.financial_goals ADD COLUMN IF NOT EXISTS deadline DATE;
    EXCEPTION WHEN others THEN
      RAISE NOTICE 'Column deadline already exists or could not be added to financial_goals table';
    END;
    BEGIN
      ALTER TABLE public.financial_goals ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true;
    EXCEPTION WHEN others THEN
      RAISE NOTICE 'Column is_active already exists or could not be added to financial_goals table';
    END;
    BEGIN
      ALTER TABLE public.financial_goals ADD COLUMN IF NOT EXISTS name TEXT NOT NULL DEFAULT 'Unnamed Goal';
    EXCEPTION WHEN others THEN
      RAISE NOTICE 'Column name already exists or could not be added to financial_goals table';
    END;
    BEGIN
      ALTER TABLE public.financial_goals ADD COLUMN IF NOT EXISTS target_amount DECIMAL(19, 4) NOT NULL DEFAULT 0;
    EXCEPTION WHEN others THEN
      RAISE NOTICE 'Column target_amount already exists or could not be added to financial_goals table';
    END;
    BEGIN
      ALTER TABLE public.financial_goals ADD COLUMN IF NOT EXISTS current_amount DECIMAL(19, 4) DEFAULT 0.0;
    EXCEPTION WHEN others THEN
      RAISE NOTICE 'Column current_amount already exists or could not be added to financial_goals table';
    END;
    BEGIN
      ALTER TABLE public.financial_goals ADD COLUMN IF NOT EXISTS created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
    EXCEPTION WHEN others THEN
      RAISE NOTICE 'Column created_at already exists or could not be added to financial_goals table';
    END;
    BEGIN
      ALTER TABLE public.financial_goals ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
    EXCEPTION WHEN others THEN
      RAISE NOTICE 'Column updated_at already exists or could not be added to financial_goals table';
    END;
    -- Ensure all necessary fields are present
    -- You can add more fields here if needed
  END IF;
END
$$;

COMMENT ON TABLE public.financial_goals IS 'Tracks financial goals for users';
COMMENT ON COLUMN public.financial_goals.icon IS 'Icon associated with the goal for UI display';
COMMENT ON COLUMN public.financial_goals.color IS 'Color associated with the goal for UI display';

-- Add currency column to budgets if not exists
DO $$ 
BEGIN
  ALTER TABLE public.budgets ADD COLUMN IF NOT EXISTS currency TEXT DEFAULT 'USD';
EXCEPTION WHEN others THEN
  -- Column already exists, do nothing
END;
$$;

-- Add currency column to financial_goals if not exists
DO $$ 
BEGIN
  ALTER TABLE public.financial_goals ADD COLUMN IF NOT EXISTS currency TEXT DEFAULT 'USD';
EXCEPTION WHEN others THEN
  -- Column already exists, do nothing
END;
$$;

-- Add currency column to transactions if not exists
DO $$ 
BEGIN
  ALTER TABLE public.transactions ADD COLUMN IF NOT EXISTS currency TEXT DEFAULT 'USD';
EXCEPTION WHEN others THEN
  -- Column already exists, do nothing
END;
$$;

-- Update Accounts Table
DO $$
BEGIN
  -- Add missing columns if they don't exist
  BEGIN
    ALTER TABLE public.accounts ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true;
  EXCEPTION WHEN others THEN
    RAISE NOTICE 'Column is_active already exists or could not be added to accounts table';
  END;
  BEGIN
    ALTER TABLE public.accounts ADD COLUMN IF NOT EXISTS icon TEXT;
  EXCEPTION WHEN others THEN
    RAISE NOTICE 'Column icon already exists or could not be added to accounts table';
  END;
  BEGIN
    ALTER TABLE public.accounts ADD COLUMN IF NOT EXISTS color TEXT;
  EXCEPTION WHEN others THEN
    RAISE NOTICE 'Column color already exists or could not be added to accounts table';
  END;
  -- Ensure all necessary fields are present
  -- You can add more fields here if needed
END
$$;

COMMENT ON COLUMN public.accounts.is_active IS 'Whether the account is active';
COMMENT ON COLUMN public.accounts.icon IS 'Icon associated with the account for UI display';
COMMENT ON COLUMN public.accounts.color IS 'Color associated with the account for UI display';

-- Update User Profiles (Settings) Table
DO $$
BEGIN
  -- Add missing columns if they don't exist
  BEGIN
    ALTER TABLE public.user_profiles ADD COLUMN IF NOT EXISTS full_name TEXT;
  EXCEPTION WHEN others THEN
    RAISE NOTICE 'Column full_name already exists or could not be added to user_profiles table';
  END;
  BEGIN
    ALTER TABLE public.user_profiles ADD COLUMN IF NOT EXISTS dark_mode BOOLEAN DEFAULT false;
  EXCEPTION WHEN others THEN
    RAISE NOTICE 'Column dark_mode already exists or could not be added to user_profiles table';
  END;
  BEGIN
    ALTER TABLE public.user_profiles ADD COLUMN IF NOT EXISTS notifications_enabled BOOLEAN DEFAULT true;
  EXCEPTION WHEN others THEN
    RAISE NOTICE 'Column notifications_enabled already exists or could not be added to user_profiles table';
  END;
  -- Ensure all necessary fields are present
  -- You can add more fields here if needed
END
$$;

COMMENT ON COLUMN public.user_profiles.full_name IS 'Full name of the user';
COMMENT ON COLUMN public.user_profiles.dark_mode IS 'User preference for dark mode UI';
COMMENT ON COLUMN public.user_profiles.notifications_enabled IS 'User preference for receiving notifications';

-- Create Analytics Table if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (SELECT FROM pg_tables WHERE tablename = 'analytics_events') THEN
    CREATE TABLE public.analytics_events (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
      event_type TEXT NOT NULL,
      event_data JSONB,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    
    -- Add RLS policy
    BEGIN
      IF NOT EXISTS (
        SELECT 1 
        FROM pg_policies 
        WHERE policyname = 'Users can only view their own analytics events' 
        AND tablename = 'analytics_events'
      ) THEN
        CREATE POLICY "Users can only view their own analytics events" ON public.analytics_events
          FOR SELECT USING (auth.uid() = user_id);
      END IF;
    EXCEPTION WHEN others THEN
      RAISE NOTICE 'Policy for analytics_events SELECT already exists or could not be created';
    END;
    BEGIN
      IF NOT EXISTS (
        SELECT 1 
        FROM pg_policies 
        WHERE policyname = 'Users can insert analytics events for themselves' 
        AND tablename = 'analytics_events'
      ) THEN
        CREATE POLICY "Users can insert analytics events for themselves" ON public.analytics_events
          FOR INSERT WITH CHECK (auth.uid() = user_id);
      END IF;
    EXCEPTION WHEN others THEN
      RAISE NOTICE 'Policy for analytics_events INSERT already exists or could not be created';
    END;
  END IF;
END
$$;

COMMENT ON TABLE public.analytics_events IS 'Stores user interaction and usage analytics data';
