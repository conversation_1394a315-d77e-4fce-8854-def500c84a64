-- Create stored procedure for getting pending invitations for a group
CREATE OR REPLACE FUNCTION public.get_group_pending_invitations(p_group_id UUID)
RETURNS TABLE(
  invitation_id UUID,
  group_id UUID,
  group_name TEXT,
  invited_by_user_id UUID,
  invited_by_email TEXT,
  invitee_email TEXT,
  status TEXT,
  token TEXT,
  expires_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_user_id UUID;
  v_is_admin BOOLEAN;
BEGIN
  -- Get the current user ID
  v_user_id := auth.uid();
  
  -- Check if the user is an admin of the group
  SELECT EXISTS (
    SELECT 1 FROM family_group_members 
    WHERE group_id = p_group_id AND user_id = v_user_id AND role = 'admin'
  ) INTO v_is_admin;
  
  -- If not an admin, return empty result
  IF NOT v_is_admin THEN
    RAISE EXCEPTION 'Only group admins can view pending invitations';
  END IF;
  
  -- Return the pending invitations with all required information
  RETURN QUERY
  SELECT 
    fgi.id AS invitation_id,
    fgi.group_id,
    fg.name AS group_name,
    fgi.invited_by_user_id,
    up.email AS invited_by_email,
    fgi.invitee_email,
    fgi.status,
    fgi.token,
    fgi.expires_at,
    fgi.created_at
  FROM 
    family_group_invitations fgi
  JOIN 
    family_groups fg ON fgi.group_id = fg.id
  LEFT JOIN 
    user_profiles up ON fgi.invited_by_user_id = up.id
  WHERE 
    fgi.group_id = p_group_id AND
    fgi.status = 'pending';
    
  RETURN;
END;
$$;

-- Create stored procedure for cancelling an invitation
CREATE OR REPLACE FUNCTION public.cancel_invitation(p_invitation_id UUID)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_user_id UUID;
  v_group_id UUID;
  v_is_admin BOOLEAN;
BEGIN
  -- Get the current user ID
  v_user_id := auth.uid();
  
  -- Get the group ID for the invitation
  SELECT group_id INTO v_group_id
  FROM family_group_invitations
  WHERE id = p_invitation_id;
  
  IF v_group_id IS NULL THEN
    RAISE EXCEPTION 'Invitation not found';
  END IF;
  
  -- Check if the user is an admin of the group
  SELECT EXISTS (
    SELECT 1 FROM family_group_members 
    WHERE group_id = v_group_id AND user_id = v_user_id AND role = 'admin'
  ) INTO v_is_admin;
  
  -- If not an admin, return error
  IF NOT v_is_admin THEN
    RAISE EXCEPTION 'Only group admins can cancel invitations';
  END IF;
  
  -- Update the invitation status to cancelled
  UPDATE family_group_invitations
  SET status = 'cancelled'
  WHERE id = p_invitation_id AND status = 'pending';
  
  RETURN;
END;
$$;
