-- Migration: Fix all remaining SECURITY DEFINER functions to include SET search_path
-- This ensures all functions mentioned in the security warnings are properly secured

-- Create new get_pending_invitations_for_user function with search path
CREATE OR REPLACE FUNCTION public.get_pending_invitations_for_user_new()
RETURNS TABLE (
    invitation_id UUID,
    group_id UUID,
    group_name TEXT,
    invited_by_user_id UUID,
    invited_by_user_email character varying(255),
    invited_at TIMESTAMPTZ,
    invitation_status TEXT
) AS $$
DECLARE
    v_user_email TEXT;
BEGIN
    -- Get the current user's email
    SELECT email INTO v_user_email FROM auth.users WHERE id = auth.uid();

    IF v_user_email IS NULL THEN
        RAISE EXCEPTION 'User must be authenticated to view pending invitations.';
    END IF;

    RETURN QUERY
    SELECT
        fgi.id AS invitation_id,
        fgi.group_id,
        fg.name AS group_name,
        fgi.invited_by_user_id,
        inviter.email AS invited_by_user_email,
        fgi.created_at AS invited_at,
        fgi.status AS invitation_status
    FROM public.family_group_invitations fgi
    JOIN public.family_groups fg ON fgi.group_id = fg.id
    LEFT JOIN auth.users inviter ON fgi.invited_by_user_id = inviter.id
    WHERE fgi.invitee_email = v_user_email AND fgi.status = 'pending'
    ORDER BY fgi.created_at DESC;
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER SET search_path = public;

-- Fix get_pending_invitations_for_group function
CREATE OR REPLACE FUNCTION public.get_pending_invitations_for_group(p_group_id UUID)
RETURNS TABLE(
  invitation_id UUID,
  group_id UUID,
  group_name TEXT,
  invited_by_user_id UUID,
  invited_by_email TEXT,
  invitee_email TEXT,
  token TEXT,
  expires_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ
) AS $$
DECLARE
  v_user_id UUID;
  v_is_admin BOOLEAN;
BEGIN
  v_user_id := auth.uid();

  -- Check if the user is an admin of the group
  SELECT EXISTS (
    SELECT 1 FROM public.family_group_members
    WHERE group_id = p_group_id AND user_id = v_user_id AND role = 'admin'
  ) INTO v_is_admin;

  IF NOT v_is_admin THEN
    RAISE EXCEPTION 'Only group admins can view pending invitations';
  END IF;

  RETURN QUERY
  SELECT
    fgi.id AS invitation_id,
    fgi.group_id,
    fg.name AS group_name,
    fgi.invited_by_user_id,
    inviter.email AS invited_by_email,
    fgi.invitee_email,
    fgi.token,
    fgi.expires_at,
    fgi.created_at
  FROM public.family_group_invitations fgi
  JOIN public.family_groups fg ON fgi.group_id = fg.id
  LEFT JOIN auth.users inviter ON fgi.invited_by_user_id = inviter.id
  WHERE fgi.group_id = p_group_id AND fgi.status = 'pending'
  ORDER BY fgi.created_at DESC;
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER SET search_path = public;

-- Fix cancel_family_group_invitation function
CREATE OR REPLACE FUNCTION public.cancel_family_group_invitation(p_invitation_id UUID)
RETURNS VOID AS $$
DECLARE
  v_user_id UUID;
  v_invitation_group_id UUID;
  v_is_admin BOOLEAN;
BEGIN
  v_user_id := auth.uid();

  -- Get the group_id of the invitation to check admin privileges
  SELECT fgi.group_id INTO v_invitation_group_id
  FROM public.family_group_invitations AS fgi
  WHERE fgi.id = p_invitation_id;

  IF v_invitation_group_id IS NULL THEN
    RAISE EXCEPTION 'Invitation not found (ID: %)', p_invitation_id;
  END IF;

  -- Check if the user is an admin of the group
  SELECT EXISTS (
    SELECT 1 FROM public.family_group_members AS fgm
    WHERE fgm.group_id = v_invitation_group_id AND fgm.user_id = v_user_id AND fgm.role = 'admin'
  ) INTO v_is_admin;

  IF NOT v_is_admin THEN
    RAISE EXCEPTION 'Only group admins can cancel invitations';
  END IF;

  -- Update the invitation status to cancelled
  UPDATE public.family_group_invitations
  SET status = 'cancelled'
  WHERE id = p_invitation_id AND status = 'pending';

  IF NOT FOUND THEN
    RAISE EXCEPTION 'Invitation not found, already processed, or not in pending status';
  END IF;
END;
$$ LANGUAGE plpgsql VOLATILE SECURITY DEFINER SET search_path = public;

-- Fix get_invitation_by_token function
CREATE OR REPLACE FUNCTION public.get_invitation_by_token(p_token TEXT)
RETURNS TABLE (
    id UUID,
    group_id UUID,
    group_name TEXT,
    invited_by_email TEXT,
    invitee_email TEXT,
    expires_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ,
    status TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        fgi.id,
        fgi.group_id,
        fg.name AS group_name,
        inviter.email AS invited_by_email,
        fgi.invitee_email,
        fgi.expires_at,
        fgi.created_at,
        fgi.status
    FROM public.family_group_invitations fgi
    JOIN public.family_groups fg ON fgi.group_id = fg.id
    LEFT JOIN auth.users inviter ON fgi.invited_by_user_id = inviter.id
    WHERE fgi.token = p_token;
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER SET search_path = public;

-- Fix remove_member_from_family_group function
CREATE OR REPLACE FUNCTION public.remove_member_from_family_group(p_group_id UUID, p_user_to_remove_id UUID)
RETURNS JSONB AS $$
DECLARE
    v_remover_id UUID := auth.uid();
    v_group_owner_id UUID;
    v_admin_count INTEGER;
BEGIN
    IF v_remover_id IS NULL THEN
        RAISE EXCEPTION 'User must be authenticated to remove members.';
    END IF;

    IF NOT public.is_family_group_admin(p_group_id, v_remover_id) THEN
        RAISE EXCEPTION 'Only admin members can remove users from the group.';
    END IF;

    IF v_remover_id = p_user_to_remove_id THEN
        RAISE EXCEPTION 'Admin cannot use this function to remove themselves. Use leave_family_group instead.';
    END IF;

    -- Check if the user to remove is the owner
    SELECT owner_user_id INTO v_group_owner_id FROM public.family_groups WHERE id = p_group_id;

    IF p_user_to_remove_id = v_group_owner_id THEN
        -- Count other admins
        SELECT count(*) INTO v_admin_count
        FROM public.family_group_members
        WHERE group_id = p_group_id AND role = 'admin' AND user_id <> p_user_to_remove_id;

        IF v_admin_count = 0 THEN
            RAISE EXCEPTION 'Cannot remove the group owner as they are the only admin. Transfer ownership or promote another admin first.';
        END IF;
    END IF;

    DELETE FROM public.family_group_members
    WHERE group_id = p_group_id AND user_id = p_user_to_remove_id;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'User to remove was not found in this group or already removed.';
    END IF;

    RETURN jsonb_build_object('status', 'success', 'message', 'User removed from group successfully.');
END;
$$ LANGUAGE plpgsql VOLATILE SECURITY DEFINER SET search_path = public;

-- Fix leave_family_group function
CREATE OR REPLACE FUNCTION public.leave_family_group(p_group_id UUID)
RETURNS JSONB AS $$
DECLARE
    v_leaver_id UUID := auth.uid();
    v_group_owner_id UUID;
    v_admin_count INTEGER;
    v_member_count INTEGER;
BEGIN
    IF v_leaver_id IS NULL THEN
        RAISE EXCEPTION 'User must be authenticated to leave a group.';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM public.family_group_members WHERE group_id = p_group_id AND user_id = v_leaver_id) THEN
        RAISE EXCEPTION 'You are not a member of this group.';
    END IF;

    SELECT owner_user_id INTO v_group_owner_id FROM public.family_groups WHERE id = p_group_id;

    -- Check if the leaver is the owner
    IF v_leaver_id = v_group_owner_id THEN
        -- Count total members
        SELECT count(*) INTO v_member_count FROM public.family_group_members WHERE group_id = p_group_id;

        IF v_member_count > 1 THEN
            -- Count other admins
            SELECT count(*) INTO v_admin_count
            FROM public.family_group_members
            WHERE group_id = p_group_id AND role = 'admin' AND user_id <> v_leaver_id;

            IF v_admin_count = 0 THEN
                RAISE EXCEPTION 'As the group owner, you cannot leave unless you transfer ownership or promote another admin first.';
            END IF;
        END IF;
    END IF;

    DELETE FROM public.family_group_members WHERE group_id = p_group_id AND user_id = v_leaver_id;

    -- If the owner left and there are no members left, delete the group
    SELECT count(*) INTO v_member_count FROM public.family_group_members WHERE group_id = p_group_id;
    IF v_member_count = 0 THEN
        DELETE FROM public.family_groups WHERE id = p_group_id;
        RETURN jsonb_build_object('status', 'success', 'message', 'You left the group. The group has been deleted as it had no remaining members.');
    END IF;

    RETURN jsonb_build_object('status', 'success', 'message', 'You have successfully left the group.');
END;
$$ LANGUAGE plpgsql VOLATILE SECURITY DEFINER SET search_path = public;

-- Swap the functions
DROP FUNCTION IF EXISTS public.get_pending_invitations_for_user() CASCADE;
ALTER FUNCTION public.get_pending_invitations_for_user_new() RENAME TO get_pending_invitations_for_user;

-- Add comments for all functions
COMMENT ON FUNCTION public.get_pending_invitations_for_user() IS 'Returns pending invitations for the current user based on their email. SECURITY DEFINER with search_path protection.';
COMMENT ON FUNCTION public.get_pending_invitations_for_group(UUID) IS 'Returns pending invitations for a specific group. Only accessible to group admins. SECURITY DEFINER with search_path protection.';
COMMENT ON FUNCTION public.cancel_family_group_invitation(UUID) IS 'Cancels a family group invitation. Only accessible to group admins. SECURITY DEFINER with search_path protection.';
COMMENT ON FUNCTION public.get_invitation_by_token(TEXT) IS 'Returns invitation details by token for the invitation acceptance page. SECURITY DEFINER with search_path protection.';
COMMENT ON FUNCTION public.remove_member_from_family_group(UUID, UUID) IS 'Removes a member from a family group. Only accessible to group admins. SECURITY DEFINER with search_path protection.';
COMMENT ON FUNCTION public.leave_family_group(UUID) IS 'Allows a user to leave a family group. SECURITY DEFINER with search_path protection.';
