-- Update the handle_new_user function to include email and full_name
-- and ensure it correctly sources all data for user_profiles.

CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = public -- Ensure it runs with elevated privileges and correct schema context
AS $$
BEGIN
  INSERT INTO public.user_profiles (id, email, full_name, username, avatar_url, currency, language)
  VALUES (
    NEW.id,
    NEW.email, -- Directly use the email from auth.users
    NEW.raw_user_meta_data->>'full_name',
    NEW.raw_user_meta_data->>'username',
    NEW.raw_user_meta_data->>'avatar_url', -- Keep existing logic for avatar
    COALESCE(NEW.raw_user_meta_data->>'currency', 'USD'), -- Default currency
    COALESCE(NEW.raw_user_meta_data->>'language', 'en')    -- Default language
  )
  ON CONFLICT (id) DO NOTHING; -- In case the profile somehow already exists, do nothing.
  RETURN NEW;
END;
$$;

-- Recreate the trigger to ensure it uses the updated function.
-- This might be redundant if the trigger definition hasn't changed,
-- but it's good practice to ensure it's linked to the newest function version.
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

COMMENT ON FUNCTION public.handle_new_user() IS 'Handles creating a user profile upon new user signup, now includes email and full_name directly.';