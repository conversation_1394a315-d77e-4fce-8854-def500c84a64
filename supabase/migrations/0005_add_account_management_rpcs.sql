-- Create insert_account RPC function
CREATE OR REPLACE FUNCTION public.insert_account(p_data jsonb)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  INSERT INTO public.accounts (
    user_id, 
    name, 
    type, 
    balance, 
    currency, 
    is_active
  )
  VALUES (
    (p_data->>'user_id')::uuid,
    p_data->>'name',
    p_data->>'type',
    (p_data->>'balance')::decimal,
    p_data->>'currency',
    (p_data->>'is_active')::boolean
  );
END;
$$;

COMMENT ON FUNCTION public.insert_account(jsonb) IS 'Inserts a new account for the user based on provided JSON data.';

-- Create update_account RPC function
CREATE OR REPLACE FUNCTION public.update_account(p_id uuid, p_data jsonb)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  UPDATE public.accounts
  SET
    name = p_data->>'name',
    type = p_data->>'type',
    balance = (p_data->>'balance')::decimal,
    currency = p_data->>'currency',
    is_active = (p_data->>'is_active')::boolean,
    updated_at = NOW()
  WHERE id = p_id AND user_id = auth.uid();
END;
$$;

COMMENT ON FUNCTION public.update_account(uuid, jsonb) IS 'Updates an existing account for the authenticated user based on provided ID and JSON data.';

-- Create delete_account RPC function
CREATE OR REPLACE FUNCTION public.delete_account(p_id uuid)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  DELETE FROM public.accounts
  WHERE id = p_id AND user_id = auth.uid();
END;
$$;

COMMENT ON FUNCTION public.delete_account(uuid) IS 'Deletes an account for the authenticated user based on provided ID.';
