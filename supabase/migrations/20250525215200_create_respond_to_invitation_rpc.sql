DROP FUNCTION IF EXISTS public.respond_to_invitation(UUI<PERSON>, TEXT);
CREATE OR REPLACE FUNCTION public.respond_to_invitation(
    p_invitation_id UUID,
    p_action TEXT -- Expected 'accept' or 'decline'
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_current_user_id UUID := auth.uid();
    v_invitation RECORD;
    v_group_id UUID;
    v_user_email TEXT;
    v_normalized_user_email TEXT;
    v_is_already_member BOOLEAN;
BEGIN
    -- Validate p_action
    IF p_action NOT IN ('accept', 'decline') THEN
        RETURN jsonb_build_object('success', false, 'message', 'Invalid action specified.');
    END IF;

    -- Get current user's email
    SELECT email INTO v_user_email FROM auth.users WHERE id = v_current_user_id;
    IF v_user_email IS NULL THEN
         RETURN jsonb_build_object('success', false, 'message', 'User not found.');
    END IF;

    -- Normalize the current user's email (lowercase and remove +alias)
    v_normalized_user_email := regexp_replace(lower(v_user_email), '\+[^@]*(@.*)', '\1');

    -- Fetch the invitation
    SELECT * INTO v_invitation
    FROM public.family_group_invitations
    WHERE id = p_invitation_id AND regexp_replace(lower(invitee_email), '\+[^@]*(@.*)', '\1') = v_normalized_user_email AND status = 'pending';

    IF NOT FOUND THEN
        RETURN jsonb_build_object('success', false, 'message', 'Invitation not found, already processed, or not for this user.');
    END IF;

    v_group_id := v_invitation.group_id;

    IF p_action = 'accept' THEN
        -- Check if user is already a member of the group
        SELECT EXISTS (
            SELECT 1 FROM public.family_group_members
            WHERE group_id = v_group_id AND user_id = v_current_user_id
        ) INTO v_is_already_member;

        IF v_is_already_member THEN
            -- Update invitation status to 'accepted' (or 'ignored_already_member') even if already a member
            UPDATE public.family_group_invitations
            SET status = 'accepted' -- Or a more specific status
            WHERE id = p_invitation_id;
            RETURN jsonb_build_object('success', false, 'message', 'You are already a member of this group.');
        END IF;

        -- Add user to family_group_members
        INSERT INTO public.family_group_members (group_id, user_id, role)
        VALUES (v_group_id, v_current_user_id, 'member'); -- Default role 'member'

        -- Update invitation status
        UPDATE public.family_group_invitations
        SET status = 'accepted', responded_at = NOW()
        WHERE id = p_invitation_id;

        RETURN jsonb_build_object('success', true, 'message', 'Invitation accepted successfully. You are now a member of the group.');

    ELSIF p_action = 'decline' THEN
        -- Update invitation status
        UPDATE public.family_group_invitations
        SET status = 'declined', responded_at = NOW()
        WHERE id = p_invitation_id;

        RETURN jsonb_build_object('success', true, 'message', 'Invitation declined.');
    END IF;

    -- Should not be reached
    RETURN jsonb_build_object('success', false, 'message', 'An unexpected error occurred.');
END;
$$;
