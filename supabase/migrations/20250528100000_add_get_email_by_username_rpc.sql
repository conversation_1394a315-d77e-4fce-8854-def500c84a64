CREATE OR REPLACE FUNCTION public.get_email_by_username(p_username TEXT)
RETURNS TABLE (email TEXT)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN QUERY
  SELECT u.email
  FROM auth.users u
  JOIN public.user_profiles up ON u.id = up.id
  WHERE up.username = p_username;
END;
$$;

COMMENT ON FUNCTION public.get_email_by_username(TEXT) IS 'Securely retrieves a user''s email by their username. Intended for use in login flows.';