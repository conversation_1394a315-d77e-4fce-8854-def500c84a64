-- Add missing columns to user_profiles table
ALTER TABLE public.user_profiles
ADD COLUMN IF NOT EXISTS full_name TEXT,
ADD COLUMN IF NOT EXISTS dark_mode BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS notifications_enabled BOOLEAN DEFAULT true;

COMMENT ON COLUMN public.user_profiles.full_name IS 'User''s full name';
COMMENT ON COLUMN public.user_profiles.dark_mode IS 'User preference for dark mode interface';
COMMENT ON COLUMN public.user_profiles.notifications_enabled IS 'User preference for receiving notifications';
