-- Migration: Fix ambiguous group_id reference in get_pending_invitations_for_group function
-- This fixes the "column reference group_id is ambiguous" error

-- Fix get_pending_invitations_for_group function with proper table aliases
CREATE OR REPLACE FUNCTION public.get_pending_invitations_for_group(p_group_id UUID)
RETURNS TABLE(
  invitation_id UUID,
  group_id UUID,
  group_name TEXT,
  invited_by_user_id UUID,
  invited_by_email TEXT,
  invitee_email TEXT,
  token TEXT,
  expires_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ
) AS $$
DECLARE
  v_user_id UUID;
  v_is_admin BOOLEAN;
BEGIN
  v_user_id := auth.uid();
  
  -- Check if the user is an admin of the group (with proper table alias)
  SELECT EXISTS (
    SELECT 1 FROM public.family_group_members fgm
    WHERE fgm.group_id = p_group_id AND fgm.user_id = v_user_id AND fgm.role = 'admin'
  ) INTO v_is_admin;
  
  IF NOT v_is_admin THEN
    RAISE EXCEPTION 'Only group admins can view pending invitations';
  END IF;
  
  RETURN QUERY
  SELECT
    fgi.id AS invitation_id,
    fgi.group_id,
    fg.name AS group_name,
    fgi.invited_by_user_id,
    inviter.email AS invited_by_email,
    fgi.invitee_email,
    fgi.token,
    fgi.expires_at,
    fgi.created_at
  FROM public.family_group_invitations fgi
  JOIN public.family_groups fg ON fgi.group_id = fg.id
  LEFT JOIN auth.users inviter ON fgi.invited_by_user_id = inviter.id
  WHERE fgi.group_id = p_group_id AND fgi.status = 'pending'
  ORDER BY fgi.created_at DESC;
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER SET search_path = public;

-- Also fix any other functions that might have similar issues
-- Fix cancel_family_group_invitation function with proper table aliases
CREATE OR REPLACE FUNCTION public.cancel_family_group_invitation(p_invitation_id UUID)
RETURNS VOID AS $$
DECLARE
  v_user_id UUID;
  v_invitation_group_id UUID;
  v_is_admin BOOLEAN;
BEGIN
  v_user_id := auth.uid();

  -- Get the group_id of the invitation to check admin privileges
  SELECT fgi.group_id INTO v_invitation_group_id
  FROM public.family_group_invitations AS fgi
  WHERE fgi.id = p_invitation_id;

  IF v_invitation_group_id IS NULL THEN
    RAISE EXCEPTION 'Invitation not found (ID: %)', p_invitation_id;
  END IF;

  -- Check if the user is an admin of the group (with proper table alias)
  SELECT EXISTS (
    SELECT 1 FROM public.family_group_members AS fgm
    WHERE fgm.group_id = v_invitation_group_id AND fgm.user_id = v_user_id AND fgm.role = 'admin'
  ) INTO v_is_admin;

  IF NOT v_is_admin THEN
    RAISE EXCEPTION 'Only group admins can cancel invitations';
  END IF;

  -- Update the invitation status to cancelled
  UPDATE public.family_group_invitations
  SET status = 'cancelled'
  WHERE id = p_invitation_id AND status = 'pending';

  IF NOT FOUND THEN
    RAISE EXCEPTION 'Invitation not found, already processed, or not in pending status';
  END IF;
END;
$$ LANGUAGE plpgsql VOLATILE SECURITY DEFINER SET search_path = public;

-- Add comments
COMMENT ON FUNCTION public.get_pending_invitations_for_group(UUID) IS 'Returns pending invitations for a specific group. Only accessible to group admins. Fixed ambiguous column reference.';
COMMENT ON FUNCTION public.cancel_family_group_invitation(UUID) IS 'Cancels a family group invitation. Only accessible to group admins. Fixed ambiguous column reference.';
