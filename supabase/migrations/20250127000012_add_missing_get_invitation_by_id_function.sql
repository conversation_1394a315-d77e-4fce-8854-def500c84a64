-- Migration: Add missing get_invitation_by_id function
-- This function is needed by the cancelInvitation function in the frontend

-- Create get_invitation_by_id function
CREATE OR REPLACE FUNCTION public.get_invitation_by_id(p_invitation_id UUID)
RETURNS TABLE (
    invitation_id UUID,
    group_id UUID,
    status TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        fgi.id AS invitation_id,
        fgi.group_id,
        fgi.status
    FROM public.family_group_invitations fgi
    WHERE fgi.id = p_invitation_id;
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER SET search_path = public;

COMMENT ON FUNCTION public.get_invitation_by_id(UUID) IS 'Returns basic invitation details by ID. Used for checking invitation existence and group membership before operations.';
