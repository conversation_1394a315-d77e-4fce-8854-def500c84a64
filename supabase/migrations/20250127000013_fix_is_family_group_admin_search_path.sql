-- Migration: Fix is_family_group_admin function to include search path security
-- This function is used by many other functions and RLS policies

-- Update is_family_group_admin function with search path security
CREATE OR REPLACE FUNCTION public.is_family_group_admin(p_group_id UUID, p_user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    is_admin BOOLEAN;
BEGIN
    SELECT EXISTS (
        SELECT 1
        FROM public.family_group_members
        WHERE group_id = p_group_id
          AND user_id = p_user_id
          AND role = 'admin'
    ) INTO is_admin;
    RETURN is_admin;
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER SET search_path = public;

COMMENT ON FUNCTION public.is_family_group_admin(UUID, UUID) IS 'Checks if a given user is an admin of a specific family group. SECURITY DEFINER with search_path protection.';
