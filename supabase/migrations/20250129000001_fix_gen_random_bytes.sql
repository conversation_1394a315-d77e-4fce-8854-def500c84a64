-- Migration: Fix gen_random_bytes function call in generate_secure_token
-- The issue is that gen_random_bytes is in the extensions schema but the function
-- has SET search_path = public, so it can't find the function.

-- Ensure pgcrypto extension is available
CREATE EXTENSION IF NOT EXISTS pgcrypto WITH SCHEMA extensions;

-- Update generate_secure_token function to properly reference extensions.gen_random_bytes
CREATE OR REPLACE FUNCTION public.generate_secure_token()
RETURNS TEXT AS $$
BEGIN
    RETURN encode(extensions.gen_random_bytes(16), 'hex'); -- Generates a 32-character hex token
END;
$$ LANGUAGE plpgsql VOLATILE SECURITY DEFINER SET search_path = public;

COMMENT ON FUNCTION public.generate_secure_token() IS 'Generates a secure random token for invitations using pgcrypto extension.'; 