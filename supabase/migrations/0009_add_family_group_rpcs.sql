-- Migration: Add RPC functions for Family Groups feature

-- Helper function to generate secure random tokens
CREATE OR REPLACE FUNCTION public.generate_secure_token()
RETURNS TEXT AS $$
BEGIN
    RETURN encode(gen_random_bytes(16), 'hex'); -- Generates a 32-character hex token
END;
$$ LANGUAGE plpgsql VOLATILE;

COMMENT ON FUNCTION public.generate_secure_token() IS 'Generates a secure random token for invitations.';

-- Helper function to check if a user is an admin of a group
CREATE OR REPLACE FUNCTION public.is_family_group_admin(p_group_id UUID, p_user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    is_admin BOOLEAN;
BEGIN
    SELECT EXISTS (
        SELECT 1
        FROM public.family_group_members
        WHERE group_id = p_group_id
          AND user_id = p_user_id
          AND role = 'admin'
    ) INTO is_admin;
    RETURN is_admin;
END;
$$ LANGUAGE plpgsql STABLE SECURITY INVOKER; -- SECURITY INVOKER is fine as it relies on RLS of family_group_members

COMMENT ON FUNCTION public.is_family_group_admin(UUID, UUID) IS 'Checks if a given user is an admin of a specific family group.';

-- Helper function: Check if a user is a member of a group (SECURITY DEFINER)
CREATE OR REPLACE FUNCTION public.is_family_group_member_sd(p_group_id UUID, p_user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1
        FROM public.family_group_members
        WHERE group_id = p_group_id
          AND user_id = p_user_id
    );
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER;

COMMENT ON FUNCTION public.is_family_group_member_sd(UUID, UUID) IS 'Checks if a given user is a member of a specific family group. SECURITY DEFINER to be safely used in RLS policies.';

-- RPC Function: create_family_group
CREATE OR REPLACE FUNCTION public.create_family_group(p_group_name TEXT)
RETURNS UUID AS $$
DECLARE
    v_user_id UUID := auth.uid();
    v_group_id UUID;
BEGIN
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'User must be authenticated to create a group.';
    END IF;

    IF p_group_name IS NULL OR trim(p_group_name) = '' THEN
        RAISE EXCEPTION 'Group name cannot be empty.';
    END IF;

    -- Create the group
    INSERT INTO public.family_groups (name, owner_user_id)
    VALUES (p_group_name, v_user_id)
    RETURNING id INTO v_group_id;

    -- Add the creator as an admin member
    INSERT INTO public.family_group_members (group_id, user_id, role)
    VALUES (v_group_id, v_user_id, 'admin');

    RETURN v_group_id;
END;
$$ LANGUAGE plpgsql VOLATILE SECURITY DEFINER;

COMMENT ON FUNCTION public.create_family_group(TEXT) IS 'Creates a new family group and adds the creator as an admin member. Returns the new group ID.';

-- RPC Function: invite_to_family_group
CREATE OR REPLACE FUNCTION public.invite_to_family_group(p_group_id UUID, p_invitee_email TEXT)
RETURNS JSONB AS $$
DECLARE
    v_inviter_id UUID := auth.uid();
    v_invitation_id UUID;
    v_token TEXT;
    v_expires_at TIMESTAMPTZ;
BEGIN
    IF v_inviter_id IS NULL THEN
        RAISE EXCEPTION 'User must be authenticated to invite members.';
    END IF;

    IF NOT public.is_family_group_admin(p_group_id, v_inviter_id) THEN
        RAISE EXCEPTION 'Only admin members can invite new users to the group.';
    END IF;

    IF p_invitee_email IS NULL OR trim(p_invitee_email) = '' THEN
        RAISE EXCEPTION 'Invitee email cannot be empty.';
    END IF;
    
    -- Check if user is already a member
    IF EXISTS (
        SELECT 1 FROM auth.users u
        JOIN public.family_group_members fgm ON u.id = fgm.user_id
        WHERE u.email = p_invitee_email AND fgm.group_id = p_group_id
    ) THEN
        RAISE EXCEPTION 'User with email % is already a member of this group.', p_invitee_email;
    END IF;

    -- Check for existing pending invitation for this email and group
    IF EXISTS (
        SELECT 1 FROM public.family_group_invitations
        WHERE group_id = p_group_id
          AND invitee_email = p_invitee_email
          AND status = 'pending'
          AND expires_at > now()
    ) THEN
        RAISE EXCEPTION 'An active pending invitation already exists for this email and group.';
    END IF;

    v_token := public.generate_secure_token();
    v_expires_at := now() + INTERVAL '7 days';

    INSERT INTO public.family_group_invitations (group_id, invited_by_user_id, invitee_email, token, expires_at, status)
    VALUES (p_group_id, v_inviter_id, p_invitee_email, v_token, v_expires_at, 'pending')
    RETURNING id INTO v_invitation_id;

    -- Return invitation details including the invitation link
    RETURN jsonb_build_object(
        'invitation_id', v_invitation_id,
        'token', v_token,
        'invitation_link', format('https://your-domain.com/invite/%s', v_token),
        'expires_at', v_expires_at
    );
END;
$$ LANGUAGE plpgsql VOLATILE SECURITY DEFINER;

COMMENT ON FUNCTION public.invite_to_family_group(UUID, TEXT) IS 'Allows an admin of a group to invite a new member by email. Creates an invitation record and returns invitation details including the invitation link.';

-- RPC Function: accept_family_group_invitation
CREATE OR REPLACE FUNCTION public.accept_family_group_invitation(p_invitation_token TEXT)
RETURNS JSONB AS $$
DECLARE
    v_user_id UUID := auth.uid();
    v_invitation RECORD;
    v_group_id UUID;
    v_user_email TEXT;
BEGIN
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'User must be authenticated to accept an invitation.';
    END IF;

    SELECT email INTO v_user_email FROM auth.users WHERE id = v_user_id;

    SELECT * INTO v_invitation
    FROM public.family_group_invitations
    WHERE token = p_invitation_token;

    IF v_invitation IS NULL THEN
        RAISE EXCEPTION 'Invitation token is invalid.';
    END IF;

    IF v_invitation.invitee_email IS DISTINCT FROM v_user_email THEN
        RAISE EXCEPTION 'This invitation is not for the currently authenticated user.';
    END IF;

    IF v_invitation.status <> 'pending' THEN
        RAISE EXCEPTION 'This invitation is no longer pending (current status: %).', v_invitation.status;
    END IF;

    IF v_invitation.expires_at <= now() THEN
        -- Optionally update status to 'expired'
        UPDATE public.family_group_invitations
        SET status = 'expired'
        WHERE id = v_invitation.id;
        RAISE EXCEPTION 'This invitation has expired.';
    END IF;
    
    v_group_id := v_invitation.group_id;

    -- Check if user is already a member (should not happen if invite was created properly, but good for robustness)
    IF EXISTS (SELECT 1 FROM public.family_group_members WHERE group_id = v_group_id AND user_id = v_user_id) THEN
        -- Update invitation status and inform
        UPDATE public.family_group_invitations SET status = 'accepted' WHERE id = v_invitation.id;
        RETURN jsonb_build_object('status', 'success', 'message', 'User is already a member of this group.', 'group_id', v_group_id);
    END IF;

    -- Add user to the group members
    INSERT INTO public.family_group_members (group_id, user_id, role)
    VALUES (v_group_id, v_user_id, 'member') -- Default role 'member'
    ON CONFLICT (group_id, user_id) DO NOTHING; -- Should not happen due to prior check

    -- Update invitation status
    UPDATE public.family_group_invitations
    SET status = 'accepted'
    WHERE id = v_invitation.id;

    RETURN jsonb_build_object('status', 'success', 'message', 'Successfully joined the group.', 'group_id', v_group_id);
END;
$$ LANGUAGE plpgsql VOLATILE SECURITY DEFINER;

COMMENT ON FUNCTION public.accept_family_group_invitation(TEXT) IS 'Allows an invited user to accept an invitation using a token, adding them to the group and updating invitation status.';

-- RPC Function: get_user_family_groups
CREATE OR REPLACE FUNCTION public.get_user_family_groups()
RETURNS TABLE (
    group_id UUID,
    group_name TEXT,
    owner_id UUID,
    user_role TEXT,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    joined_at TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        fg.id AS group_id,
        fg.name AS group_name,
        fg.owner_user_id AS owner_id,
        fgm.role AS user_role,
        fg.created_at,
        fg.updated_at,
        fgm.joined_at
    FROM public.family_groups fg
    JOIN public.family_group_members fgm ON fg.id = fgm.group_id
    WHERE fgm.user_id = auth.uid();
END;
$$ LANGUAGE plpgsql STABLE SECURITY INVOKER;

COMMENT ON FUNCTION public.get_user_family_groups() IS 'Returns a list of family groups the current authenticated user is a member of, along with their role in each group.';

-- RPC Function: get_family_group_members
-- To get member details like email, you might join with auth.users or a user_profiles table
CREATE OR REPLACE FUNCTION public.get_family_group_members(p_group_id UUID)
RETURNS TABLE (
    user_id UUID,
    email VARCHAR(255), 
    role TEXT,
    joined_at TIMESTAMPTZ
) AS $$
BEGIN
    -- First, check if the current user is a member of the requested group.
    -- RLS on family_group_members should implicitly handle this if SECURITY INVOKER.
    -- Explicit check for clarity and to prevent unnecessary queries if not a member.
    IF NOT EXISTS (
        SELECT 1
        FROM public.family_group_members fgm
        WHERE fgm.group_id = p_group_id AND fgm.user_id = auth.uid()
    ) THEN
        RAISE EXCEPTION 'Access denied: You are not a member of this group.';
    END IF;

    RETURN QUERY
    SELECT
        fgm.user_id,
        u.email, 
        fgm.role,
        fgm.joined_at
    FROM public.family_group_members fgm
    JOIN auth.users u ON fgm.user_id = u.id
    WHERE fgm.group_id = p_group_id;
END;
$$ LANGUAGE plpgsql STABLE SECURITY INVOKER;

COMMENT ON FUNCTION public.get_family_group_members(UUID) IS 'Returns a list of members for a specific family group, if the current user is also a member of that group.';

-- RPC Function: remove_member_from_family_group
CREATE OR REPLACE FUNCTION public.remove_member_from_family_group(p_group_id UUID, p_user_to_remove_id UUID)
RETURNS JSONB AS $$
DECLARE
    v_remover_id UUID := auth.uid();
    v_group_owner_id UUID;
    v_admin_count INTEGER;
BEGIN
    IF v_remover_id IS NULL THEN
        RAISE EXCEPTION 'User must be authenticated to remove members.';
    END IF;

    IF NOT public.is_family_group_admin(p_group_id, v_remover_id) THEN
        RAISE EXCEPTION 'Only admin members can remove users from the group.';
    END IF;

    IF v_remover_id = p_user_to_remove_id THEN
        RAISE EXCEPTION 'Admin cannot use this function to remove themselves. Use leave_family_group instead.';
    END IF;

    -- Check if the user to remove is the owner
    SELECT owner_user_id INTO v_group_owner_id FROM public.family_groups WHERE id = p_group_id;

    IF p_user_to_remove_id = v_group_owner_id THEN
        -- Count other admins
        SELECT count(*) INTO v_admin_count
        FROM public.family_group_members
        WHERE group_id = p_group_id AND role = 'admin' AND user_id <> p_user_to_remove_id;

        IF v_admin_count = 0 THEN
            RAISE EXCEPTION 'Cannot remove the group owner as they are the only admin. Transfer ownership or promote another admin first.';
        END IF;
    END IF;

    DELETE FROM public.family_group_members
    WHERE group_id = p_group_id AND user_id = p_user_to_remove_id;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'User to remove was not found in this group or already removed.';
    END IF;

    RETURN jsonb_build_object('status', 'success', 'message', 'Member removed successfully.');
END;
$$ LANGUAGE plpgsql VOLATILE SECURITY DEFINER;

COMMENT ON FUNCTION public.remove_member_from_family_group(UUID, UUID) IS 'Allows an admin of a group to remove another member. Prevents removing self (use leave_family_group) and removing the sole admin owner.';

-- RPC Function: leave_family_group
CREATE OR REPLACE FUNCTION public.leave_family_group(p_group_id UUID)
RETURNS JSONB AS $$
DECLARE
    v_leaver_id UUID := auth.uid();
    v_group_owner_id UUID;
    v_admin_count INTEGER;
    v_member_count INTEGER;
BEGIN
    IF v_leaver_id IS NULL THEN
        RAISE EXCEPTION 'User must be authenticated to leave a group.';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM public.family_group_members WHERE group_id = p_group_id AND user_id = v_leaver_id) THEN
        RAISE EXCEPTION 'You are not a member of this group.';
    END IF;

    SELECT owner_user_id INTO v_group_owner_id FROM public.family_groups WHERE id = p_group_id;

    IF v_leaver_id = v_group_owner_id THEN
        -- If owner is leaving, check if other admins exist
        SELECT count(*) INTO v_admin_count
        FROM public.family_group_members
        WHERE group_id = p_group_id AND role = 'admin' AND user_id <> v_leaver_id;

        IF v_admin_count = 0 THEN
            -- If owner is the only admin, check if other members exist.
            -- If other members exist, they can't be left without an admin.
            -- If no other members exist, the group can be orphaned or deleted.
            SELECT count(*) INTO v_member_count
            FROM public.family_group_members
            WHERE group_id = p_group_id AND user_id <> v_leaver_id;

            IF v_member_count > 0 THEN
                 RAISE EXCEPTION 'Group owner cannot leave as they are the only admin and other members exist. Transfer ownership or promote another admin first.';
            END IF;
            -- If owner is sole admin AND sole member, they can leave, orphaning/emptying the group.
            -- The group itself is not deleted here, but will be empty.
        END IF;
    END IF;

    DELETE FROM public.family_group_members
    WHERE group_id = p_group_id AND user_id = v_leaver_id;
    
    RETURN jsonb_build_object('status', 'success', 'message', 'Successfully left the group.');
END;
$$ LANGUAGE plpgsql VOLATILE SECURITY INVOKER; -- RLS allows self-delete

COMMENT ON FUNCTION public.leave_family_group(UUID) IS 'Allows the current authenticated user to leave a family group. Considers ownership and last admin scenarios.';
