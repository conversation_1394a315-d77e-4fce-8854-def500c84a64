-- Ensure predefined categories functions exist
-- This migration recreates the functions if they don't exist

-- Create the predefined_categories table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.predefined_categories (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('income', 'expense')),
  icon TEXT,
  color TEXT NOT NULL,
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Enable RLS
ALTER TABLE public.predefined_categories ENABLE ROW LEVEL SECURITY;

-- Create policy for viewing predefined categories
DROP POLICY IF EXISTS "Authenticated users can view predefined categories" ON public.predefined_categories;
CREATE POLICY "Authenticated users can view predefined categories" ON public.predefined_categories
  FOR SELECT USING (auth.role() = 'authenticated');

-- Insert predefined categories if they don't exist
DO $$
BEGIN
  -- Insert only if the table is empty
  IF NOT EXISTS (SELECT 1 FROM public.predefined_categories LIMIT 1) THEN
    INSERT INTO public.predefined_categories (name, type, icon, color, sort_order) VALUES
    -- Essential Categories
    ('Groceries', 'expense', 'shopping-cart', '#10b981', 1),
    ('Transportation', 'expense', 'car', '#3b82f6', 2),
    ('Utilities', 'expense', 'zap', '#f59e0b', 3),
    ('Housing', 'expense', 'home', '#ef4444', 4),
    ('Healthcare', 'expense', 'heart-pulse', '#ec4899', 5),

    -- Lifestyle Categories  
    ('Dining Out', 'expense', 'utensils', '#8b5cf6', 6),
    ('Entertainment', 'expense', 'clapperboard', '#f97316', 7),
    ('Shopping', 'expense', 'shopping-bag', '#14b8a6', 8),
    ('Subscriptions', 'expense', 'smartphone', '#6366f1', 9),
    ('Personal Care', 'expense', 'sparkles', '#84cc16', 10),

    -- Financial Categories
    ('Insurance', 'expense', 'shield', '#0ea5e9', 11),
    ('Taxes', 'expense', 'calculator', '#64748b', 12),
    ('Debt Payment', 'expense', 'credit-card', '#dc2626', 13),

    -- Other
    ('Gifts', 'expense', 'gift', '#f472b6', 14),
    ('Travel', 'expense', 'plane', '#06b6d4', 15),
    ('Education', 'expense', 'graduation-cap', '#7c3aed', 16),
    ('Miscellaneous', 'expense', 'package', '#6b7280', 17),

    -- Income Categories
    ('Salary', 'income', 'briefcase', '#10b981', 1),
    ('Freelancing', 'income', 'laptop', '#3b82f6', 2),
    ('Business', 'income', 'building-2', '#f59e0b', 3),
    ('Investments', 'income', 'trending-up', '#8b5cf6', 4),
    ('Side Hustle', 'income', 'zap', '#ec4899', 5),
    ('Rental Income', 'income', 'home', '#14b8a6', 6),
    ('Interest', 'income', 'landmark', '#6366f1', 7),
    ('Dividends', 'income', 'coins', '#84cc16', 8),
    ('Bonus', 'income', 'target', '#f97316', 9),
    ('Gifts Received', 'income', 'gift', '#ef4444', 10),
    ('Refunds', 'income', 'undo-2', '#0ea5e9', 11),
    ('Other Income', 'income', 'banknote', '#6b7280', 12);
  END IF;
END $$;

-- Function to create default categories for a new user
CREATE OR REPLACE FUNCTION public.create_default_categories_for_user(user_id UUID)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = public
AS $$
BEGIN
  -- Insert all active predefined categories for the new user
  INSERT INTO public.categories (user_id, name, type, icon, color)
  SELECT 
    user_id,
    pc.name,
    pc.type,
    pc.icon,
    pc.color
  FROM public.predefined_categories pc
  WHERE pc.is_active = true
  ORDER BY pc.sort_order;
  
  -- Log that categories were created
  RAISE NOTICE 'Created default categories for user: %', user_id;
END;
$$;

-- Function to get predefined categories
CREATE OR REPLACE FUNCTION public.get_predefined_categories()
RETURNS TABLE (
  id UUID,
  name TEXT,
  type TEXT,
  icon TEXT,
  color TEXT,
  sort_order INTEGER
)
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = public
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    pc.id,
    pc.name,
    pc.type,
    pc.icon,
    pc.color,
    pc.sort_order
  FROM public.predefined_categories pc
  WHERE pc.is_active = true
  ORDER BY pc.type, pc.sort_order;
END;
$$;

-- Function to add missing categories for current user
CREATE OR REPLACE FUNCTION public.add_missing_categories_for_current_user()
RETURNS TABLE (
  categories_added INTEGER,
  message TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = public
AS $$
DECLARE
  current_user_id UUID;
  category_count INTEGER;
BEGIN
  -- Get current authenticated user
  current_user_id := auth.uid();
  
  IF current_user_id IS NULL THEN
    RETURN QUERY SELECT 0, 'User not authenticated'::TEXT;
    RETURN;
  END IF;
  
  -- Insert missing predefined categories for current user
  INSERT INTO public.categories (user_id, name, type, icon, color)
  SELECT 
    current_user_id,
    pc.name,
    pc.type,
    pc.icon,
    pc.color
  FROM public.predefined_categories pc
  WHERE pc.is_active = true
    AND NOT EXISTS (
      SELECT 1 
      FROM public.categories c 
      WHERE c.user_id = current_user_id 
        AND LOWER(c.name) = LOWER(pc.name) 
        AND c.type = pc.type
    )
  ORDER BY pc.sort_order;
  
  GET DIAGNOSTICS category_count = ROW_COUNT;
  
  RETURN QUERY SELECT 
    category_count,
    CASE 
      WHEN category_count > 0 THEN format('Added %s missing default categories', category_count)
      ELSE 'You already have all default categories'
    END;
END;
$$;

-- Update the handle_new_user function to include creating default categories
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = public
AS $$
BEGIN
  -- First, create the user profile
  INSERT INTO public.user_profiles (id, email, full_name, username, avatar_url, currency, language)
  VALUES (
    NEW.id,
    NEW.email,
    NEW.raw_user_meta_data->>'full_name',
    NEW.raw_user_meta_data->>'username',
    NEW.raw_user_meta_data->>'avatar_url',
    COALESCE(NEW.raw_user_meta_data->>'currency', 'USD'),
    COALESCE(NEW.raw_user_meta_data->>'language', 'en')
  )
  ON CONFLICT (id) DO NOTHING;
  
  -- Then, create default categories for the user
  PERFORM public.create_default_categories_for_user(NEW.id);
  
  RETURN NEW;
END;
$$;

-- Add comments
COMMENT ON FUNCTION public.create_default_categories_for_user(UUID) IS 'Creates default categories for a new user based on predefined templates';
COMMENT ON FUNCTION public.get_predefined_categories() IS 'Returns all active predefined categories sorted by type and order';
COMMENT ON FUNCTION public.add_missing_categories_for_current_user() IS 'Adds missing default categories for the currently authenticated user';
COMMENT ON FUNCTION public.handle_new_user() IS 'Handles creating a user profile and default categories upon new user signup';

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_predefined_categories_active_sort ON public.predefined_categories(is_active, sort_order) WHERE is_active = true;