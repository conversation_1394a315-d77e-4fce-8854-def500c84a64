-- Add audit logging table for invitation actions
CREATE TABLE IF NOT EXISTS public.family_group_invitation_audit (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    invitation_id UUID NOT NULL REFERENCES public.family_group_invitations(id) ON DELETE CASCADE,
    action TEXT NOT NULL, -- 'created', 'accepted', 'declined', 'cancelled', 'expired'
    performed_by_user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    performed_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    details JSONB -- Additional context about the action
);

-- Add comments to audit table
COMMENT ON TABLE public.family_group_invitation_audit IS 'Audit log for all invitation-related actions';
COMMENT ON COLUMN public.family_group_invitation_audit.invitation_id IS 'Reference to the invitation that was acted upon';
COMMENT ON COLUMN public.family_group_invitation_audit.action IS 'Type of action performed (created, accepted, declined, cancelled, expired)';
COMMENT ON COLUMN public.family_group_invitation_audit.performed_by_user_id IS 'User who performed the action (null for system actions like expiration)';
COMMENT ON COLUMN public.family_group_invitation_audit.performed_at IS 'Timestamp when the action was performed';
COMMENT ON COLUMN public.family_group_invitation_audit.details IS 'Additional context about the action in JSON format';

-- Enable RLS for audit table
ALTER TABLE public.family_group_invitation_audit ENABLE ROW LEVEL SECURITY;

-- Policy: Allow group admins to view audit logs for their group's invitations
CREATE POLICY "RLS: Allow group admins to view invitation audit logs"
ON public.family_group_invitation_audit FOR SELECT
USING (
    EXISTS (
        SELECT 1 FROM public.family_group_invitations fgi
        JOIN public.family_group_members fgm ON fgi.group_id = fgm.group_id
        WHERE fgi.id = family_group_invitation_audit.invitation_id
        AND fgm.user_id = auth.uid()
        AND fgm.role = 'admin'
    )
);

-- Function to log invitation actions
CREATE OR REPLACE FUNCTION public.log_invitation_action(
    p_invitation_id UUID,
    p_action TEXT,
    p_performed_by_user_id UUID DEFAULT NULL,
    p_details JSONB DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
    INSERT INTO public.family_group_invitation_audit (
        invitation_id,
        action,
        performed_by_user_id,
        details
    ) VALUES (
        p_invitation_id,
        p_action,
        COALESCE(p_performed_by_user_id, auth.uid()),
        p_details
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMENT ON FUNCTION public.log_invitation_action(UUID, TEXT, UUID, JSONB) IS 'Logs an action performed on an invitation for audit purposes';

-- Function to expire old invitations
CREATE OR REPLACE FUNCTION public.expire_old_invitations()
RETURNS INTEGER AS $$
DECLARE
    expired_count INTEGER := 0;
    expired_invitation RECORD;
BEGIN
    -- Update expired invitations and log the action
    FOR expired_invitation IN
        SELECT id FROM public.family_group_invitations
        WHERE status = 'pending' AND expires_at <= now()
    LOOP
        -- Update status to expired
        UPDATE public.family_group_invitations
        SET status = 'expired'
        WHERE id = expired_invitation.id;
        
        -- Log the expiration action
        PERFORM public.log_invitation_action(
            expired_invitation.id,
            'expired',
            NULL, -- System action, no user
            jsonb_build_object('expired_at', now())
        );
        
        expired_count := expired_count + 1;
    END LOOP;
    
    RETURN expired_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMENT ON FUNCTION public.expire_old_invitations() IS 'Expires old pending invitations and logs the action. Returns the number of invitations expired.';

-- Add responded_at column to invitations table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'family_group_invitations' 
        AND column_name = 'responded_at'
    ) THEN
        ALTER TABLE public.family_group_invitations 
        ADD COLUMN responded_at TIMESTAMPTZ;
        
        COMMENT ON COLUMN public.family_group_invitations.responded_at IS 'Timestamp when the invitation was responded to (accepted/declined)';
    END IF;
END $$;

-- Create a trigger to automatically log invitation creation
CREATE OR REPLACE FUNCTION public.trigger_log_invitation_created()
RETURNS TRIGGER AS $$
BEGIN
    PERFORM public.log_invitation_action(
        NEW.id,
        'created',
        NEW.invited_by_user_id,
        jsonb_build_object(
            'invitee_email', NEW.invitee_email,
            'expires_at', NEW.expires_at
        )
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for invitation creation logging
DROP TRIGGER IF EXISTS log_invitation_created ON public.family_group_invitations;
CREATE TRIGGER log_invitation_created
    AFTER INSERT ON public.family_group_invitations
    FOR EACH ROW
    EXECUTE FUNCTION public.trigger_log_invitation_created();

-- Create a trigger to automatically log invitation status changes
CREATE OR REPLACE FUNCTION public.trigger_log_invitation_status_change()
RETURNS TRIGGER AS $$
BEGIN
    -- Only log if status actually changed
    IF OLD.status IS DISTINCT FROM NEW.status THEN
        PERFORM public.log_invitation_action(
            NEW.id,
            NEW.status,
            CASE 
                WHEN NEW.status IN ('accepted', 'declined') THEN auth.uid()
                ELSE OLD.invited_by_user_id -- For cancelled invitations
            END,
            jsonb_build_object(
                'old_status', OLD.status,
                'new_status', NEW.status,
                'responded_at', NEW.responded_at
            )
        );
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for invitation status change logging
DROP TRIGGER IF EXISTS log_invitation_status_change ON public.family_group_invitations;
CREATE TRIGGER log_invitation_status_change
    AFTER UPDATE ON public.family_group_invitations
    FOR EACH ROW
    EXECUTE FUNCTION public.trigger_log_invitation_status_change();