-- Migration: Fix function return type mismatch for get_pending_invitations_for_user
-- This fixes the "structure of query does not match function result type" error

-- Create new function with correct return types
CREATE OR REPLACE FUNCTION public.get_pending_invitations_for_user_fixed()
RETURNS TABLE (
    invitation_id UUID,
    group_id UUID,
    group_name TEXT,
    invited_by_user_id UUID,
    invited_by_user_email TEXT,  -- Changed from character varying(255) to TEXT
    invited_at TIMESTAMPTZ,
    invitation_status TEXT
) AS $$
DECLARE
    v_user_email TEXT;
BEGIN
    -- Get the current user's email
    SELECT email INTO v_user_email FROM auth.users WHERE id = auth.uid();

    IF v_user_email IS NULL THEN
        RAISE EXCEPTION 'User must be authenticated to view pending invitations.';
    END IF;

    RETURN QUERY
    SELECT
        fgi.id AS invitation_id,
        fgi.group_id,
        fg.name AS group_name,
        fgi.invited_by_user_id,
        inviter.email::TEXT AS invited_by_user_email,  -- Explicit cast to TEXT
        fgi.created_at AS invited_at,
        fgi.status AS invitation_status
    FROM public.family_group_invitations fgi
    JOIN public.family_groups fg ON fgi.group_id = fg.id
    LEFT JOIN auth.users inviter ON fgi.invited_by_user_id = inviter.id
    WHERE fgi.invitee_email = v_user_email AND fgi.status = 'pending'
    ORDER BY fgi.created_at DESC;
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER SET search_path = public;

-- Drop old function and rename new one
DROP FUNCTION IF EXISTS public.get_pending_invitations_for_user() CASCADE;
ALTER FUNCTION public.get_pending_invitations_for_user_fixed() RENAME TO get_pending_invitations_for_user;

-- Add comments
COMMENT ON FUNCTION public.get_pending_invitations_for_user() IS 'Returns pending invitations for the current user based on their email. Fixed return type mismatch.';
