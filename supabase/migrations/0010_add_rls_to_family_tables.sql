-- Enable RLS and define policies for family_groups table
ALTER TABLE public.family_groups ENABLE ROW LEVEL SECURITY;

-- Policy: Allow group members to view their groups
CREATE POLICY "RLS: Allow group members to view their groups" 
ON public.family_groups FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM public.family_group_members fgm
    WHERE fgm.group_id = family_groups.id AND fgm.user_id = auth.uid()
  )
);

-- Policy: Allow authenticated users to create groups
-- The create_family_group RPC handles adding the creator as an admin.
CREATE POLICY "RLS: Allow authenticated users to create groups"
ON public.family_groups FOR INSERT
WITH CHECK (auth.role() = 'authenticated');

-- Policy: Allow group admins to update their groups
CREATE POLICY "RLS: Allow group admins to update their groups"
ON public.family_groups FOR UPDATE
USING (public.is_family_group_admin(id, auth.uid()))
WITH CHECK (public.is_family_group_admin(id, auth.uid()));

-- Policy: Allow group admins to delete their groups
CREATE POLICY "RLS: Allow group admins to delete their groups"
ON public.family_groups FOR DELETE
USING (public.is_family_group_admin(id, auth.uid()));

-- Enable RLS and define policies for family_group_members table
ALTER TABLE public.family_group_members ENABLE ROW LEVEL SECURITY;

-- Policy: Allow group members to view other members of their groups
CREATE POLICY "RLS: Allow group members to view other members"
ON public.family_group_members FOR SELECT
USING (
  public.is_family_group_member_sd(family_group_members.group_id, auth.uid())
);

-- Policy: Allow group admins to add members
-- Note: Primary mechanism for adding members is via accept_family_group_invitation RPC.
-- This policy allows direct inserts by admins if necessary.
CREATE POLICY "RLS: Allow group admins to add members"
ON public.family_group_members FOR INSERT
WITH CHECK (public.is_family_group_admin(group_id, auth.uid()));

-- Policy: Allow group admins to update member roles
CREATE POLICY "RLS: Allow group admins to update member roles"
ON public.family_group_members FOR UPDATE
USING (public.is_family_group_admin(group_id, auth.uid()))
WITH CHECK (public.is_family_group_admin(group_id, auth.uid()));

-- Policy: Allow members to leave or admins to remove members
CREATE POLICY "RLS: Allow members to leave or admins to remove members"
ON public.family_group_members FOR DELETE
USING (
  (user_id = auth.uid()) OR -- User can remove themselves
  (public.is_family_group_admin(group_id, auth.uid())) -- Admin can remove others
);

-- Enable RLS and define policies for family_group_invitations table
ALTER TABLE public.family_group_invitations ENABLE ROW LEVEL SECURITY;

-- Policy: Allow invitees to view their own pending invitations
-- Uses get_user_email helper function defined in 0009_add_family_group_rpcs.sql
CREATE POLICY "RLS: Allow invitees to view their own pending invitations"
ON public.family_group_invitations FOR SELECT
USING (
  invitee_email = (SELECT email FROM auth.users WHERE id = auth.uid()) AND status = 'pending'
);

-- Policy: Allow group admins to view invitations for their group
CREATE POLICY "RLS: Allow group admins to view invitations for their group"
ON public.family_group_invitations FOR SELECT
USING (
  public.is_family_group_admin(group_id, auth.uid())
);

-- Policy: Allow group admins to create invitations
-- Note: Primary mechanism is via invite_to_family_group RPC.
-- This policy ensures direct inserts are also admin-controlled.
CREATE POLICY "RLS: Allow group admins to create invitations"
ON public.family_group_invitations FOR INSERT
WITH CHECK (
  public.is_family_group_admin(group_id, auth.uid()) AND invited_by_user_id = auth.uid()
);

-- Policy: Allow group admins to update invitations
-- Note: Invitees update status via SECURITY DEFINER accept_family_group_invitation RPC.
-- This policy allows admins to manage invitations (e.g., revoke by changing status).
CREATE POLICY "RLS: Allow group admins to update invitations"
ON public.family_group_invitations FOR UPDATE
USING (public.is_family_group_admin(group_id, auth.uid()))
WITH CHECK (public.is_family_group_admin(group_id, auth.uid()));

-- Policy: Allow group admins to delete invitations
CREATE POLICY "RLS: Allow group admins to delete invitations"
ON public.family_group_invitations FOR DELETE
USING (public.is_family_group_admin(group_id, auth.uid()));
