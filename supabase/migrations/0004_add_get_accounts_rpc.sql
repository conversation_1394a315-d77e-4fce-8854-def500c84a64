-- Create get_accounts RPC function
CREATE OR REPLACE FUNCTION public.get_accounts()
RETURNS TABLE (
  id UUID,
  user_id UUID,
  name TEXT,
  type TEXT,
  balance DECIMAL(19, 4),
  currency TEXT,
  icon TEXT,
  color TEXT,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    a.id, 
    a.user_id, 
    a.name, 
    a.type, 
    a.balance, 
    a.currency, 
    a.icon, 
    a.color, 
    a.created_at, 
    a.updated_at
  FROM public.accounts a
  WHERE a.user_id = auth.uid();
END;
$$;

COMMENT ON FUNCTION public.get_accounts() IS 'Retrieves all accounts for the authenticated user.';
