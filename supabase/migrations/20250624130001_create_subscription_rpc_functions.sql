-- Create RPC functions for subscription operations
-- This migration creates all the RPC functions used by the subscription service

-- Function to get user's personal subscriptions with details
CREATE OR REPLACE FUNCTION public.get_user_subscriptions()
RETURNS TABLE (
  id UUID,
  user_id UUID,
  family_group_id UUID,
  name TEXT,
  description TEXT,
  category_id UUID,
  amount DECIMAL,
  currency TEXT,
  billing_cycle TEXT,
  start_date TEXT,
  end_date TEXT,
  next_billing_date TEXT,
  is_active BOOLEAN,
  auto_renew BOOLEAN,
  provider TEXT,
  account_id UUID,
  payment_method TEXT,
  reminder_days INTEGER,
  notes TEXT,
  created_at TEXT,
  updated_at TEXT,
  category_name TEXT,
  category_icon TEXT,
  account_name TEXT,
  created_by_username TEXT,
  days_until_payment INTEGER,
  monthly_cost DECIMAL
)
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = public
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    s.id,
    s.user_id,
    s.family_group_id,
    s.name,
    s.description,
    s.category_id,
    s.amount,
    s.currency,
    s.billing_cycle,
    s.start_date::TEXT,
    s.end_date::TEXT,
    s.next_billing_date::TEXT,
    s.is_active,
    s.auto_renew,
    s.provider,
    s.account_id,
    s.payment_method,
    s.reminder_days,
    s.notes,
    s.created_at::TEXT,
    s.updated_at::TEXT,
    c.name as category_name,
    c.icon as category_icon,
    a.name as account_name,
    up.username as created_by_username,
    public.calculate_days_until_payment(s.next_billing_date) as days_until_payment,
    public.calculate_monthly_cost(s.amount, s.billing_cycle) as monthly_cost
  FROM public.subscriptions s
  LEFT JOIN public.categories c ON s.category_id = c.id
  LEFT JOIN public.accounts a ON s.account_id = a.id
  LEFT JOIN public.user_profiles up ON s.user_id = up.id
  WHERE s.user_id = auth.uid()
    AND s.family_group_id IS NULL  -- Personal subscriptions only
  ORDER BY s.next_billing_date ASC, s.name ASC;
END;
$$;

-- Function to get family group subscriptions with details
CREATE OR REPLACE FUNCTION public.get_family_group_subscriptions(group_uuid UUID)
RETURNS TABLE (
  id UUID,
  user_id UUID,
  family_group_id UUID,
  name TEXT,
  description TEXT,
  category_id UUID,
  amount DECIMAL,
  currency TEXT,
  billing_cycle TEXT,
  start_date TEXT,
  end_date TEXT,
  next_billing_date TEXT,
  is_active BOOLEAN,
  auto_renew BOOLEAN,
  provider TEXT,
  account_id UUID,
  payment_method TEXT,
  reminder_days INTEGER,
  notes TEXT,
  created_at TEXT,
  updated_at TEXT,
  category_name TEXT,
  category_icon TEXT,
  account_name TEXT,
  created_by_username TEXT,
  days_until_payment INTEGER,
  monthly_cost DECIMAL
)
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = public
AS $$
BEGIN
  -- Check if user is a member of the family group
  IF NOT EXISTS (
    SELECT 1 FROM public.family_group_members fgm
    WHERE fgm.group_id = group_uuid
    AND fgm.user_id = auth.uid()
      ) THEN
    RAISE EXCEPTION 'User is not a member of this family group';
  END IF;

  RETURN QUERY
  SELECT 
    s.id,
    s.user_id,
    s.family_group_id,
    s.name,
    s.description,
    s.category_id,
    s.amount,
    s.currency,
    s.billing_cycle,
    s.start_date::TEXT,
    s.end_date::TEXT,
    s.next_billing_date::TEXT,
    s.is_active,
    s.auto_renew,
    s.provider,
    s.account_id,
    s.payment_method,
    s.reminder_days,
    s.notes,
    s.created_at::TEXT,
    s.updated_at::TEXT,
    c.name as category_name,
    c.icon as category_icon,
    a.name as account_name,
    up.username as created_by_username,
    public.calculate_days_until_payment(s.next_billing_date) as days_until_payment,
    public.calculate_monthly_cost(s.amount, s.billing_cycle) as monthly_cost
  FROM public.subscriptions s
  LEFT JOIN public.categories c ON s.category_id = c.id
  LEFT JOIN public.accounts a ON s.account_id = a.id
  LEFT JOIN public.user_profiles up ON s.user_id = up.id
  WHERE s.family_group_id = group_uuid
  ORDER BY s.next_billing_date ASC, s.name ASC;
END;
$$;

-- Function to get subscription summary for personal dashboard
CREATE OR REPLACE FUNCTION public.get_subscription_summary()
RETURNS TABLE (
  total_monthly_cost DECIMAL,
  active_subscriptions INTEGER,
  upcoming_payments_7_days INTEGER,
  expiring_soon INTEGER
)
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = public
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COALESCE(SUM(
      CASE 
        WHEN s.is_active THEN public.calculate_monthly_cost(s.amount, s.billing_cycle)
        ELSE 0
      END
    ), 0) as total_monthly_cost,
    COUNT(CASE WHEN s.is_active THEN 1 END)::INTEGER as active_subscriptions,
    COUNT(CASE 
      WHEN s.is_active AND s.next_billing_date <= CURRENT_DATE + INTERVAL '7 days' 
      THEN 1 
    END)::INTEGER as upcoming_payments_7_days,
    COUNT(CASE 
      WHEN s.end_date IS NOT NULL AND s.end_date <= CURRENT_DATE + INTERVAL '30 days'
      THEN 1 
    END)::INTEGER as expiring_soon
  FROM public.subscriptions s
  WHERE s.user_id = auth.uid()
    AND s.family_group_id IS NULL; -- Personal subscriptions only
END;
$$;

-- Function to get family subscription summary
CREATE OR REPLACE FUNCTION public.get_family_subscription_summary(group_uuid UUID)
RETURNS TABLE (
  total_monthly_cost DECIMAL,
  active_subscriptions INTEGER,
  upcoming_payments_7_days INTEGER,
  expiring_soon INTEGER
)
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = public
AS $$
BEGIN
  -- Check if user is a member of the family group
  IF NOT EXISTS (
    SELECT 1 FROM public.family_group_members fgm
    WHERE fgm.group_id = group_uuid
    AND fgm.user_id = auth.uid()
      ) THEN
    RAISE EXCEPTION 'User is not a member of this family group';
  END IF;

  RETURN QUERY
  SELECT 
    COALESCE(SUM(
      CASE 
        WHEN s.is_active THEN public.calculate_monthly_cost(s.amount, s.billing_cycle)
        ELSE 0
      END
    ), 0) as total_monthly_cost,
    COUNT(CASE WHEN s.is_active THEN 1 END)::INTEGER as active_subscriptions,
    COUNT(CASE 
      WHEN s.is_active AND s.next_billing_date <= CURRENT_DATE + INTERVAL '7 days' 
      THEN 1 
    END)::INTEGER as upcoming_payments_7_days,
    COUNT(CASE 
      WHEN s.end_date IS NOT NULL AND s.end_date <= CURRENT_DATE + INTERVAL '30 days'
      THEN 1 
    END)::INTEGER as expiring_soon
  FROM public.subscriptions s
  WHERE s.family_group_id = group_uuid;
END;
$$;

-- Function to get upcoming payments across all subscriptions
CREATE OR REPLACE FUNCTION public.get_upcoming_payments(days_ahead INTEGER DEFAULT 30)
RETURNS TABLE (
  id UUID,
  name TEXT,
  amount DECIMAL,
  currency TEXT,
  next_billing_date TEXT,
  days_until_payment INTEGER,
  provider TEXT,
  category_name TEXT,
  is_family BOOLEAN
)
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = public
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    s.id,
    s.name,
    s.amount,
    s.currency,
    s.next_billing_date::TEXT,
    public.calculate_days_until_payment(s.next_billing_date) as days_until_payment,
    s.provider,
    c.name as category_name,
    (s.family_group_id IS NOT NULL) as is_family
  FROM public.subscriptions s
  LEFT JOIN public.categories c ON s.category_id = c.id
  WHERE s.is_active = true
    AND s.next_billing_date <= CURRENT_DATE + (days_ahead || ' days')::INTERVAL
    AND (
      -- Personal subscriptions
      (s.user_id = auth.uid() AND s.family_group_id IS NULL)
      OR
      -- Family group subscriptions where user is a member
      (s.family_group_id IS NOT NULL AND EXISTS (
        SELECT 1 FROM public.family_group_members fgm
        WHERE fgm.group_id = s.family_group_id
        AND fgm.user_id = auth.uid()
              ))
    )
  ORDER BY s.next_billing_date ASC, s.name ASC;
END;
$$;

-- Add comments for documentation
COMMENT ON FUNCTION public.get_user_subscriptions() IS 'Returns all personal subscriptions for the authenticated user with category and account details';
COMMENT ON FUNCTION public.get_family_group_subscriptions(UUID) IS 'Returns all subscriptions for a family group if user is a member';
COMMENT ON FUNCTION public.get_subscription_summary() IS 'Returns subscription statistics for personal dashboard';
COMMENT ON FUNCTION public.get_family_subscription_summary(UUID) IS 'Returns subscription statistics for family group dashboard';
COMMENT ON FUNCTION public.get_upcoming_payments(INTEGER) IS 'Returns upcoming payments for all accessible subscriptions within specified days';