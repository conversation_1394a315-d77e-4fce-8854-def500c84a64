-- Migration: Add sample family group financial data for testing
-- This migration creates sample data to test the family group financial features

-- Insert sample categories for family groups (only if they don't exist)
INSERT INTO public.categories (name, type, icon, color, family_group_id, user_id, created_by_user_id)
SELECT 'Groceries', 'expense', 'shopping-cart', '#10B981', fg.id, fg.owner_user_id, fg.owner_user_id
FROM public.family_groups fg
WHERE NOT EXISTS (
    SELECT 1 FROM public.categories c 
    WHERE c.name = 'Groceries' AND c.family_group_id = fg.id
)
LIMIT 1;

INSERT INTO public.categories (name, type, icon, color, family_group_id, user_id, created_by_user_id)
SELECT 'Salary', 'income', 'tag', '#059669', fg.id, fg.owner_user_id, fg.owner_user_id
FROM public.family_groups fg
WHERE NOT EXISTS (
    SELECT 1 FROM public.categories c 
    WHERE c.name = 'Salary' AND c.family_group_id = fg.id
)
LIMIT 1;

INSERT INTO public.categories (name, type, icon, color, family_group_id, user_id, created_by_user_id)
SELECT 'Utilities', 'expense', 'zap', '#DC2626', fg.id, fg.owner_user_id, fg.owner_user_id
FROM public.family_groups fg
WHERE NOT EXISTS (
    SELECT 1 FROM public.categories c 
    WHERE c.name = 'Utilities' AND c.family_group_id = fg.id
)
LIMIT 1;

-- Insert sample accounts for family groups (only if they don't exist)
INSERT INTO public.accounts (name, type, balance, currency, icon, color, family_group_id, user_id, created_by_user_id)
SELECT 'Family Checking', 'checking', 2500.00, 'USD', 'building-2', '#3B82F6', fg.id, fg.owner_user_id, fg.owner_user_id
FROM public.family_groups fg
WHERE NOT EXISTS (
    SELECT 1 FROM public.accounts a 
    WHERE a.name = 'Family Checking' AND a.family_group_id = fg.id
)
LIMIT 1;

INSERT INTO public.accounts (name, type, balance, currency, icon, color, family_group_id, user_id, created_by_user_id)
SELECT 'Family Savings', 'savings', 10000.00, 'USD', 'piggy-bank', '#10B981', fg.id, fg.owner_user_id, fg.owner_user_id
FROM public.family_groups fg
WHERE NOT EXISTS (
    SELECT 1 FROM public.accounts a 
    WHERE a.name = 'Family Savings' AND a.family_group_id = fg.id
)
LIMIT 1;

-- Insert sample transactions for family groups (only if they don't exist)
INSERT INTO public.transactions (amount, type, description, date, family_group_id, user_id, created_by_user_id, account_id, category_id)
SELECT 
    -85.50, 
    'expense', 
    'Weekly grocery shopping', 
    CURRENT_DATE - INTERVAL '2 days',
    fg.id,
    fg.owner_user_id,
    fg.owner_user_id,
    a.id,
    c.id
FROM public.family_groups fg
JOIN public.accounts a ON a.family_group_id = fg.id AND a.name = 'Family Checking'
JOIN public.categories c ON c.family_group_id = fg.id AND c.name = 'Groceries'
WHERE NOT EXISTS (
    SELECT 1 FROM public.transactions t 
    WHERE t.description = 'Weekly grocery shopping' AND t.family_group_id = fg.id
)
LIMIT 1;

INSERT INTO public.transactions (amount, type, description, date, family_group_id, user_id, created_by_user_id, account_id, category_id)
SELECT 
    3500.00, 
    'income', 
    'Monthly salary deposit', 
    CURRENT_DATE - INTERVAL '1 day',
    fg.id,
    fg.owner_user_id,
    fg.owner_user_id,
    a.id,
    c.id
FROM public.family_groups fg
JOIN public.accounts a ON a.family_group_id = fg.id AND a.name = 'Family Checking'
JOIN public.categories c ON c.family_group_id = fg.id AND c.name = 'Salary'
WHERE NOT EXISTS (
    SELECT 1 FROM public.transactions t 
    WHERE t.description = 'Monthly salary deposit' AND t.family_group_id = fg.id
)
LIMIT 1;

-- Insert sample budgets for family groups (only if they don't exist)
INSERT INTO public.budgets (name, amount, period, start_date, family_group_id, user_id, created_by_user_id, category_id)
SELECT
    'Monthly Groceries Budget',
    400.00,
    'monthly',
    DATE_TRUNC('month', CURRENT_DATE),
    fg.id,
    fg.owner_user_id,
    fg.owner_user_id,
    c.id
FROM public.family_groups fg
JOIN public.categories c ON c.family_group_id = fg.id AND c.name = 'Groceries'
WHERE NOT EXISTS (
    SELECT 1 FROM public.budgets b
    WHERE b.family_group_id = fg.id AND b.category_id = c.id
)
LIMIT 1;

-- Insert sample financial goals for family groups (only if they don't exist)
INSERT INTO public.financial_goals (name, target_amount, current_amount, target_date, status, icon, color, family_group_id, user_id, created_by_user_id)
SELECT 
    'Emergency Fund',
    15000.00,
    8500.00,
    CURRENT_DATE + INTERVAL '12 months',
    'active',
    'shield',
    '#EF4444',
    fg.id,
    fg.owner_user_id,
    fg.owner_user_id
FROM public.family_groups fg
WHERE NOT EXISTS (
    SELECT 1 FROM public.financial_goals g 
    WHERE g.name = 'Emergency Fund' AND g.family_group_id = fg.id
)
LIMIT 1;

INSERT INTO public.financial_goals (name, target_amount, current_amount, target_date, status, icon, color, family_group_id, user_id, created_by_user_id)
SELECT 
    'Vacation Fund',
    5000.00,
    1200.00,
    CURRENT_DATE + INTERVAL '6 months',
    'active',
    'palm-tree',
    '#F59E0B',
    fg.id,
    fg.owner_user_id,
    fg.owner_user_id
FROM public.family_groups fg
WHERE NOT EXISTS (
    SELECT 1 FROM public.financial_goals g 
    WHERE g.name = 'Vacation Fund' AND g.family_group_id = fg.id
)
LIMIT 1;