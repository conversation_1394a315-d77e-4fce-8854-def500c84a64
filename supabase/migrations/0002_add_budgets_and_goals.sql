-- Create budgets table if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (SELECT FROM pg_tables WHERE tablename = 'budgets') THEN
    CREATE TABLE public.budgets (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
      category_id UUID REFERENCES public.categories(id) ON DELETE SET NULL,
      amount DECIMAL(19, 4) NOT NULL,
      period TEXT NOT NULL CHECK (period IN ('monthly', 'weekly', 'yearly')),
      start_date TIMESTAMP WITH TIME ZONE NOT NULL,
      end_date TIMESTAMP WITH TIME ZONE,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
    );
    
    COMMENT ON TABLE public.budgets IS 'Stores user budgets for specific categories or overall spending.';
    
    -- Add RLS policy if it doesn't exist
    BEGIN
      -- Check if policy already exists
      IF NOT EXISTS (
        SELECT 1 
        FROM pg_policies 
        WHERE policyname = 'Users can manage their own budgets' 
        AND tablename = 'budgets'
      ) THEN
        CREATE POLICY "Users can manage their own budgets" ON public.budgets
          FOR ALL USING (auth.uid() = user_id);
      END IF;
    EXCEPTION WHEN others THEN
      -- Policy creation failed, likely because it already exists
      RAISE NOTICE 'Policy for budgets table already exists or could not be created';
    END;
  END IF;
END
$$;

-- Create financial_goals table if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (SELECT FROM pg_tables WHERE tablename = 'financial_goals') THEN
    CREATE TABLE public.financial_goals (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
      name TEXT NOT NULL,
      target_amount DECIMAL(19, 4) NOT NULL,
      current_amount DECIMAL(19, 4) DEFAULT 0.00 NOT NULL,
      target_date TIMESTAMP WITH TIME ZONE,
      status TEXT DEFAULT 'active' CHECK (status IN ('active', 'completed', 'paused')),
      icon TEXT,
      color TEXT,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
    );
    
    COMMENT ON TABLE public.financial_goals IS 'Stores user financial goals.';
    
    -- Add RLS policy if it doesn't exist
    BEGIN
      -- Check if policy already exists
      IF NOT EXISTS (
        SELECT 1 
        FROM pg_policies 
        WHERE policyname = 'Users can manage their own financial goals' 
        AND tablename = 'financial_goals'
      ) THEN
        CREATE POLICY "Users can manage their own financial goals" ON public.financial_goals
          FOR ALL USING (auth.uid() = user_id);
      END IF;
    EXCEPTION WHEN others THEN
      -- Policy creation failed, likely because it already exists
      RAISE NOTICE 'Policy for financial_goals table already exists or could not be created';
    END;
  END IF;
END
$$;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_budgets_user_id ON public.budgets(user_id);
CREATE INDEX IF NOT EXISTS idx_budgets_category_id ON public.budgets(category_id);
CREATE INDEX IF NOT EXISTS idx_financial_goals_user_id ON public.financial_goals(user_id);
