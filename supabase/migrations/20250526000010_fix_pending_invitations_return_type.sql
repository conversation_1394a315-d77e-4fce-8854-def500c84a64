-- Migration: Fix all invitation-related functions return types
-- The auth.users.email field is VARCHAR(255), not TEXT, causing type mismatches

-- Fix get_pending_invitations_for_group function
DROP FUNCTION IF EXISTS public.get_pending_invitations_for_group(UUID);

CREATE OR REPLACE FUNCTION public.get_pending_invitations_for_group(p_group_id UUID)
RETURNS TABLE(
  invitation_id UUID,
  group_id UUID,
  group_name TEXT,
  invited_by_user_id UUID,
  invited_by_email VARCHAR(255),  -- Changed from TEXT to VARCHAR(255)
  invitee_email TEXT,
  token TEXT,
  expires_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ
) AS $$
DECLARE
  v_user_id UUID;
  v_is_admin BOOLEAN;
BEGIN
  v_user_id := auth.uid();
  
  -- Check if the user is an admin of the group (with proper table alias)
  SELECT EXISTS (
    SELECT 1 FROM public.family_group_members fgm
    WHERE fgm.group_id = p_group_id AND fgm.user_id = v_user_id AND fgm.role = 'admin'
  ) INTO v_is_admin;
  
  IF NOT v_is_admin THEN
    RAISE EXCEPTION 'Only group admins can view pending invitations';
  END IF;
  
  RETURN QUERY
  SELECT
    fgi.id AS invitation_id,
    fgi.group_id,
    fg.name AS group_name,
    fgi.invited_by_user_id,
    inviter.email AS invited_by_email,
    fgi.invitee_email,
    fgi.token,
    fgi.expires_at,
    fgi.created_at
  FROM public.family_group_invitations fgi
  JOIN public.family_groups fg ON fgi.group_id = fg.id
  LEFT JOIN auth.users inviter ON fgi.invited_by_user_id = inviter.id
  WHERE fgi.group_id = p_group_id AND fgi.status = 'pending'
  ORDER BY fgi.created_at DESC;
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER SET search_path = public;

-- Fix get_invitation_by_token function
DROP FUNCTION IF EXISTS public.get_invitation_by_token(TEXT);

CREATE OR REPLACE FUNCTION public.get_invitation_by_token(p_token TEXT)
RETURNS TABLE (
    id UUID,
    group_id UUID,
    group_name TEXT,
    invited_by_email VARCHAR(255),  -- Changed from TEXT to VARCHAR(255)
    invitee_email TEXT,
    expires_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ,
    status TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        fgi.id,
        fgi.group_id,
        fg.name AS group_name,
        inviter.email AS invited_by_email,
        fgi.invitee_email,
        fgi.expires_at,
        fgi.created_at,
        fgi.status
    FROM public.family_group_invitations fgi
    JOIN public.family_groups fg ON fgi.group_id = fg.id
    LEFT JOIN auth.users inviter ON fgi.invited_by_user_id = inviter.id
    WHERE fgi.token = p_token;
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER;

-- Fix get_pending_invitations_for_user function
DROP FUNCTION IF EXISTS public.get_pending_invitations_for_user();

CREATE OR REPLACE FUNCTION public.get_pending_invitations_for_user()
RETURNS TABLE (
    invitation_id UUID,
    group_id UUID,
    group_name TEXT,
    invited_by_user_id UUID,
    invited_by_user_email VARCHAR(255),  -- Changed from TEXT to VARCHAR(255)
    invited_at TIMESTAMPTZ,
    invitation_status TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_current_user_email TEXT;
    v_normalized_current_user_email TEXT;
BEGIN
    -- Get the email of the currently authenticated user
    SELECT email INTO v_current_user_email FROM auth.users WHERE id = auth.uid();

    IF v_current_user_email IS NULL THEN
        RETURN QUERY SELECT NULL::UUID, NULL::UUID, NULL::TEXT, NULL::UUID, NULL::VARCHAR(255), NULL::TIMESTAMPTZ, NULL::TEXT WHERE FALSE;
    END IF;

    -- Normalize the current user's email (lowercase and remove +alias)
    v_normalized_current_user_email := regexp_replace(lower(v_current_user_email), '\+[^@]*(@.*)', '\1');

    RETURN QUERY
    SELECT
        fgi.id AS invitation_id,
        fgi.group_id,
        fg.name AS group_name,
        fgi.invited_by_user_id AS invited_by_user_id,
        inviter_user.email AS invited_by_user_email,
        fgi.created_at AS invited_at,
        fgi.status AS invitation_status
    FROM
        public.family_group_invitations fgi
    JOIN
        public.family_groups fg ON fgi.group_id = fg.id
    LEFT JOIN
        auth.users inviter_user ON fgi.invited_by_user_id = inviter_user.id
    WHERE
        -- Compare fully normalized invitee_email with fully normalized current_user_email
        regexp_replace(lower(fgi.invitee_email), '\+[^@]*(@.*)', '\1') = v_normalized_current_user_email
        AND fgi.status = 'pending'
    ORDER BY
        fgi.created_at DESC;
END;
$$;

-- Fix get_invitation_audit_logs function
DROP FUNCTION IF EXISTS public.get_invitation_audit_logs(UUID);

CREATE OR REPLACE FUNCTION public.get_invitation_audit_logs(p_group_id UUID)
RETURNS TABLE (
    id UUID,
    invitation_id UUID,
    action TEXT,
    performed_by_user_id UUID,
    performed_by_email VARCHAR(255),  -- Changed from TEXT to VARCHAR(255)
    performed_at TIMESTAMPTZ,
    details JSONB,
    invitee_email TEXT
) AS $$
DECLARE
    v_user_id UUID := auth.uid();
    v_is_admin BOOLEAN;
BEGIN
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'User must be authenticated to view audit logs.';
    END IF;

    -- Check if user is an admin of the group
    SELECT EXISTS (
        SELECT 1 FROM public.family_group_members
        WHERE group_id = p_group_id AND user_id = v_user_id AND role = 'admin'
    ) INTO v_is_admin;

    IF NOT v_is_admin THEN
        RAISE EXCEPTION 'Only group admins can view invitation audit logs.';
    END IF;

    RETURN QUERY
    SELECT
        audit.id,
        audit.invitation_id,
        audit.action,
        audit.performed_by_user_id,
        performer.email AS performed_by_email,
        audit.performed_at,
        audit.details,
        fgi.invitee_email
    FROM public.family_group_invitation_audit audit
    JOIN public.family_group_invitations fgi ON audit.invitation_id = fgi.id
    LEFT JOIN auth.users performer ON audit.performed_by_user_id = performer.id
    WHERE fgi.group_id = p_group_id
    ORDER BY audit.performed_at DESC;
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER;

-- Add comments
COMMENT ON FUNCTION public.get_pending_invitations_for_group(UUID) IS 'Returns pending invitations for a specific group. Only accessible to group admins. Fixed email return type.';
COMMENT ON FUNCTION public.get_invitation_by_token(TEXT) IS 'Returns invitation details by token for the invitation acceptance page. Fixed email return type.';
COMMENT ON FUNCTION public.get_pending_invitations_for_user() IS 'Returns pending invitations for the current user. Fixed email return type.';
COMMENT ON FUNCTION public.get_invitation_audit_logs(UUID) IS 'Returns audit logs for invitations in a specific group. Only accessible to group admins. Fixed email return type.';