DROP FUNCTION IF EXISTS public.get_pending_invitations_for_user();
CREATE OR REPLACE FUNCTION public.get_pending_invitations_for_user()
RETURNS TABLE (
    invitation_id UUID,
    group_id UUID,
    group_name TEXT,
    invited_by_user_id UUID, -- Storing the inviter's user_id
    invited_by_user_email TEXT, -- Storing the inviter's email
    invited_at TIMESTAMPTZ,
    invitation_status TEXT -- Though we filter by 'pending', including it for completeness
)
LANGUAGE plpgsql
SECURITY DEFINER -- Important if you need to access auth.users from a non-privileged role
AS $$
DECLARE
    v_current_user_email TEXT;
    v_normalized_current_user_email TEXT;
BEGIN
    -- Get the email of the currently authenticated user
    SELECT email INTO v_current_user_email FROM auth.users WHERE id = auth.uid();

    IF v_current_user_email IS NULL THEN
        -- Or handle as an error, e.g., RAISE EXCEPTION 'User not found';
        RETURN QUERY SELECT NULL::UUID, NULL::UUID, NULL::TEXT, NULL::UUID, NULL::TEXT, NULL::TIMESTAMPTZ, NULL::TEXT WHERE FALSE;
    END IF;

    -- Normalize the current user's email (lowercase and remove +alias)
    v_normalized_current_user_email := regexp_replace(lower(v_current_user_email), '\+[^@]*(@.*)', '\1');

    RETURN QUERY
    SELECT
        fgi.id AS invitation_id,
        fgi.group_id,
        fg.name AS group_name,
        fgi.invited_by_user_id AS invited_by_user_id,
        inviter_user.email::TEXT AS invited_by_user_email,
        fgi.created_at AS invited_at,
        fgi.status AS invitation_status
    FROM
        public.family_group_invitations fgi
    JOIN
        public.family_groups fg ON fgi.group_id = fg.id
    LEFT JOIN
        auth.users inviter_user ON fgi.invited_by_user_id = inviter_user.id -- Corrected column name
    WHERE
        -- Compare fully normalized invitee_email with fully normalized current_user_email
        regexp_replace(lower(fgi.invitee_email), '\+[^@]*(@.*)', '\1') = v_normalized_current_user_email
        AND fgi.status = 'pending'
    ORDER BY
        fgi.created_at DESC; -- Add back the ORDER BY clause
END;
$$;
