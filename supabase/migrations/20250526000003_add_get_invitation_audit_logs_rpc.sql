-- RPC Function: get_invitation_audit_logs
-- Returns audit logs for invitations in a specific group (admin only)
CREATE OR REPLACE FUNCTION public.get_invitation_audit_logs(p_group_id UUID)
RETURNS TABLE (
    id UUID,
    invitation_id UUID,
    action TEXT,
    performed_by_user_id UUID,
    performed_by_email TEXT,
    performed_at TIMESTAMPTZ,
    details JSONB,
    invitee_email TEXT
) AS $$
DECLARE
    v_user_id UUID := auth.uid();
    v_is_admin BOOLEAN;
BEGIN
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'User must be authenticated to view audit logs.';
    END IF;

    -- Check if user is an admin of the group
    SELECT EXISTS (
        SELECT 1 FROM public.family_group_members
        WHERE group_id = p_group_id AND user_id = v_user_id AND role = 'admin'
    ) INTO v_is_admin;

    IF NOT v_is_admin THEN
        RAISE EXCEPTION 'Only group admins can view invitation audit logs.';
    END IF;

    RETURN QUERY
    SELECT
        audit.id,
        audit.invitation_id,
        audit.action,
        audit.performed_by_user_id,
        performer.email AS performed_by_email,
        audit.performed_at,
        audit.details,
        fgi.invitee_email
    FROM public.family_group_invitation_audit audit
    JOIN public.family_group_invitations fgi ON audit.invitation_id = fgi.id
    LEFT JOIN auth.users performer ON audit.performed_by_user_id = performer.id
    WHERE fgi.group_id = p_group_id
    ORDER BY audit.performed_at DESC;
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER;

COMMENT ON FUNCTION public.get_invitation_audit_logs(UUID) IS 'Returns audit logs for invitations in a specific group. Only accessible to group admins.';