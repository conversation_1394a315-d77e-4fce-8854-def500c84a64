-- Migration: Final fix for get_family_group_categories RPC function
-- This function was missing from the comprehensive fix in 20250526000009

-- Drop and recreate the function with correct return types
DROP FUNCTION IF EXISTS public.get_family_group_categories(UUID);

CREATE OR REPLACE FUNCTION public.get_family_group_categories(p_group_id UUID)
RETURNS TABLE (
    id UUID,
    name TEXT,
    type TEXT,
    icon TEXT,
    color TEXT,
    parent_id UUID,
    parent_name TEXT,
    created_by_user_id UUID,
    created_by_email VARCHAR(255),  -- Changed from TEXT to VARCHAR(255) to match auth.users.email
    created_at TIMESTAMPTZ
) AS $$
DECLARE
    v_user_id UUID := auth.uid();
BEGIN
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'User must be authenticated to view family group categories.';
    END IF;

    -- Check if user is a member of the group
    IF NOT EXISTS (
        SELECT 1 FROM public.family_group_members
        WHERE group_id = p_group_id AND user_id = v_user_id
    ) THEN
        RAISE EXCEPTION 'Access denied: You are not a member of this family group.';
    END IF;

    RETURN QUERY
    SELECT
        c.id,
        c.name,
        c.type,
        c.icon,
        c.color,
        c.parent_id,
        pc.name AS parent_name,
        c.created_by_user_id,
        u.email AS created_by_email,  -- This will be VARCHAR(255) from auth.users
        c.created_at
    FROM public.categories c
    LEFT JOIN public.categories pc ON c.parent_id = pc.id
    LEFT JOIN auth.users u ON c.created_by_user_id = u.id
    WHERE c.family_group_id = p_group_id
    ORDER BY c.type, c.name;
END;
$$ LANGUAGE plpgsql VOLATILE SECURITY DEFINER;

COMMENT ON FUNCTION public.get_family_group_categories(UUID) IS 'Returns all categories for a specific family group. Only accessible to group members.';