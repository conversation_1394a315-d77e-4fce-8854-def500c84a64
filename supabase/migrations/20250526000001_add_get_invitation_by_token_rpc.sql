-- RPC Function: get_invitation_by_token
-- Returns invitation details by token for the invitation acceptance page
CREATE OR REPLACE FUNCTION public.get_invitation_by_token(p_token TEXT)
RETURNS TABLE (
    id UUID,
    group_id UUID,
    group_name TEXT,
    invited_by_email TEXT,
    invitee_email TEXT,
    expires_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ,
    status TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        fgi.id,
        fgi.group_id,
        fg.name AS group_name,
        inviter.email AS invited_by_email,
        fgi.invitee_email,
        fgi.expires_at,
        fgi.created_at,
        fgi.status
    FROM public.family_group_invitations fgi
    JOIN public.family_groups fg ON fgi.group_id = fg.id
    LEFT JOIN auth.users inviter ON fgi.invited_by_user_id = inviter.id
    WHERE fgi.token = p_token;
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER;

COMMENT ON FUNCTION public.get_invitation_by_token(TEXT) IS 'Returns invitation details by token for the invitation acceptance page. Used for token-based invitation links.';