-- Migration: Fix accept_family_group_invitation to use normalized email matching
-- This fixes the issue where invitations fail with "This invitation is not for your email address"
-- when the user's email doesn't exactly match the invited email (e.g., email aliases)

CREATE OR REPLACE FUNCTION public.accept_family_group_invitation(p_invitation_token TEXT)
RETURNS JSONB AS $$
DECLARE
    v_user_id UUID := auth.uid();
    v_invitation RECORD;
    v_group_id UUID;
    v_user_email TEXT;
    v_normalized_user_email TEXT;
BEGIN
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'User must be authenticated to accept an invitation.';
    END IF;

    SELECT email INTO v_user_email FROM auth.users WHERE id = v_user_id;

    -- Normalize the current user's email (lowercase and remove +alias)
    v_normalized_user_email := regexp_replace(lower(v_user_email), '\+[^@]*(@.*)', '\1');

    -- Find invitation using normalized email matching
    SELECT * INTO v_invitation
    FROM public.family_group_invitations
    WHERE token = p_invitation_token 
      AND regexp_replace(lower(invitee_email), '\+[^@]*(@.*)', '\1') = v_normalized_user_email;

    IF v_invitation IS NULL THEN
        RAISE EXCEPTION 'Invitation token is invalid or not for your email address.';
    END IF;

    IF v_invitation.status <> 'pending' THEN
        RAISE EXCEPTION 'Invitation is no longer pending. Status: %', v_invitation.status;
    END IF;

    IF v_invitation.expires_at < now() THEN
        RAISE EXCEPTION 'Invitation has expired.';
    END IF;

    v_group_id := v_invitation.group_id;

    -- Check if user is already a member
    IF EXISTS (
        SELECT 1 FROM public.family_group_members
        WHERE group_id = v_group_id AND user_id = v_user_id
    ) THEN
        -- Update invitation status even if already a member
        UPDATE public.family_group_invitations
        SET status = 'accepted'
        WHERE id = v_invitation.id;
        
        RAISE EXCEPTION 'You are already a member of this group.';
    END IF;

    -- Add user to the group members
    INSERT INTO public.family_group_members (group_id, user_id, role)
    VALUES (v_group_id, v_user_id, 'member')
    ON CONFLICT (group_id, user_id) DO NOTHING;

    -- Update invitation status
    UPDATE public.family_group_invitations
    SET status = 'accepted'
    WHERE id = v_invitation.id;

    RETURN jsonb_build_object('status', 'success', 'message', 'Successfully joined the group.', 'group_id', v_group_id);
END;
$$ LANGUAGE plpgsql VOLATILE SECURITY DEFINER SET search_path = public;

COMMENT ON FUNCTION public.accept_family_group_invitation(TEXT) IS 'Allows an invited user to accept an invitation using a token. Uses normalized email matching to handle email aliases. SECURITY DEFINER with search_path protection.'; 