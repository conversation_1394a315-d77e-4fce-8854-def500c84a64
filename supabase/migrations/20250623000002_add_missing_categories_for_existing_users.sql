-- Add function to create missing default categories for existing users
-- This function can be called manually or triggered to ensure all users have default categories

CREATE OR REPLACE FUNCTION public.ensure_user_has_default_categories(target_user_id UUID DEFAULT NULL)
RETURNS TABLE (
  user_id UUID,
  categories_added INTEGER,
  message TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = public
AS $$
DECLARE
  current_user_id UUID;
  category_count INTEGER;
  users_cursor CURSOR FOR 
    SELECT up.id 
    FROM public.user_profiles up
    WHERE (target_user_id IS NULL OR up.id = target_user_id);
BEGIN
  -- Loop through users (either specific user or all users)
  FOR user_record IN users_cursor LOOP
    current_user_id := user_record.id;
    
    -- Count existing categories for this user
    SELECT COUNT(*) INTO category_count
    FROM public.categories c
    WHERE c.user_id = current_user_id;
    
    -- If user has fewer than 10 categories, add missing defaults
    IF category_count < 10 THEN
      -- Insert missing predefined categories
      INSERT INTO public.categories (user_id, name, type, icon, color)
      SELECT 
        current_user_id,
        pc.name,
        pc.type,
        pc.icon,
        pc.color
      FROM public.predefined_categories pc
      WHERE pc.is_active = true
        AND NOT EXISTS (
          SELECT 1 
          FROM public.categories c 
          WHERE c.user_id = current_user_id 
            AND LOWER(c.name) = LOWER(pc.name) 
            AND c.type = pc.type
        )
      ORDER BY pc.sort_order;
      
      -- Get count of categories added
      GET DIAGNOSTICS category_count = ROW_COUNT;
      
      RETURN QUERY SELECT 
        current_user_id,
        category_count,
        CASE 
          WHEN category_count > 0 THEN format('Added %s default categories', category_count)
          ELSE 'User already has all default categories'
        END;
    ELSE
      RETURN QUERY SELECT 
        current_user_id,
        0,
        'User already has sufficient categories'::TEXT;
    END IF;
  END LOOP;
  
  RETURN;
END;
$$;

COMMENT ON FUNCTION public.ensure_user_has_default_categories(UUID) IS 'Ensures users have default categories. Pass NULL for all users or specific user_id';

-- Create a simpler function for the frontend to call
CREATE OR REPLACE FUNCTION public.add_missing_categories_for_current_user()
RETURNS TABLE (
  categories_added INTEGER,
  message TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER SET search_path = public
AS $$
DECLARE
  current_user_id UUID;
  category_count INTEGER;
BEGIN
  -- Get current authenticated user
  current_user_id := auth.uid();
  
  IF current_user_id IS NULL THEN
    RETURN QUERY SELECT 0, 'User not authenticated'::TEXT;
    RETURN;
  END IF;
  
  -- Insert missing predefined categories for current user
  INSERT INTO public.categories (user_id, name, type, icon, color)
  SELECT 
    current_user_id,
    pc.name,
    pc.type,
    pc.icon,
    pc.color
  FROM public.predefined_categories pc
  WHERE pc.is_active = true
    AND NOT EXISTS (
      SELECT 1 
      FROM public.categories c 
      WHERE c.user_id = current_user_id 
        AND LOWER(c.name) = LOWER(pc.name) 
        AND c.type = pc.type
    )
  ORDER BY pc.sort_order;
  
  GET DIAGNOSTICS category_count = ROW_COUNT;
  
  RETURN QUERY SELECT 
    category_count,
    CASE 
      WHEN category_count > 0 THEN format('Added %s missing default categories', category_count)
      ELSE 'You already have all default categories'
    END;
END;
$$;

COMMENT ON FUNCTION public.add_missing_categories_for_current_user() IS 'Adds missing default categories for the currently authenticated user';