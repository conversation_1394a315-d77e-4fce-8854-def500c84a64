-- Recreate the trigger to call handle_new_user on new user creation
-- This is to ensure it exists if it was missed during the initial schema push.

-- Drop the trigger first if it somehow exists but is not visible or is misconfigured
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;

-- Recreate the trigger
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
