-- Migration: Fix all RPC functions to use correct return types for auth.users.email
-- The auth.users.email field is VARCHAR(255), not TEXT

-- Fix get_family_group_accounts
DROP FUNCTION IF EXISTS public.get_family_group_accounts(UUID);

CREATE OR REPLACE FUNCTION public.get_family_group_accounts(p_group_id UUID)
RETURNS TABLE (
    id UUID,
    name TEXT,
    type TEXT,
    balance DECIMAL(19, 4),
    currency TEXT,
    icon TEXT,
    color TEXT,
    created_by_user_id UUID,
    created_by_email VARCHAR(255),  -- Changed from TEXT to VARCHAR(255)
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ
) AS $$
DECLARE
    v_user_id UUID := auth.uid();
BEGIN
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'User must be authenticated to view family group accounts.';
    END IF;

    -- Check if user is a member of the group
    IF NOT EXISTS (
        SELECT 1 FROM public.family_group_members
        WHERE group_id = p_group_id AND user_id = v_user_id
    ) THEN
        RAISE EXCEPTION 'Access denied: You are not a member of this family group.';
    END IF;

    RETURN QUERY
    SELECT
        a.id,
        a.name,
        a.type,
        a.balance,
        a.currency,
        a.icon,
        a.color,
        a.created_by_user_id,
        u.email AS created_by_email,
        a.created_at,
        a.updated_at
    FROM public.accounts a
    LEFT JOIN auth.users u ON a.created_by_user_id = u.id
    WHERE a.family_group_id = p_group_id
    ORDER BY a.created_at DESC;
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER;

-- Fix get_family_group_transactions
DROP FUNCTION IF EXISTS public.get_family_group_transactions(UUID, INTEGER, INTEGER);

CREATE OR REPLACE FUNCTION public.get_family_group_transactions(
    p_group_id UUID,
    p_limit INTEGER DEFAULT 50,
    p_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    account_id UUID,
    account_name TEXT,
    category_id UUID,
    category_name TEXT,
    amount DECIMAL(19, 4),
    type TEXT,
    description TEXT,
    date TIMESTAMPTZ,
    notes TEXT,
    created_by_user_id UUID,
    created_by_email VARCHAR(255),  -- Changed from TEXT to VARCHAR(255)
    created_at TIMESTAMPTZ
) AS $$
DECLARE
    v_user_id UUID := auth.uid();
BEGIN
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'User must be authenticated to view family group transactions.';
    END IF;

    -- Check if user is a member of the group
    IF NOT EXISTS (
        SELECT 1 FROM public.family_group_members
        WHERE group_id = p_group_id AND user_id = v_user_id
    ) THEN
        RAISE EXCEPTION 'Access denied: You are not a member of this family group.';
    END IF;

    RETURN QUERY
    SELECT
        t.id,
        t.account_id,
        a.name AS account_name,
        t.category_id,
        c.name AS category_name,
        t.amount,
        t.type,
        t.description,
        t.date,
        t.notes,
        t.created_by_user_id,
        u.email AS created_by_email,
        t.created_at
    FROM public.transactions t
    LEFT JOIN public.accounts a ON t.account_id = a.id
    LEFT JOIN public.categories c ON t.category_id = c.id
    LEFT JOIN auth.users u ON t.created_by_user_id = u.id
    WHERE t.family_group_id = p_group_id
    ORDER BY t.date DESC, t.created_at DESC
    LIMIT p_limit OFFSET p_offset;
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER;

-- Fix get_family_group_budgets
DROP FUNCTION IF EXISTS public.get_family_group_budgets(UUID);

CREATE OR REPLACE FUNCTION public.get_family_group_budgets(p_group_id UUID)
RETURNS TABLE (
    id UUID,
    category_id UUID,
    category_name TEXT,
    amount DECIMAL(19, 4),
    period TEXT,
    start_date TIMESTAMPTZ,
    end_date TIMESTAMPTZ,
    spent_amount DECIMAL(19, 4),
    created_by_user_id UUID,
    created_by_email VARCHAR(255),  -- Changed from TEXT to VARCHAR(255)
    created_at TIMESTAMPTZ
) AS $$
DECLARE
    v_user_id UUID := auth.uid();
BEGIN
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'User must be authenticated to view family group budgets.';
    END IF;

    -- Check if user is a member of the group
    IF NOT EXISTS (
        SELECT 1 FROM public.family_group_members
        WHERE group_id = p_group_id AND user_id = v_user_id
    ) THEN
        RAISE EXCEPTION 'Access denied: You are not a member of this family group.';
    END IF;

    RETURN QUERY
    SELECT
        b.id,
        b.category_id,
        c.name AS category_name,
        b.amount,
        b.period,
        b.start_date,
        b.end_date,
        COALESCE(
            (SELECT SUM(ABS(t.amount)) 
             FROM public.transactions t 
             WHERE t.family_group_id = p_group_id 
             AND t.category_id = b.category_id 
             AND t.type = 'expense'
             AND t.date >= b.start_date 
             AND (b.end_date IS NULL OR t.date <= b.end_date)
            ), 0
        ) AS spent_amount,
        b.created_by_user_id,
        u.email AS created_by_email,
        b.created_at
    FROM public.budgets b
    LEFT JOIN public.categories c ON b.category_id = c.id
    LEFT JOIN auth.users u ON b.created_by_user_id = u.id
    WHERE b.family_group_id = p_group_id
    ORDER BY b.created_at DESC;
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER;

-- Fix get_family_group_goals
DROP FUNCTION IF EXISTS public.get_family_group_goals(UUID);

CREATE OR REPLACE FUNCTION public.get_family_group_goals(p_group_id UUID)
RETURNS TABLE (
    id UUID,
    name TEXT,
    target_amount DECIMAL(19, 4),
    current_amount DECIMAL(19, 4),
    target_date TIMESTAMPTZ,
    status TEXT,
    icon TEXT,
    color TEXT,
    progress_percentage DECIMAL(5, 2),
    created_by_user_id UUID,
    created_by_email VARCHAR(255),  -- Changed from TEXT to VARCHAR(255)
    created_at TIMESTAMPTZ
) AS $$
DECLARE
    v_user_id UUID := auth.uid();
BEGIN
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'User must be authenticated to view family group goals.';
    END IF;

    -- Check if user is a member of the group
    IF NOT EXISTS (
        SELECT 1 FROM public.family_group_members
        WHERE group_id = p_group_id AND user_id = v_user_id
    ) THEN
        RAISE EXCEPTION 'Access denied: You are not a member of this family group.';
    END IF;

    RETURN QUERY
    SELECT
        fg.id,
        fg.name,
        fg.target_amount,
        fg.current_amount,
        fg.target_date,
        fg.status,
        fg.icon,
        fg.color,
        CASE 
            WHEN fg.target_amount > 0 THEN 
                ROUND((fg.current_amount / fg.target_amount * 100)::DECIMAL, 2)
            ELSE 0
        END AS progress_percentage,
        fg.created_by_user_id,
        u.email AS created_by_email,
        fg.created_at
    FROM public.financial_goals fg
    LEFT JOIN auth.users u ON fg.created_by_user_id = u.id
    WHERE fg.family_group_id = p_group_id
    ORDER BY fg.created_at DESC;
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER;

-- Add comments
COMMENT ON FUNCTION public.get_family_group_accounts(UUID) IS 'Returns all accounts for a specific family group. Only accessible to group members.';
COMMENT ON FUNCTION public.get_family_group_transactions(UUID, INTEGER, INTEGER) IS 'Returns transactions for a specific family group with pagination. Only accessible to group members.';
COMMENT ON FUNCTION public.get_family_group_budgets(UUID) IS 'Returns all budgets for a specific family group with spent amounts. Only accessible to group members.';
COMMENT ON FUNCTION public.get_family_group_goals(UUID) IS 'Returns all financial goals for a specific family group with progress calculations. Only accessible to group members.';
