ALTER TABLE public.user_profiles
ADD COLUMN IF NOT EXISTS email TEXT;

-- Optional: Add a constraint to ensure email is unique if desired,
-- and link it to the auth.users table's email.
-- This assumes you want to keep them in sync and that 'id' in user_profiles
-- is a foreign key to 'id' in auth.users.

-- First, ensure the email column exists from the step above.
-- Then, you might want to populate it from auth.users if it's empty
-- UPDATE public.user_profiles up
-- SET email = au.email
-- FROM auth.users au
-- WHERE up.id = au.id AND up.email IS NULL;

-- Add a unique constraint if emails should be unique within user_profiles
-- ALTER TABLE public.user_profiles
-- ADD CONSTRAINT user_profiles_email_key UNIQUE (email);

-- If you want to ensure the email in user_profiles matches the one in auth.users,
-- you might consider triggers or regularly scheduled functions,
-- as direct foreign key constraints on auth.users.email can be complex
-- due to RLS and permissions.
-- For now, just adding the column is the primary goal.
