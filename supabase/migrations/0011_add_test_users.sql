-- Migration: Add test users for development and testing

-- This script directly inserts into auth.users and auth.identities
-- as a fallback if auth.admin_create_user is unavailable or problematic.
-- Requires pgcrypto extension for crypt() and gen_salt().

DO $$
DECLARE
  user_1_id UUID := gen_random_uuid();
  user_2_id UUID := gen_random_uuid();
  user_1_email TEXT := '<EMAIL>';
  user_2_email TEXT := '<EMAIL>';
  test_password TEXT := 'password123';
  hashed_password TEXT;
BEGIN
  -- Ensure pgcrypto is available (usually is in Supabase)
  CREATE EXTENSION IF NOT EXISTS pgcrypto WITH SCHEMA extensions;

  hashed_password := crypt(test_password, gen_salt('bf'));

  -- Create <EMAIL>
  INSERT INTO auth.users 
    (id, email, encrypted_password, email_confirmed_at, role, aud, created_at, updated_at, last_sign_in_at)
  VALUES 
    (user_1_id, user_1_email, hashed_password, now(), 'authenticated', 'authenticated', now(), now(), now());

  INSERT INTO auth.identities 
    (id, user_id, identity_data, provider, provider_id, last_sign_in_at, created_at, updated_at)
  VALUES 
    (gen_random_uuid(), user_1_id, jsonb_build_object('sub', user_1_id::TEXT, 'email', user_1_email), 'email', user_1_email, now(), now(), now());

  RAISE NOTICE 'Created <EMAIL> with ID: %', user_1_id;

  -- Create <EMAIL>
  INSERT INTO auth.users 
    (id, email, encrypted_password, email_confirmed_at, role, aud, created_at, updated_at, last_sign_in_at)
  VALUES 
    (user_2_id, user_2_email, hashed_password, now(), 'authenticated', 'authenticated', now(), now(), now());

  INSERT INTO auth.identities 
    (id, user_id, identity_data, provider, provider_id, last_sign_in_at, created_at, updated_at)
  VALUES 
    (gen_random_uuid(), user_2_id, jsonb_build_object('sub', user_2_id::TEXT, 'email', user_2_email), 'email', user_2_email, now(), now(), now());

  RAISE NOTICE 'Created <EMAIL> with ID: %', user_2_id;

  -- Note: If you have a user_profiles table that needs to be populated for these users,
  -- you would add INSERT statements here, e.g.:
  -- IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'user_profiles') THEN
  --   INSERT INTO public.user_profiles (id, email) VALUES (user_1_id, user_1_email) ON CONFLICT (id) DO NOTHING;
  --   INSERT INTO public.user_profiles (id, email) VALUES (user_2_id, user_2_email) ON CONFLICT (id) DO NOTHING;
  --   RAISE NOTICE 'Attempted to populate user_profiles for test users.';
  -- END IF;
END;
$$;
