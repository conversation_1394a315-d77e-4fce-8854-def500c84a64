-- Migration: Add family group support to financial features
-- This migration extends existing financial tables to support family group context

-- Add family_group_id to existing tables to support both personal and family group financial data

-- 1. Add family_group_id to accounts table
ALTER TABLE public.accounts 
ADD COLUMN family_group_id UUID REFERENCES public.family_groups(id) ON DELETE CASCADE;

-- Add index for family group accounts
CREATE INDEX IF NOT EXISTS idx_accounts_family_group_id ON public.accounts(family_group_id);

-- Update accounts RLS policy to include family group access
DROP POLICY IF EXISTS "Users can manage their own accounts" ON public.accounts;
CREATE POLICY "Users can manage their own accounts and family group accounts" ON public.accounts
FOR ALL USING (
  auth.uid() = user_id OR 
  (family_group_id IS NOT NULL AND EXISTS (
    SELECT 1 FROM public.family_group_members fgm 
    WHERE fgm.group_id = accounts.family_group_id 
    AND fgm.user_id = auth.uid()
  ))
);

-- 2. Add family_group_id to categories table
ALTER TABLE public.categories 
ADD COLUMN family_group_id UUID REFERENCES public.family_groups(id) ON DELETE CASCADE;

-- Add index for family group categories
CREATE INDEX IF NOT EXISTS idx_categories_family_group_id ON public.categories(family_group_id);

-- Update categories RLS policy to include family group access
DROP POLICY IF EXISTS "Users can manage their own categories" ON public.categories;
CREATE POLICY "Users can manage their own categories and family group categories" ON public.categories
FOR ALL USING (
  auth.uid() = user_id OR 
  (family_group_id IS NOT NULL AND EXISTS (
    SELECT 1 FROM public.family_group_members fgm 
    WHERE fgm.group_id = categories.family_group_id 
    AND fgm.user_id = auth.uid()
  ))
);

-- 3. Add family_group_id to transactions table
ALTER TABLE public.transactions 
ADD COLUMN family_group_id UUID REFERENCES public.family_groups(id) ON DELETE CASCADE;

-- Add index for family group transactions
CREATE INDEX IF NOT EXISTS idx_transactions_family_group_id ON public.transactions(family_group_id);

-- Update transactions RLS policy to include family group access
DROP POLICY IF EXISTS "Users can manage their own transactions" ON public.transactions;
CREATE POLICY "Users can manage their own transactions and family group transactions" ON public.transactions
FOR ALL USING (
  auth.uid() = user_id OR 
  (family_group_id IS NOT NULL AND EXISTS (
    SELECT 1 FROM public.family_group_members fgm 
    WHERE fgm.group_id = transactions.family_group_id 
    AND fgm.user_id = auth.uid()
  ))
);

-- 4. Add family_group_id to budgets table
ALTER TABLE public.budgets 
ADD COLUMN family_group_id UUID REFERENCES public.family_groups(id) ON DELETE CASCADE;

-- Add index for family group budgets
CREATE INDEX IF NOT EXISTS idx_budgets_family_group_id ON public.budgets(family_group_id);

-- Update budgets RLS policy to include family group access
DROP POLICY IF EXISTS "Users can manage their own budgets" ON public.budgets;
CREATE POLICY "Users can manage their own budgets and family group budgets" ON public.budgets
FOR ALL USING (
  auth.uid() = user_id OR 
  (family_group_id IS NOT NULL AND EXISTS (
    SELECT 1 FROM public.family_group_members fgm 
    WHERE fgm.group_id = budgets.family_group_id 
    AND fgm.user_id = auth.uid()
  ))
);

-- 5. Add family_group_id to financial_goals table
ALTER TABLE public.financial_goals 
ADD COLUMN family_group_id UUID REFERENCES public.family_groups(id) ON DELETE CASCADE;

-- Add index for family group goals
CREATE INDEX IF NOT EXISTS idx_financial_goals_family_group_id ON public.financial_goals(family_group_id);

-- Update financial_goals RLS policy to include family group access
DROP POLICY IF EXISTS "Users can manage their own financial goals" ON public.financial_goals;
CREATE POLICY "Users can manage their own financial goals and family group goals" ON public.financial_goals
FOR ALL USING (
  auth.uid() = user_id OR 
  (family_group_id IS NOT NULL AND EXISTS (
    SELECT 1 FROM public.family_group_members fgm 
    WHERE fgm.group_id = financial_goals.family_group_id 
    AND fgm.user_id = auth.uid()
  ))
);

-- Add comments to document the new columns
COMMENT ON COLUMN public.accounts.family_group_id IS 'Optional reference to family group. If set, this account belongs to the family group rather than individual user.';
COMMENT ON COLUMN public.categories.family_group_id IS 'Optional reference to family group. If set, this category belongs to the family group rather than individual user.';
COMMENT ON COLUMN public.transactions.family_group_id IS 'Optional reference to family group. If set, this transaction belongs to the family group rather than individual user.';
COMMENT ON COLUMN public.budgets.family_group_id IS 'Optional reference to family group. If set, this budget belongs to the family group rather than individual user.';
COMMENT ON COLUMN public.financial_goals.family_group_id IS 'Optional reference to family group. If set, this goal belongs to the family group rather than individual user.';

-- Create constraints to ensure data integrity
-- Each financial record should belong to either a user OR a family group, but not both contexts simultaneously

-- For accounts: if family_group_id is set, user_id should still be set (to track who created it)
-- But we'll add a check to ensure logical consistency in application layer

-- Add created_by_user_id to track who created family group financial records
ALTER TABLE public.accounts ADD COLUMN created_by_user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL;
ALTER TABLE public.categories ADD COLUMN created_by_user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL;
ALTER TABLE public.transactions ADD COLUMN created_by_user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL;
ALTER TABLE public.budgets ADD COLUMN created_by_user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL;
ALTER TABLE public.financial_goals ADD COLUMN created_by_user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL;

-- Add comments for created_by_user_id columns
COMMENT ON COLUMN public.accounts.created_by_user_id IS 'User who created this account. For family group accounts, tracks the creator while user_id may be different.';
COMMENT ON COLUMN public.categories.created_by_user_id IS 'User who created this category. For family group categories, tracks the creator while user_id may be different.';
COMMENT ON COLUMN public.transactions.created_by_user_id IS 'User who created this transaction. For family group transactions, tracks the creator while user_id may be different.';
COMMENT ON COLUMN public.budgets.created_by_user_id IS 'User who created this budget. For family group budgets, tracks the creator while user_id may be different.';
COMMENT ON COLUMN public.financial_goals.created_by_user_id IS 'User who created this goal. For family group goals, tracks the creator while user_id may be different.';

-- Create indexes for created_by_user_id columns
CREATE INDEX IF NOT EXISTS idx_accounts_created_by_user_id ON public.accounts(created_by_user_id);
CREATE INDEX IF NOT EXISTS idx_categories_created_by_user_id ON public.categories(created_by_user_id);
CREATE INDEX IF NOT EXISTS idx_transactions_created_by_user_id ON public.transactions(created_by_user_id);
CREATE INDEX IF NOT EXISTS idx_budgets_created_by_user_id ON public.budgets(created_by_user_id);
CREATE INDEX IF NOT EXISTS idx_financial_goals_created_by_user_id ON public.financial_goals(created_by_user_id);