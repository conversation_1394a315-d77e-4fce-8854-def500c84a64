---
description: 
globs: 
alwaysApply: false
---
# Database Patterns and Supabase Integration

## Database Architecture

### Core Tables
The application uses the following main tables in Supabase:
- **user_profiles** - Extended user information beyond Supabase Auth
- **categories** - Transaction categories (income/expense)
- **accounts** - Bank accounts and financial accounts
- **transactions** - Financial transactions
- **budgets** - Budget tracking
- **financial_goals** - Savings and financial goals
- **family_groups** - Family collaboration groups
- **family_group_members** - Group membership
- **family_group_invitations** - Invitation system

### Database Schema Location
- **[supabase/migrations/](mdc:supabase/migrations)** - All database migrations
- **[supabase/migrations/0000_initial_schema.sql](mdc:supabase/migrations/0000_initial_schema.sql)** - Initial schema setup

## Service Layer Pattern

### Supabase Services Location
- **[src/shared/services/supabase/](mdc:src/shared/services/supabase)** - Core Supabase clients and utilities
  - **[client.ts](mdc:src/shared/services/supabase/client.ts)** - Browser client
  - **[server.ts](mdc:src/shared/services/supabase/server.ts)** - Server-side client
  - **[client-utils.ts](mdc:src/shared/services/supabase/client-utils.ts)** - Client utilities
  - **[server-actions.ts](mdc:src/shared/services/supabase/server-actions.ts)** - Server actions
  - **[avatar-service.ts](mdc:src/shared/services/supabase/avatar-service.ts)** - Avatar management
- **[src/features/[feature]/services/](mdc:src/features)** - Feature-specific database operations
  - **[dashboard/services/dashboard.ts](mdc:src/features/dashboard/services/dashboard.ts)** - Dashboard data operations
  - **[family-groups/services/](mdc:src/features/family-groups/services)** - Family group and finance operations
  - **[transactions/services/](mdc:src/features/transactions/services)** - Transaction operations

### Service Architecture
```typescript
// Feature-specific service example
export class AccountService {
  static async getAccounts(userId: string): Promise<Account[]> {
    // Database operation
  }
  
  static async createAccount(data: CreateAccountData): Promise<Account> {
    // Database operation with validation
  }
}
```

## Database Client Configuration

### Client Files
- **[src/shared/services/supabase/client.ts](mdc:src/shared/services/supabase/client.ts)** - Browser client
- **[src/shared/services/supabase/server.ts](mdc:src/shared/services/supabase/server.ts)** - Server-side client
- **[src/shared/services/supabase/client-utils.ts](mdc:src/shared/services/supabase/client-utils.ts)** - Client utilities

### Usage Patterns
```typescript
// Client-side (React components)
import { supabaseClient } from '@/shared/services/supabase/client'

// Server-side (API routes, server components)
import { createServerClient } from '@/shared/services/supabase/server'
```

## Row Level Security (RLS)

### Security Principles
1. **User Isolation**: Users can only access their own data
2. **Family Group Sharing**: Group members can access shared group data
3. **Admin Controls**: Group admins have additional permissions
4. **Invitation System**: Secure token-based invitations

### RLS Implementation
- All tables have RLS policies enabled
- Policies defined in migration files
- User context automatically handled by Supabase Auth

## Database Types

### Type Definitions
- **[src/shared/types/supabase.ts](mdc:src/shared/types/supabase.ts)** - Generated Supabase types
- **[src/features/[feature]/types/](mdc:src/features/)** - Feature-specific type extensions

### Type Generation
```bash
# Generate types from Supabase schema
npx supabase gen types typescript --project-id YOUR_PROJECT_ID > src/shared/types/supabase.ts
```

## Query Patterns

### React Query Integration
```typescript
// Custom hook for data fetching
export function useAccounts() {
  return useQuery({
    queryKey: ['accounts'],
    queryFn: () => AccountService.getAccounts(),
    staleTime: 1000 * 60 * 5, // 5 minutes
  })
}
```

### Server Actions
- **[src/shared/services/supabase/server-actions.ts](mdc:src/shared/services/supabase/server-actions.ts)** - Server-side mutations
- Use for form submissions and data mutations
- Automatically handle authentication and validation

## RPC Functions

### Custom Database Functions
- Prefix: `get_`, `insert_`, `update_`, `delete_`
- Location: Defined in migration files
- Purpose: Complex queries, aggregations, business logic

### Usage Example
```typescript
// Call RPC function
const { data } = await supabase
  .rpc('get_user_financial_summary', { user_id: userId })
```

## Family Group Data Model

### Data Sharing Pattern
- **Private Data**: `group_id` is NULL, belongs to individual user
- **Shared Data**: `group_id` references family_groups table
- **Context Switching**: Users can switch between personal and group views

### Group Permissions
- **Members**: Can view and add data
- **Admins**: Can manage group settings and members
- **Owners**: Can delete group and manage all aspects

## Error Handling

### Database Error Patterns
```typescript
try {
  const result = await AccountService.createAccount(data)
  return { success: true, data: result }
} catch (error) {
  if (error.code === '23505') {
    return { success: false, error: 'Account already exists' }
  }
  throw error
}
```

### Common Error Codes
- `23505` - Unique constraint violation
- `42501` - Insufficient privilege (RLS)
- `23503` - Foreign key violation

## Migration Best Practices

1. **Incremental Changes**: Each migration should be small and focused
2. **Rollback Safety**: Ensure migrations can be safely rolled back
3. **Data Preservation**: Never drop columns with data without backup
4. **RLS First**: Always add RLS policies when creating tables
5. **Function Versioning**: Use `CREATE OR REPLACE` for function updates
