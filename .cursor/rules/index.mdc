---
description: 
globs: 
alwaysApply: false
---
# Budget Tracker - Cursor Rules Index

## Overview
This budget tracker application is a modern Next.js application with Supabase backend, featuring family collaboration, comprehensive financial tracking, and a clean feature-based architecture.

## Quick Reference

### Essential Files
- **[package.json](mdc:package.json)** - Dependencies and scripts
- **[tsconfig.json](mdc:tsconfig.json)** - TypeScript configuration
- **[tailwind.config.ts](mdc:tailwind.config.ts)** - Tailwind CSS configuration
- **[next.config.ts](mdc:next.config.ts)** - Next.js configuration
- **[src/middleware.ts](mdc:src/middleware.ts)** - Route protection middleware
- **[CLAUDE.md](mdc:CLAUDE.md)** - Comprehensive project documentation

### Development Commands
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run lint         # Run ESLint
npm run type-check   # TypeScript type checking
```

## Rule Categories

### 0. Project Status
**[project-status.mdc](mdc:.cursor/rules/project-status.mdc)**
- Current implementation status and what's actually built
- Complete feature inventory with file sizes and line counts
- Technology stack confirmation from package.json
- Production-ready components and development areas

### 1. Project Structure
**[project-structure.mdc](mdc:.cursor/rules/project-structure.mdc)**
- Feature-based architecture overview
- Directory organization principles
- Import conventions and naming patterns
- File organization best practices

### 2. Component Patterns
**[component-patterns.mdc](mdc:.cursor/rules/component-patterns.mdc)**
- React component architecture
- UI component hierarchy (shadcn/ui)
- Form patterns with React Hook Form + Zod
- Dialog, table, and loading state patterns
- Styling conventions with Tailwind CSS

### 3. Database Patterns
**[database-patterns.mdc](mdc:.cursor/rules/database-patterns.mdc)**
- Supabase integration patterns
- Service layer architecture
- Row Level Security (RLS) implementation
- Database client configuration
- Migration and type generation

### 4. Authentication & Authorization
**[authentication-patterns.mdc](mdc:.cursor/rules/authentication-patterns.mdc)**
- Supabase Auth integration
- Route protection with middleware
- User context and session management
- Family group permissions
- Invitation system patterns

### 5. Data Fetching
**[data-fetching-patterns.mdc](mdc:.cursor/rules/data-fetching-patterns.mdc)**
- React Query (TanStack Query) patterns
- Query and mutation hooks
- Cache management strategies
- Error handling and loading states
- Real-time updates with Supabase

### 6. Development Workflow
**[development-workflow.mdc](mdc:.cursor/rules/development-workflow.mdc)**
- Code quality standards
- Testing strategies
- Performance optimization
- Security best practices

### 7. Deployment Checklist
**[deployment-checklist.mdc](mdc:.cursor/rules/deployment-checklist.mdc)**
- Pre-deployment validation checklist
- Common deployment failure patterns
- Local vs production environment differences
- Historical deployment issues and solutions
- Automated prevention strategies

## Key Technologies

### Frontend Stack
- **Next.js 15** - App Router with TypeScript
- **React 19** - Latest React features
- **Tailwind CSS** - Utility-first styling
- **shadcn/ui** - Complete component library (27+ components)
- **React Hook Form** - Form management
- **Zod** - Schema validation
- **TanStack Query** - Server state management
- **Sonner** - Toast notifications
- **Recharts** - Data visualization
- **date-fns** - Date utilities

### Backend Stack
- **Supabase** - PostgreSQL database with Auth
- **Row Level Security** - Database-level permissions
- **Real-time subscriptions** - Live data updates
- **Custom RPC functions** - Complex database operations

## Architecture Principles

### 1. Feature-Based Organization
- Each feature is self-contained in `/src/features/[feature-name]/`
- Currently implemented: `dashboard`, `family-groups`, `transactions`
- Consistent internal structure: components, services, types (hooks where needed)
- Clear separation between feature-specific and shared code

### 2. Service Layer Pattern
- Database operations abstracted into service classes
- Consistent error handling and data transformation
- Feature-specific services in feature directories
- Shared services in `/src/shared/services/`

### 3. Type-Safe Development
- TypeScript strict mode enabled
- Comprehensive type definitions
- Zod schemas for runtime validation
- Generated types from Supabase schema

### 4. Security First
- Route protection via middleware
- Database-level security with RLS
- Input validation on all user data
- Secure authentication flow

## Common Patterns

### Component Creation
```typescript
// Feature component example (from family-groups)
import { Button } from '@/shared/components/ui/button'
import { Card } from '@/shared/components/ui/card'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/shared/components/ui/dialog'
import { useToast } from '@/shared/hooks/use-toast'

interface AddAccountDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  groupId?: string
}

export function AddAccountDialog({ open, onOpenChange, groupId }: AddAccountDialogProps) {
  const { toast } = useToast()
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Add Account</DialogTitle>
        </DialogHeader>
        {/* Form content */}
      </DialogContent>
    </Dialog>
  )
}
```

### Service Integration
```typescript
// Feature service example (from family-groups)
import { supabaseClient } from '@/shared/services/supabase/client'

export async function getFamilyGroupAccounts(groupId: string) {
  const { data, error } = await supabaseClient
    .rpc('get_family_group_accounts', { group_id: groupId })
  
  if (error) throw error
  return data
}

// Usage in component with React Query
const { data: accounts, isLoading } = useQuery({
  queryKey: ['family-group-accounts', groupId],
  queryFn: () => getFamilyGroupAccounts(groupId),
  enabled: !!groupId,
})
```

### Actual Implementation Examples
The project features comprehensive implementations:

**Family Groups Feature** - Complete financial management with:
- **[AddAccountDialog.tsx](mdc:src/features/family-groups/components/AddAccountDialog.tsx)** (359 lines)
- **[AddBudgetDialog.tsx](mdc:src/features/family-groups/components/AddBudgetDialog.tsx)** (410 lines)  
- **[AddTransactionDialog.tsx](mdc:src/features/family-groups/components/AddTransactionDialog.tsx)** (525 lines)
- **[family-finances.ts](mdc:src/features/family-groups/services/family-finances.ts)** (529 lines)

**Dashboard Feature** - Main interface:
- **[dashboard-client.tsx](mdc:src/features/dashboard/components/dashboard-client.tsx)** (754 lines)
- **[dashboard.ts](mdc:src/features/dashboard/services/dashboard.ts)** (577 lines)

## Family Collaboration Features

### Group Context
The application supports family groups where users can:
- Create and manage family groups
- Invite family members via email
- Share financial data within groups
- Switch between personal and group contexts
- Manage group permissions (member, admin, owner)

### Data Sharing Model
- **Private Data**: `group_id` is NULL, belongs to individual user
- **Shared Data**: `group_id` references family group
- **Permission Levels**: Member, Admin, Owner with different capabilities

## Getting Started

1. **Read the project structure** - Start with [project-structure.mdc](mdc:.cursor/rules/project-structure.mdc)
2. **Understand components** - Review [component-patterns.mdc](mdc:.cursor/rules/component-patterns.mdc)
3. **Learn data patterns** - Check [data-fetching-patterns.mdc](mdc:.cursor/rules/data-fetching-patterns.mdc)
4. **Review database setup** - Study [database-patterns.mdc](mdc:.cursor/rules/database-patterns.mdc)
5. **Understand auth flow** - Read [authentication-patterns.mdc](mdc:.cursor/rules/authentication-patterns.mdc)

## Best Practices Summary

- **Always use TypeScript** - Define interfaces for all props and data
- **Follow feature isolation** - Keep feature code within feature directories
- **Use service layer** - Abstract database operations into service classes
- **Implement proper loading states** - Use skeletons and error boundaries
- **Validate all inputs** - Use Zod schemas for validation
- **Handle errors gracefully** - Provide meaningful error messages
- **Optimize performance** - Use React Query caching and optimization patterns
- **Maintain accessibility** - Follow WCAG guidelines and use semantic HTML
