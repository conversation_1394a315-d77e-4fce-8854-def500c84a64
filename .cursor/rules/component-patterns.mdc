---
description: 
globs: 
alwaysApply: false
---
# Component Patterns and UI Conventions

## Component Architecture

### Component Hierarchy
- **[src/shared/components/ui/](mdc:src/shared/components/ui)** - Complete shadcn/ui component library (27+ components)
- **[src/shared/components/layout/](mdc:src/shared/components/layout)** - Layout components (Header, Sidebar)
- **[src/shared/components/](mdc:src/shared/components)** - Provider components (QueryProvider, ThemeProvider)
- **[src/features/[feature]/components/](mdc:src/features)** - Feature-specific components

### Available UI Components
The project includes a complete shadcn/ui component library:
- **Form components**: `form`, `input`, `textarea`, `button`, `checkbox`, `select`, `switch`
- **Layout components**: `card`, `dialog`, `sheet`, `popover`, `dropdown-menu`, `tabs`
- **Feedback components**: `alert`, `toast`, `progress`, `badge`
- **Data components**: `table`, `calendar`, `avatar`
- **Utility components**: `separator`, `scroll-area`, `label`

### Component Types

#### 1. UI Components (shadcn/ui)
```typescript
// Example: Button component
import { Button } from '@/shared/components/ui/button'

<Button variant="outline" size="sm">
  Click me
</Button>
```

#### 2. Layout Components
```typescript
// Header component usage
import { Header } from '@/shared/components/layout/Header'

// Sidebar component usage  
import { Sidebar } from '@/shared/components/layout/Sidebar'
```

#### 3. Feature Components
```typescript
// Feature-specific component
import { AccountCard } from '@/features/accounts/components/AccountCard'
import { TransactionForm } from '@/features/transactions/components/TransactionForm'
```

## Component Conventions

### File Structure
```typescript
// Component file structure
ComponentName/
├── index.ts          # Export barrel
├── ComponentName.tsx # Main component
├── ComponentName.types.ts # TypeScript interfaces
└── ComponentName.stories.tsx # Storybook stories (if applicable)
```

### Component Template
```typescript
import React from 'react'
import { cn } from '@/shared/utils'

interface ComponentNameProps {
  className?: string
  children?: React.ReactNode
  // Other props...
}

export function ComponentName({ 
  className,
  children,
  ...props 
}: ComponentNameProps) {
  return (
    <div className={cn("base-styles", className)} {...props}>
      {children}
    </div>
  )
}
```

## Form Patterns

### Form Components
Use React Hook Form with Zod validation:

```typescript
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/shared/components/ui/form'
import { Input } from '@/shared/components/ui/input'
import { Button } from '@/shared/components/ui/button'

const formSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  amount: z.number().min(0, 'Amount must be positive'),
})

type FormData = z.infer<typeof formSchema>

export function ExampleForm() {
  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      amount: 0,
    },
  })

  const onSubmit = (data: FormData) => {
    // Handle form submission
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Name</FormLabel>
              <FormControl>
                <Input {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button type="submit">Submit</Button>
      </form>
    </Form>
  )
}
```

## Dialog Patterns

### Modal Components
```typescript
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/shared/components/ui/dialog'
import { Button } from '@/shared/components/ui/button'

export function ExampleDialog() {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button>Open Dialog</Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Dialog Title</DialogTitle>
        </DialogHeader>
        {/* Dialog content */}
      </DialogContent>
    </Dialog>
  )
}
```

## Data Loading Patterns

### Loading States
```typescript
import { Skeleton } from '@/shared/components/ui/skeleton'
import { Alert } from '@/shared/components/ui/alert'

export function DataComponent() {
  const { data, isLoading, error } = useQuery({
    queryKey: ['data'],
    queryFn: fetchData,
  })

  if (isLoading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-3/4" />
        <Skeleton className="h-4 w-1/2" />
      </div>
    )
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>
          Failed to load data. Please try again.
        </AlertDescription>
      </Alert>
    )
  }

  return (
    <div>
      {/* Render data */}
    </div>
  )
}
```

## Table Patterns

### Data Tables
```typescript
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/shared/components/ui/table'

interface DataTableProps<T> {
  data: T[]
  columns: Column<T>[]
}

export function DataTable<T>({ data, columns }: DataTableProps<T>) {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          {columns.map((column) => (
            <TableHead key={column.id}>{column.header}</TableHead>
          ))}
        </TableRow>
      </TableHeader>
      <TableBody>
        {data.map((row, index) => (
          <TableRow key={index}>
            {columns.map((column) => (
              <TableCell key={column.id}>
                {column.cell(row)}
              </TableCell>
            ))}
          </TableRow>
        ))}
      </TableBody>
    </Table>
  )
}
```

## Styling Conventions

### Tailwind CSS Usage
- Use Tailwind utility classes for styling
- Combine classes with `cn()` utility for conditional styling
- Prefer component variants over custom CSS

### Class Organization
```typescript
// Group related classes together
const buttonClasses = cn(
  // Base styles
  "inline-flex items-center justify-center rounded-md font-medium transition-colors",
  // Focus styles
  "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2",
  // Disabled styles
  "disabled:pointer-events-none disabled:opacity-50",
  // Variant styles
  variant === "default" && "bg-primary text-primary-foreground hover:bg-primary/90",
  variant === "outline" && "border border-input hover:bg-accent hover:text-accent-foreground",
  // Size styles
  size === "sm" && "h-9 px-3 text-sm",
  size === "lg" && "h-11 px-8 text-lg",
  // Custom classes
  className
)
```

## Accessibility Patterns

### ARIA Labels and Roles
```typescript
<button
  aria-label="Close dialog"
  aria-expanded={isOpen}
  role="button"
  tabIndex={0}
>
  <X className="h-4 w-4" />
</button>
```

### Keyboard Navigation
```typescript
const handleKeyDown = (event: React.KeyboardEvent) => {
  if (event.key === 'Enter' || event.key === ' ') {
    event.preventDefault()
    onClick()
  }
}
```

## Performance Patterns

### Memoization
```typescript
import { memo, useMemo, useCallback } from 'react'

const ExpensiveComponent = memo(({ data }: { data: any[] }) => {
  const processedData = useMemo(() => {
    return data.map(item => expensiveProcessing(item))
  }, [data])

  const handleClick = useCallback((id: string) => {
    // Handle click
  }, [])

  return (
    <div>
      {processedData.map(item => (
        <div key={item.id} onClick={() => handleClick(item.id)}>
          {item.name}
        </div>
      ))}
    </div>
  )
})
```

## Error Boundary Pattern

### Error Handling
```typescript
import { ErrorBoundary } from 'react-error-boundary'

function ErrorFallback({ error, resetErrorBoundary }: any) {
  return (
    <Alert variant="destructive">
      <AlertDescription>
        Something went wrong: {error.message}
        <Button onClick={resetErrorBoundary} className="mt-2">
          Try again
        </Button>
      </AlertDescription>
    </Alert>
  )
}

export function ComponentWithErrorBoundary() {
  return (
    <ErrorBoundary FallbackComponent={ErrorFallback}>
      <YourComponent />
    </ErrorBoundary>
  )
}
```

## Icon Usage

### Lucide React Icons
```typescript
import { Plus, Edit, Trash2, ArrowRight } from 'lucide-react'

// Consistent icon sizing
<Plus className="h-4 w-4" />
<Edit className="h-5 w-5" />
<Trash2 className="h-4 w-4 text-destructive" />
```

## Component Export Patterns

### Barrel Exports
```typescript
// index.ts
export { ComponentName } from './ComponentName'
export type { ComponentNameProps } from './ComponentName.types'
```
