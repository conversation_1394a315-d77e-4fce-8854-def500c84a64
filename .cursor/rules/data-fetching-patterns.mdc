---
description: 
globs: 
alwaysApply: false
---
# Data Fetching Patterns with React Query

## React Query Setup

### Query Client Configuration
- **[src/shared/components/query-provider.tsx](mdc:src/shared/components/query-provider.tsx)** - Query client provider setup
- **[src/app/layout.tsx](mdc:src/app/layout.tsx)** - App-level provider setup

### Toast Integration
The project uses **Sonner** for toast notifications with the **[use-toast.ts](mdc:src/shared/hooks/use-toast.ts)** hook.

### Provider Setup
```typescript
'use client'

import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import { useState } from 'react'

export function QueryProvider({ children }: { children: React.ReactNode }) {
  const [queryClient] = useState(() => new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: 1000 * 60 * 5, // 5 minutes
        gcTime: 1000 * 60 * 10, // 10 minutes (formerly cacheTime)
        retry: (failureCount, error) => {
          // Don't retry on 4xx errors
          if (error.status >= 400 && error.status < 500) {
            return false
          }
          return failureCount < 3
        },
      },
      mutations: {
        retry: false,
      },
    },
  }))

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  )
}
```

## Query Patterns

### Basic Query Hook
```typescript
import { useQuery } from '@tanstack/react-query'
import { AccountService } from '@/features/accounts/services/AccountService'

export function useAccounts() {
  return useQuery({
    queryKey: ['accounts'],
    queryFn: AccountService.getAccounts,
    staleTime: 1000 * 60 * 5, // 5 minutes
  })
}
```

### Parameterized Query
```typescript
export function useAccount(accountId: string) {
  return useQuery({
    queryKey: ['accounts', accountId],
    queryFn: () => AccountService.getAccount(accountId),
    enabled: !!accountId, // Only run if accountId exists
  })
}
```

### Dependent Queries
```typescript
export function useAccountTransactions(accountId: string) {
  const { data: account } = useAccount(accountId)
  
  return useQuery({
    queryKey: ['transactions', 'account', accountId],
    queryFn: () => TransactionService.getByAccount(accountId),
    enabled: !!account, // Only run after account is loaded
  })
}
```

### Infinite Query Pattern
```typescript
export function useTransactionsPaginated(filters?: TransactionFilters) {
  return useInfiniteQuery({
    queryKey: ['transactions', 'paginated', filters],
    queryFn: ({ pageParam = 0 }) => 
      TransactionService.getPaginated(pageParam, filters),
    getNextPageParam: (lastPage, pages) => {
      return lastPage.hasMore ? pages.length : undefined
    },
    initialPageParam: 0,
  })
}
```

## Mutation Patterns

### Basic Mutation
```typescript
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'

export function useCreateAccount() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: AccountService.createAccount,
    onSuccess: (newAccount) => {
      // Invalidate and refetch accounts
      queryClient.invalidateQueries({ queryKey: ['accounts'] })
      
      // Optimistically add to cache
      queryClient.setQueryData(['accounts'], (old: Account[] = []) => [
        ...old,
        newAccount,
      ])
      
      toast.success('Account created successfully')
    },
    onError: (error) => {
      toast.error(error.message || 'Failed to create account')
    },
  })
}
```

### Optimistic Updates
```typescript
export function useUpdateAccount() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<Account> }) =>
      AccountService.updateAccount(id, data),
    onMutate: async ({ id, data }) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['accounts', id] })
      
      // Snapshot previous value
      const previousAccount = queryClient.getQueryData(['accounts', id])
      
      // Optimistically update
      queryClient.setQueryData(['accounts', id], (old: Account) => ({
        ...old,
        ...data,
      }))
      
      return { previousAccount }
    },
    onError: (err, variables, context) => {
      // Rollback on error
      if (context?.previousAccount) {
        queryClient.setQueryData(['accounts', variables.id], context.previousAccount)
      }
      toast.error('Failed to update account')
    },
    onSettled: (data, error, variables) => {
      // Always invalidate after mutation
      queryClient.invalidateQueries({ queryKey: ['accounts', variables.id] })
    },
  })
}
```

## Service Layer Integration

### Service Class Pattern
```typescript
// Feature-specific service
export class AccountService {
  static async getAccounts(): Promise<Account[]> {
    const { data, error } = await supabaseClient
      .from('accounts')
      .select('*')
      .order('created_at', { ascending: false })
    
    if (error) throw new Error(error.message)
    return data || []
  }
  
  static async createAccount(accountData: CreateAccountData): Promise<Account> {
    const { data, error } = await supabaseClient
      .from('accounts')
      .insert(accountData)
      .select()
      .single()
    
    if (error) throw new Error(error.message)
    return data
  }
  
  static async updateAccount(id: string, updates: Partial<Account>): Promise<Account> {
    const { data, error } = await supabaseClient
      .from('accounts')
      .update(updates)
      .eq('id', id)
      .select()
      .single()
    
    if (error) throw new Error(error.message)
    return data
  }
  
  static async deleteAccount(id: string): Promise<void> {
    const { error } = await supabaseClient
      .from('accounts')
      .delete()
      .eq('id', id)
    
    if (error) throw new Error(error.message)
  }
}
```

### RPC Function Queries
```typescript
// For complex database operations
export function useFinancialSummary(userId: string, groupId?: string) {
  return useQuery({
    queryKey: ['financial-summary', userId, groupId],
    queryFn: async () => {
      const { data, error } = await supabaseClient
        .rpc('get_user_financial_summary', {
          user_id: userId,
          group_id: groupId,
        })
      
      if (error) throw new Error(error.message)
      return data
    },
    enabled: !!userId,
  })
}
```

## Error Handling

### Global Error Handling
```typescript
// In QueryProvider
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: (failureCount, error) => {
        // Don't retry on authentication errors
        if (error.status === 401) {
          // Redirect to login
          window.location.href = '/auth/login'
          return false
        }
        
        // Don't retry on client errors
        if (error.status >= 400 && error.status < 500) {
          return false
        }
        
        return failureCount < 3
      },
      onError: (error) => {
        // Global error handling
        console.error('Query error:', error)
        
        if (error.status === 401) {
          toast.error('Session expired. Please log in again.')
        }
      },
    },
  },
})
```

### Component Error Handling
```typescript
export function AccountsList() {
  const { data: accounts, isLoading, error } = useAccounts()
  
  if (isLoading) {
    return <AccountsListSkeleton />
  }
  
  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>
          Failed to load accounts. {error.message}
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => queryClient.invalidateQueries({ queryKey: ['accounts'] })}
            className="ml-2"
          >
            Retry
          </Button>
        </AlertDescription>
      </Alert>
    )
  }
  
  return (
    <div className="space-y-4">
      {accounts?.map((account) => (
        <AccountCard key={account.id} account={account} />
      ))}
    </div>
  )
}
```

## Cache Management

### Cache Invalidation Strategies
```typescript
// Invalidate specific queries
queryClient.invalidateQueries({ queryKey: ['accounts'] })

// Invalidate all queries for a feature
queryClient.invalidateQueries({ queryKey: ['transactions'] })

// Invalidate queries with specific parameters
queryClient.invalidateQueries({ 
  queryKey: ['transactions', 'account', accountId] 
})

// Remove specific query from cache
queryClient.removeQueries({ queryKey: ['accounts', accountId] })
```

### Cache Updates
```typescript
// Update cache directly
queryClient.setQueryData(['accounts', accountId], updatedAccount)

// Update cache with function
queryClient.setQueryData(['accounts'], (old: Account[] = []) => 
  old.map(account => 
    account.id === updatedAccount.id ? updatedAccount : account
  )
)
```

## Loading States

### Skeleton Components
```typescript
export function AccountsListSkeleton() {
  return (
    <div className="space-y-4">
      {Array.from({ length: 3 }).map((_, i) => (
        <Card key={i} className="p-4">
          <div className="space-y-2">
            <Skeleton className="h-4 w-1/3" />
            <Skeleton className="h-6 w-1/4" />
            <Skeleton className="h-3 w-1/2" />
          </div>
        </Card>
      ))}
    </div>
  )
}
```

### Loading States in Components
```typescript
export function TransactionForm() {
  const createTransaction = useCreateTransaction()
  
  return (
    <form onSubmit={handleSubmit}>
      {/* Form fields */}
      
      <Button 
        type="submit" 
        disabled={createTransaction.isPending}
      >
        {createTransaction.isPending ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Creating...
          </>
        ) : (
          'Create Transaction'
        )}
      </Button>
    </form>
  )
}
```

## Real-time Updates

### Supabase Realtime Integration
```typescript
export function useRealtimeTransactions() {
  const queryClient = useQueryClient()
  
  useEffect(() => {
    const subscription = supabaseClient
      .channel('transactions')
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: 'transactions' },
        (payload) => {
          // Invalidate transactions queries on any change
          queryClient.invalidateQueries({ queryKey: ['transactions'] })
        }
      )
      .subscribe()
    
    return () => {
      subscription.unsubscribe()
    }
  }, [queryClient])
}
```

## Background Refetching

### Automatic Refetching
```typescript
// Refetch on window focus
export function useAccountsWithRefresh() {
  return useQuery({
    queryKey: ['accounts'],
    queryFn: AccountService.getAccounts,
    refetchOnWindowFocus: true,
    refetchInterval: 1000 * 60 * 5, // Refetch every 5 minutes
  })
}
```

### Manual Refetching
```typescript
export function RefreshButton() {
  const queryClient = useQueryClient()
  const [isRefreshing, setIsRefreshing] = useState(false)
  
  const handleRefresh = async () => {
    setIsRefreshing(true)
    await queryClient.invalidateQueries({ queryKey: ['accounts'] })
    setIsRefreshing(false)
  }
  
  return (
    <Button onClick={handleRefresh} disabled={isRefreshing}>
      <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
    </Button>
  )
}
```
