---
description: 
globs: 
alwaysApply: false
---
# Development Workflow and Best Practices

## Development Environment

### Prerequisites
- Node.js ^20.0.0
- npm ^10.0.0
- Supabase CLI (for database management)
- VS Code with recommended extensions

### Environment Setup
1. Copy environment variables from `.env.example` to `.env.local`
2. Configure Supabase credentials:
   - `NEXT_PUBLIC_SUPABASE_URL`
   - `NEXT_PUBLIC_SUPABASE_ANON_KEY`
   - `SUPABASE_SERVICE_ROLE_KEY`
3. Set `NEXT_PUBLIC_APP_URL=http://localhost:3000` for development

### Essential Commands
```bash
# Development
npm run dev              # Start development server (port 3000)
npm run build           # Create production build
npm run start           # Start production server
npm run lint            # Run ESLint checks
npm run type-check      # Run TypeScript type checking

# Pre-Deployment Validation (Run before every commit)
npm run build           # Full production build (same as Netlify)
npx tsc --noEmit       # TypeScript compilation check
npm run lint           # ESLint validation

# Database
supabase start          # Start local Supabase
supabase db reset       # Reset local database
supabase gen types typescript --local > src/shared/types/supabase.ts

# Utility
./scripts/kill-servers.sh  # Kill development servers
```

## Code Quality Standards

### TypeScript Configuration
- **Strict mode enabled** - All TypeScript strict checks are enforced
- **Path aliases** - Use `@/` for imports from `src/` directory
- **Type-first development** - Define interfaces before implementation

### ESLint Rules
- **[eslint.config.mjs](mdc:eslint.config.mjs)** - ESLint configuration
- **React Hooks rules** - Enforced for proper hook usage
- **TypeScript recommended** - Standard TypeScript linting
- **Import organization** - Consistent import ordering

### Code Style
```typescript
// Use 2-space indentation
// Single quotes for strings
// Trailing commas in multiline structures
// Descriptive variable names

interface UserAccount {
  id: string
  name: string
  balance: number
  createdAt: Date
}

const formatAccountBalance = (account: UserAccount): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(account.balance)
}
```

## Git Workflow

### Branch Naming
- `feature/feature-name` - New features
- `fix/bug-description` - Bug fixes
- `refactor/component-name` - Code refactoring
- `docs/update-description` - Documentation updates

### Commit Message Format
```
type(scope): description

Examples:
feat(auth): add password reset functionality
fix(transactions): resolve category filtering bug
refactor(components): restructure form components
docs(readme): update installation instructions
```

## Testing Strategy

### Test Structure
```
src/
├── features/
│   └── [feature]/
│       ├── __tests__/
│       │   ├── components/
│       │   ├── hooks/
│       │   └── services/
│       └── ...
└── shared/
    └── __tests__/
        ├── components/
        ├── hooks/
        └── utils/
```

### Testing Patterns
```typescript
// Component testing
import { render, screen } from '@testing-library/react'
import { AccountCard } from '../AccountCard'

describe('AccountCard', () => {
  it('displays account information correctly', () => {
    const account = {
      id: '1',
      name: 'Checking Account',
      balance: 1000,
    }
    
    render(<AccountCard account={account} />)
    
    expect(screen.getByText('Checking Account')).toBeInTheDocument()
    expect(screen.getByText('$1,000.00')).toBeInTheDocument()
  })
})
```

## Performance Guidelines

### Bundle Optimization
- **Dynamic imports** for route-based code splitting
- **Tree shaking** - Import only what you need
- **Image optimization** - Use Next.js Image component
- **Bundle analysis** - Regular bundle size monitoring

### React Performance
```typescript
// Use React.memo for expensive components
const ExpensiveComponent = React.memo(({ data }) => {
  // Component logic
})

// Use useMemo for expensive calculations
const processedData = useMemo(() => {
  return data.map(item => expensiveCalculation(item))
}, [data])

// Use useCallback for event handlers
const handleClick = useCallback((id: string) => {
  // Handle click
}, [])
```

### Database Performance
- **Query optimization** - Use indexes and efficient queries
- **Data pagination** - Implement pagination for large datasets
- **Caching strategy** - Use React Query with appropriate stale times
- **RPC functions** - Use for complex database operations

## Security Best Practices

### Authentication & Authorization
- **Route protection** - Use middleware for protected routes
- **RLS policies** - Database-level security with Row Level Security
- **Input validation** - Validate all user inputs with Zod schemas
- **Error handling** - Don't expose sensitive information in errors

### Data Validation
```typescript
import { z } from 'zod'

// Define validation schemas
const createAccountSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long'),
  initialBalance: z.number().min(0, 'Balance must be positive'),
  accountType: z.enum(['checking', 'savings', 'credit']),
})

// Use in forms and API routes
type CreateAccountData = z.infer<typeof createAccountSchema>
```

### Environment Variables
- **Never commit secrets** - Use `.env.local` for sensitive data
- **Validate environment** - Check required variables at startup
- **Separate environments** - Different configs for dev/staging/prod

## Accessibility Standards

### WCAG 2.1 Compliance
- **Semantic HTML** - Use proper HTML elements
- **ARIA labels** - Provide accessible names and descriptions
- **Keyboard navigation** - Ensure all functionality is keyboard accessible
- **Color contrast** - Meet minimum contrast ratios
- **Focus management** - Proper focus indicators and management

### Implementation Examples
```typescript
// Accessible button
<button
  aria-label="Delete transaction"
  aria-describedby="delete-help-text"
  onClick={handleDelete}
>
  <Trash2 className="h-4 w-4" />
</button>

// Accessible form field
<FormField
  control={form.control}
  name="amount"
  render={({ field }) => (
    <FormItem>
      <FormLabel>Amount</FormLabel>
      <FormControl>
        <Input
          {...field}
          type="number"
          aria-describedby="amount-error"
        />
      </FormControl>
      <FormMessage id="amount-error" />
    </FormItem>
  )}
/>
```

## Error Handling Strategy

### Error Boundaries
```typescript
// Feature-level error boundary
export function FeatureErrorBoundary({ children }: { children: React.ReactNode }) {
  return (
    <ErrorBoundary
      FallbackComponent={({ error, resetErrorBoundary }) => (
        <Alert variant="destructive">
          <AlertDescription>
            Something went wrong in this section.
            <Button onClick={resetErrorBoundary} className="mt-2">
              Try again
            </Button>
          </AlertDescription>
        </Alert>
      )}
    >
      {children}
    </ErrorBoundary>
  )
}
```

### API Error Handling
```typescript
// Consistent error response format
interface ApiError {
  message: string
  code?: string
  field?: string
}

// Error handling in services
export class AccountService {
  static async createAccount(data: CreateAccountData): Promise<{ success: boolean; data?: Account; error?: ApiError }> {
    try {
      const result = await supabase
        .from('accounts')
        .insert(data)
        .select()
        .single()
      
      return { success: true, data: result.data }
    } catch (error) {
      return { 
        success: false, 
        error: { 
          message: 'Failed to create account',
          code: error.code 
        } 
      }
    }
  }
}
```

## Documentation Standards

### Code Documentation
```typescript
/**
 * Formats a currency amount with proper localization
 * @param amount - The numeric amount to format
 * @param currency - The currency code (default: USD)
 * @param locale - The locale for formatting (default: en-US)
 * @returns Formatted currency string
 */
export function formatCurrency(
  amount: number, 
  currency = 'USD', 
  locale = 'en-US'
): string {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency,
  }).format(amount)
}
```

### Component Documentation
```typescript
/**
 * AccountCard displays account information in a card format
 * 
 * @example
 * ```tsx
 * <AccountCard 
 *   account={account} 
 *   onEdit={handleEdit}
 *   onDelete={handleDelete}
 * />
 * ```
 */
interface AccountCardProps {
  /** The account data to display */
  account: Account
  /** Callback when edit button is clicked */
  onEdit?: (account: Account) => void
  /** Callback when delete button is clicked */
  onDelete?: (accountId: string) => void
  /** Additional CSS classes */
  className?: string
}
```

## Deployment Checklist

### Pre-deployment
- [ ] All tests passing
- [ ] TypeScript compilation successful
- [ ] ESLint checks passing
- [ ] Bundle size within limits
- [ ] Performance audit completed
- [ ] Security audit completed
- [ ] Database migrations applied
- [ ] Environment variables configured

### Post-deployment
- [ ] Health checks passing
- [ ] Error monitoring active
- [ ] Performance monitoring active
- [ ] Database connections healthy
- [ ] Feature flags configured (if applicable)
