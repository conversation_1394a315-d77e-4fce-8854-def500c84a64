---
description: 
globs: 
alwaysApply: false
---
# Project Structure Guide

## Overview
This budget tracker uses a **feature-based architecture** with clear separation between shared and feature-specific code.

## Main Directory Structure

### Core Directories
- **[src/app/](mdc:src/app)** - Next.js App Router pages and layouts
- **[src/features/](mdc:src/features)** - Feature-based modules (accounts, budgets, transactions, etc.)
- **[src/shared/](mdc:src/shared)** - Shared components, hooks, services, and utilities
- **[src/lib/](mdc:src/lib)** - Third-party configurations (Supabase)

## Feature-Based Organization

Each feature in [src/features/](mdc:src/features) follows this consistent structure:
```
features/[feature-name]/
├── components/     # Feature-specific UI components
├── hooks/         # Feature-specific React hooks
├── services/      # Feature-specific API/database operations
├── types/         # Feature-specific TypeScript types
└── utils/         # Feature-specific utility functions
```

### Available Features
- **[dashboard/](mdc:src/features/dashboard)** - Main dashboard components and logic
  - Contains the main dashboard client component and dashboard services
- **[family-groups/](mdc:src/features/family-groups)** - Family collaboration features
  - Complete family financial management with dialogs for accounts, budgets, categories, goals, and transactions
  - Family group services and types
- **[transactions/](mdc:src/features/transactions)** - Transaction management services
  - Core transaction processing and server-side operations

### Feature Structure
Each implemented feature contains:
- **components/** - React components specific to the feature
- **services/** - Database operations and business logic  
- **types/** - TypeScript type definitions (where applicable)

## Shared Resources

### [src/shared/](mdc:src/shared)
- **[components/](mdc:src/shared/components)** - Reusable UI components
  - **[ui/](mdc:src/shared/components/ui)** - Complete shadcn/ui component library (27+ components)
  - **[layout/](mdc:src/shared/components/layout)** - Header and sidebar components
  - **[query-provider.tsx](mdc:src/shared/components/query-provider.tsx)** - React Query provider
  - **[theme-provider.tsx](mdc:src/shared/components/theme-provider.tsx)** - Theme context provider
- **[hooks/](mdc:src/shared/hooks)** - Shared React hooks
  - **[use-toast.ts](mdc:src/shared/hooks/use-toast.ts)** - Toast notification hook
  - **[useUserSettings.ts](mdc:src/shared/hooks/useUserSettings.ts)** - User settings management
- **[services/](mdc:src/shared/services)** - Shared API services and database clients
  - **[supabase/](mdc:src/shared/services/supabase)** - Complete Supabase client setup
- **[contexts/](mdc:src/shared/contexts)** - React contexts
  - **[UserSettingsContext.tsx](mdc:src/shared/contexts/UserSettingsContext.tsx)** - User settings context
  - **[mobile-menu-context.tsx](mdc:src/shared/contexts/mobile-menu-context.tsx)** - Mobile navigation context
- **[types/](mdc:src/shared/types)** - Shared TypeScript types
  - **[supabase.ts](mdc:src/shared/types/supabase.ts)** - Generated Supabase types (719 lines)
  - **[index.ts](mdc:src/shared/types/index.ts)** - Core application types
  - **[family.ts](mdc:src/shared/types/family.ts)** - Family-related types
- **[utils/](mdc:src/shared/utils)** - Utility functions
  - **[dashboard.ts](mdc:src/shared/utils/dashboard.ts)** - Dashboard utilities
  - **[index.ts](mdc:src/shared/utils/index.ts)** - Common utilities

## Import Conventions

### Feature Imports
```typescript
// Import from same feature
import { AccountCard } from './components/AccountCard'
import { useAccounts } from './hooks/useAccounts'

// Import from other features
import { TransactionService } from '@/features/transactions/services'
import type { Budget } from '@/features/budgets/types'
```

### Shared Imports
```typescript
// Shared components
import { Button } from '@/shared/components/ui/button'
import { Header } from '@/shared/components/layout/header'

// Shared services
import { supabaseClient } from '@/shared/services/supabase/client'

// Shared utilities and hooks
import { formatCurrency } from '@/shared/utils'
import { useToast } from '@/shared/hooks/use-toast'
import { useUserSettings } from '@/shared/hooks/useUserSettings'
```

## File Naming Conventions
- **Components**: PascalCase (e.g., `AccountCard.tsx`)
- **Hooks**: camelCase starting with "use" (e.g., `useAccounts.ts`)
- **Services**: PascalCase ending with "Service" (e.g., `AccountService.ts`)
- **Types**: PascalCase for interfaces, camelCase for type aliases
- **Utils**: camelCase (e.g., `formatCurrency.ts`)

## Key Principles
1. **Feature Isolation**: Each feature should be self-contained
2. **Shared Resources**: Common code goes in shared/ directory
3. **Consistent Structure**: All features follow the same internal organization
4. **Clear Dependencies**: Features can depend on shared/ but not on each other directly
5. **Type Safety**: Comprehensive TypeScript types for all data structures
