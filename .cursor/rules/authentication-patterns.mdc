---
description: 
globs: 
alwaysApply: false
---
# Authentication and Authorization Patterns

## Authentication Architecture

### Supabase Auth Integration
- **[src/middleware.ts](mdc:src/middleware.ts)** - Route protection middleware
- **[src/shared/services/supabase/client.ts](mdc:src/shared/services/supabase/client.ts)** - Client-side auth
- **[src/shared/services/supabase/server.ts](mdc:src/shared/services/supabase/server.ts)** - Server-side auth

### Authentication Flow
1. **Login/Signup** - Users authenticate via Supabase Auth
2. **Session Management** - Automatic session handling with cookies
3. **Route Protection** - Middleware protects `/dashboard/*` routes
4. **User Profile** - Auto-created via database trigger on signup

## Route Protection

### Middleware Configuration
```typescript
// src/middleware.ts
import { createServerClient } from '@supabase/ssr'
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export async function middleware(request: NextRequest) {
  // Protect dashboard routes
  if (request.nextUrl.pathname.startsWith('/dashboard')) {
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return NextResponse.redirect(new URL('/auth/login', request.url))
    }
  }
  
  return NextResponse.next()
}

export const config = {
  matcher: ['/dashboard/:path*']
}
```

### Protected Route Pattern
```typescript
// In page components
export default async function ProtectedPage() {
  const supabase = createServerClient()
  const { data: { user } } = await supabase.auth.getUser()
  
  if (!user) {
    redirect('/auth/login')
  }
  
  return <PageContent user={user} />
}
```

## Authentication Pages

### Auth Page Structure
- **[src/app/auth/login/page.tsx](mdc:src/app/auth/login/page.tsx)** - Login form
- **[src/app/auth/signup/page.tsx](mdc:src/app/auth/signup/page.tsx)** - Registration form
- **[src/app/auth/forgot-password/page.tsx](mdc:src/app/auth/forgot-password/page.tsx)** - Password reset
- **[src/app/auth/update-password/page.tsx](mdc:src/app/auth/update-password/page.tsx)** - Password update

### Login Form Pattern
```typescript
import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { supabaseClient } from '@/shared/services/supabase/client'
import { Button } from '@/shared/components/ui/button'
import { Input } from '@/shared/components/ui/input'
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from '@/shared/components/ui/form'

export function LoginForm() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  
  const form = useForm({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  })

  const onSubmit = async (data: LoginFormData) => {
    setIsLoading(true)
    
    try {
      const { error } = await supabaseClient.auth.signInWithPassword({
        email: data.email,
        password: data.password,
      })
      
      if (error) throw error
      
      router.push('/dashboard')
      router.refresh()
    } catch (error) {
      toast.error('Failed to sign in')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input type="email" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Password</FormLabel>
              <FormControl>
                <Input type="password" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <Button type="submit" disabled={isLoading} className="w-full">
          {isLoading ? 'Signing in...' : 'Sign In'}
        </Button>
      </form>
    </Form>
  )
}
```

## User Context and State

### User Profile Management
```typescript
// Get current user
const getCurrentUser = async () => {
  const { data: { user } } = await supabaseClient.auth.getUser()
  return user
}

// Get user profile (extended data)
const getUserProfile = async (userId: string) => {
  const { data, error } = await supabaseClient
    .from('user_profiles')
    .select('*')
    .eq('id', userId)
    .single()
    
  if (error) throw error
  return data
}
```

### Auth State Hook
```typescript
// Custom hook for auth state
export function useAuth() {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Get initial session
    supabaseClient.auth.getSession().then(({ data: { session } }) => {
      setUser(session?.user ?? null)
      setLoading(false)
    })

    // Listen for auth changes
    const { data: { subscription } } = supabaseClient.auth.onAuthStateChange(
      (_event, session) => {
        setUser(session?.user ?? null)
        setLoading(false)
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  return { user, loading }
}
```

## Row Level Security (RLS)

### RLS Policy Patterns
```sql
-- User can only access their own data
CREATE POLICY "Users can access own data" ON user_profiles
  FOR ALL USING (auth.uid() = id);

-- Family group members can access shared data
CREATE POLICY "Family members can access group data" ON transactions
  FOR ALL USING (
    group_id IS NULL AND user_id = auth.uid() OR
    group_id IN (
      SELECT group_id FROM family_group_members 
      WHERE user_id = auth.uid()
    )
  );

-- Only group admins can manage group settings
CREATE POLICY "Admins can manage group" ON family_groups
  FOR UPDATE USING (
    id IN (
      SELECT group_id FROM family_group_members 
      WHERE user_id = auth.uid() AND role IN ('admin', 'owner')
    )
  );
```

### RLS Helper Functions
```sql
-- Check if user is family group member
CREATE OR REPLACE FUNCTION is_family_group_member(group_uuid UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM family_group_members
    WHERE group_id = group_uuid AND user_id = auth.uid()
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Check if user is group admin
CREATE OR REPLACE FUNCTION is_family_group_admin(group_uuid UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM family_group_members
    WHERE group_id = group_uuid 
    AND user_id = auth.uid() 
    AND role IN ('admin', 'owner')
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## Family Group Authorization

### Group Context Switching
```typescript
// Context for current family group
interface FamilyGroupContextType {
  currentGroup: FamilyGroup | null
  setCurrentGroup: (group: FamilyGroup | null) => void
  userGroups: FamilyGroup[]
  isGroupAdmin: boolean
  isGroupOwner: boolean
}

export function useFamilyGroup() {
  const context = useContext(FamilyGroupContext)
  if (!context) {
    throw new Error('useFamilyGroup must be used within FamilyGroupProvider')
  }
  return context
}
```

### Permission Checking
```typescript
// Check user permissions
export function useGroupPermissions(groupId?: string) {
  const { user } = useAuth()
  
  const { data: membership } = useQuery({
    queryKey: ['group-membership', groupId, user?.id],
    queryFn: async () => {
      if (!groupId || !user?.id) return null
      
      const { data } = await supabaseClient
        .from('family_group_members')
        .select('role')
        .eq('group_id', groupId)
        .eq('user_id', user.id)
        .single()
        
      return data
    },
    enabled: !!groupId && !!user?.id,
  })

  return {
    isMember: !!membership,
    isAdmin: membership?.role === 'admin' || membership?.role === 'owner',
    isOwner: membership?.role === 'owner',
    role: membership?.role,
  }
}
```

## Invitation System

### Invitation Flow
1. **Create Invitation** - Admin creates invitation with email
2. **Send Email** - System sends invitation email with token
3. **Accept Invitation** - User clicks link and accepts invitation
4. **Join Group** - User is added to family group

### Invitation Components
```typescript
// Invitation creation
export async function createInvitation(groupId: string, email: string) {
  const { data, error } = await supabaseClient
    .rpc('create_family_group_invitation', {
      group_id: groupId,
      invited_email: email,
    })
    
  if (error) throw error
  return data
}

// Accept invitation
export async function acceptInvitation(token: string) {
  const { data, error } = await supabaseClient
    .rpc('respond_to_invitation', {
      invitation_token: token,
      response: 'accepted',
    })
    
  if (error) throw error
  return data
}
```

## Logout and Session Management

### Logout Pattern
```typescript
export async function signOut() {
  const { error } = await supabaseClient.auth.signOut()
  
  if (error) {
    toast.error('Failed to sign out')
    return
  }
  
  // Clear any client-side state
  queryClient.clear()
  
  // Redirect to login
  router.push('/auth/login')
  router.refresh()
}
```

### Session Refresh
```typescript
// Automatic session refresh
useEffect(() => {
  const { data: { subscription } } = supabaseClient.auth.onAuthStateChange(
    async (event, session) => {
      if (event === 'SIGNED_OUT' || event === 'TOKEN_REFRESHED') {
        // Handle session changes
        router.refresh()
      }
    }
  )

  return () => subscription.unsubscribe()
}, [])
```

## Security Best Practices

### Input Validation
- **Always validate** email format and password strength
- **Sanitize inputs** before database operations
- **Rate limiting** on authentication endpoints
- **CSRF protection** via Supabase's built-in protections

### Error Handling
```typescript
// Don't expose sensitive information
const handleAuthError = (error: AuthError) => {
  switch (error.message) {
    case 'Invalid login credentials':
      return 'Invalid email or password'
    case 'Email not confirmed':
      return 'Please check your email and confirm your account'
    default:
      return 'An error occurred. Please try again.'
  }
}
```

### Password Requirements
```typescript
const passwordSchema = z.string()
  .min(8, 'Password must be at least 8 characters')
  .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
  .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
  .regex(/[0-9]/, 'Password must contain at least one number')
  .regex(/[^A-Za-z0-9]/, 'Password must contain at least one special character')
```
