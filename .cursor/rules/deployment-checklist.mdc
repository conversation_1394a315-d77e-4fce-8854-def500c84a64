---
description:
globs:
alwaysApply: false
---
# Deployment Checklist and Common Issues

## Pre-Deployment Validation

### Required Checks Before Every Commit
```bash
# Run the exact same checks that Netlify runs
npm run build          # Full production build
npx tsc --noEmit      # TypeScript type checking
npm run lint          # ESLint validation

# Additional checks
npm run type-check     # Alternative TypeScript check
```

### Local vs Production Environment Differences

**Local Development Environment**:
- Often runs with warnings-only mode
- TypeScript errors may show as warnings
- ESLint may allow certain violations
- Hot reload can mask compilation issues

**Production Build Environment (Netlify)**:
- Enforces strict TypeScript compilation
- Zero tolerance for TypeScript errors
- ESLint violations cause build failures
- All dependencies must be properly declared

## Common Deployment Failure Patterns

### 1. Missing useEffect Dependencies
**Symptom**: `React Hook useEffect has a missing dependency`
```typescript
// ❌ WRONG - Missing dependency
useEffect(() => {
  if (currentUserId && groupId) {
    fetchCategories();
  }
}, [currentUserId, groupId]); // Missing fetchCategories

// ✅ CORRECT - Use useCallback
const fetchCategories = useCallback(async () => {
  // Function implementation
}, [currentUserId, groupId]);

useEffect(() => {
  if (currentUserId && groupId) {
    fetchCategories();
  }
}, [currentUserId, groupId, fetchCategories]);
```

### 2. Unused Variables After Refactoring
**Symptom**: `'variableName' is assigned a value but never used`
```typescript
// ❌ WRONG - Unused after refactoring
const { settings } = useUserSettings();
const defaultCurrency = settings.currency; // Not used anymore
const { formatCurrency } = useCurrencyFormatter();

// ✅ CORRECT - Remove unused code
const { formatCurrency } = useCurrencyFormatter();
```

### 3. Unused Import Statements
**Symptom**: `'ImportName' is defined but never used`
```typescript
// ❌ WRONG - Import not used after refactoring
import { useUserSettings } from '@/shared/contexts/UserSettingsContext';
import { useCurrencyFormatter } from '@/shared/hooks/useCurrencyFormatter';

// Only using useCurrencyFormatter now

// ✅ CORRECT - Remove unused imports
import { useCurrencyFormatter } from '@/shared/hooks/useCurrencyFormatter';
```

### 4. Type Mismatches
**Symptom**: Type-related errors that may be warnings locally
```typescript
// ❌ WRONG - Incorrect parameter type
formatCurrency(amount, currency) // formatCurrency only accepts amount now

// ✅ CORRECT - Use updated signature
formatCurrency(amount) // Currency comes from user settings
```

## Historical Deployment Issues (Resolved)

### 2024-12-19: Currency Refactoring Deployment Failures
**Root Cause**: After implementing centralized currency formatting, leftover code caused build failures

**Files Affected**:
- `src/app/dashboard/family/[groupId]/categories/page.tsx` - Missing useEffect dependency
- `src/app/dashboard/family/[groupId]/page.tsx` - Unused variables
- `src/features/dashboard/components/dashboard-client.tsx` - Unused imports

**Resolution Strategy**:
1. Wrapped async functions with `useCallback` when used in useEffect dependencies
2. Removed all unused variables from refactoring
3. Cleaned up import statements no longer needed
4. Verified with production build command

### TypeScript Compilation Issues (Previous)
**Root Cause**: Type conflicts and missing exports after major refactoring

**Common Patterns**:
- Type import conflicts between different modules
- Missing re-exports in service files
- Optional field handling in interfaces
- Enum vs type alias conflicts

## Automated Prevention Strategies

### Git Pre-Commit Hooks (Recommended)
```json
// package.json
{
  "husky": {
    "hooks": {
      "pre-commit": "npm run build && npm run lint"
    }
  }
}
```

### CI/CD Pipeline Checks
```yaml
# .github/workflows/build-check.yml
name: Build Check
on: [push, pull_request]
jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '20'
      - run: npm ci
      - run: npm run build
      - run: npm run lint
      - run: npx tsc --noEmit
```

### Local Development Scripts
```json
// package.json scripts
{
  "scripts": {
    "build:check": "npm run build && npm run lint && npx tsc --noEmit",
    "pre-deploy": "npm run build:check",
    "deploy:safe": "npm run pre-deploy && netlify deploy"
  }
}
```

## Deployment Best Practices

### Before Every Feature Implementation
1. **Run baseline checks** to ensure starting point is clean
2. **Make incremental commits** to isolate potential issues
3. **Test build after major refactoring** before moving to next feature

### During Development
1. **Run TypeScript checks frequently** with `npx tsc --noEmit`
2. **Use strict ESLint configuration** matching production
3. **Test in production mode locally** with `npm run build && npm run start`

### Before Committing
1. **Always run full build** - `npm run build`
2. **Verify TypeScript compilation** - `npx tsc --noEmit`
3. **Check ESLint compliance** - `npm run lint`
4. **Review unused imports/variables** in IDE

### Code Review Checklist
- [ ] No unused variables or imports
- [ ] All useEffect dependencies properly declared
- [ ] TypeScript compilation passes
- [ ] ESLint warnings addressed
- [ ] Production build succeeds

## Debugging Deployment Failures

### Reading Netlify Build Logs
1. **Look for specific error lines** - Usually clearly marked with line numbers
2. **Identify error type** - TypeScript vs ESLint vs compilation
3. **Check recent changes** - What was modified since last successful build
4. **Verify locally** - Reproduce with same commands Netlify uses

### Common Error Messages
```bash
# Missing dependency error
"React Hook useEffect has a missing dependency: 'functionName'"

# Unused variable error  
"'variableName' is assigned a value but never used"

# Unused import error
"'ImportName' is defined but never used"

# Type error
"Type 'X' is not assignable to type 'Y'"
```

### Quick Fix Strategy
1. **Identify the exact error** from build logs
2. **Run the same command locally** that failed on Netlify
3. **Fix the specific issue** without over-engineering
4. **Verify fix with production build** before recommitting
5. **Document the pattern** to prevent recurrence

## Netlify-Specific Considerations

### Build Command Configuration
```toml
# netlify.toml
[build]
  command = "npm run build"
  publish = ".next"

[build.environment]
  NODE_VERSION = "20"
```

### Environment Variables
- Ensure all required environment variables are set in Netlify dashboard
- Match variable names exactly with local `.env.local`
- Verify Supabase credentials are correctly configured

### Build Optimization
- Consider build time limits and optimize if needed
- Use proper caching strategies for dependencies
- Monitor build performance and optimize as project grows
