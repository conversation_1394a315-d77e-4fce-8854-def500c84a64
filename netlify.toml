[build]
  command = "npm run build"
  publish = ".next"

[build.environment]
  NODE_VERSION = "20"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[functions]
  node_bundler = "esbuild"

# Environment variables will need to be set in Netlify dashboard:
# NEXT_PUBLIC_SUPABASE_URL
# NEXT_PUBLIC_SUPABASE_ANON_KEY  
# SUPABASE_SERVICE_ROLE_KEY
# NEXT_PUBLIC_APP_URL (set to your Netlify URL)