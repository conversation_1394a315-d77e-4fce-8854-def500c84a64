# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Core Commands

- `npm run dev` - Start development server (runs on port 3000)
- `npm run build` - Create production build
- `npm run start` - Start production server
- `npm run lint` - Run ESLint checks
- `npm run type-check` - Run TypeScript type checking

### Supabase Commands

- `npx supabase gen types typescript --project-id [PROJECT_ID]` - Generate TypeScript types
- `npx supabase db push` - Push local migrations to remote database
- `npx supabase db reset` - Reset local database (development only)
- `npx supabase start` - Start local Supabase development environment

### Environment Setup

- Copy `.env.example` to `.env.local` if it exists
- Configure Supabase credentials: `NEXT_PUBLIC_SUPABASE_URL`, `NEXT_PUBLIC_SUPABASE_ANON_KEY`, `SUPABASE_SERVICE_ROLE_KEY`
- Set `NEXT_PUBLIC_APP_URL=http://localhost:3000` for development

### Email System Setup

The family invitation system uses Supabase's native email functionality:

- **Built-in Emails**: Uses `supabase.auth.admin.inviteUserByEmail()` for invitation emails
- **Email Templates**: MUST configure custom template in Supabase Dashboard
  - Navigate to Authentication > Email Templates > "Invite user"
  - Replace default template with family group invitation template from `EMAIL_TEMPLATE_SETUP.md`
  - Template uses variables: `{{ .Data.groupName }}`, `{{ .Data.inviterName }}`, `{{ .ConfirmationURL }}`
- **User Flow**: Email → Auth Callback → Password Setup (if needed) → Invitation Page → Auto-accept → Family Group
- **No External Services**: No additional email service configuration required
- **Authentication Flow**: Complete callback system handles email verification and account setup

## Architecture Overview

### Tech Stack

- **Framework**: Next.js 15 with App Router and TypeScript (React 19)
- **Database**: Supabase (PostgreSQL) with Row Level Security (RLS)
- **Styling**: Tailwind CSS with shadcn/ui components (27+ components available)
- **State Management**: TanStack Query (React Query) for server state
- **Authentication**: Supabase Auth with middleware protection
- **Forms**: React Hook Form with Zod validation
- **UI Libraries**: Lucide React (icons), Sonner (toasts), Recharts (charts)
- **Utilities**: date-fns, next-themes (dark/light mode)

### Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── auth/              # Authentication pages (login, signup, etc.)
│   ├── dashboard/         # Protected dashboard pages
│   │   └── family/        # Family group collaboration features
│   └── layout.tsx         # Root layout with providers
├── features/              # Feature-based organization
│   ├── accounts/          # Account management feature
│   ├── analytics/         # Analytics and reporting
│   ├── budgets/          # Budget management
│   ├── dashboard/        # Dashboard components and services
│   ├── family-groups/    # Family collaboration features
│   ├── goals/            # Financial goals tracking
│   └── transactions/     # Transaction management
├── shared/               # Shared resources
│   ├── components/       # Reusable components
│   │   ├── ui/          # shadcn/ui components
│   │   └── layout/      # Header, sidebar components
│   ├── contexts/        # React contexts (UserSettings, MobileMenu)
│   ├── hooks/           # Custom React hooks
│   ├── services/        # API and database services
│   │   └── supabase/   # Supabase client, server actions
│   ├── types/          # TypeScript type definitions
│   └── utils/          # Utility functions
└── lib/                # Third-party configurations
```

### Database Architecture

- **Core Tables**: `user_profiles`, `categories`, `transactions`, `accounts`, `budgets`, `financial_goals`
- **Family Features**: `family_groups`, `family_group_members`, `family_group_invitations`
- **Schema Design**: All tables use UUID primary keys with comprehensive RLS policies
- **Custom RPC Functions**: Complex operations with consistent naming (`get_`, `insert_`, `update_`, `delete_`)
  - `get_family_group_details(p_group_id)` - Get group information with permissions
  - `invite_to_family_group(p_group_id, p_invitee_email)` - Create secure invitation
  - `accept_family_group_invitation(p_invitation_token)` - Process invitation acceptance
  - `get_invitation_by_token(p_token)` - Retrieve invitation details
- **Multi-tenancy**: Family group data shared via `group_id` column, private data has NULL `group_id`
- **Migrations**: 40+ migrations with proper audit trails and schema evolution
- **Type Generation**: Automatic TypeScript types in `src/shared/services/supabase/types.ts`

### Authentication & Authorization

- **Supabase Auth**: Handles user authentication with email/password
- **Middleware Protection**: `src/middleware.ts` protects `/dashboard/*` routes
- **Auth Callback System**: `src/app/auth/callback/route.ts` handles email verification and redirects
- **Password Setup Flow**: `src/app/auth/set-password/page.tsx` for invited users to set passwords
- **Family Invitation Flow**:
  1. Email invitation with Supabase native email
  2. Auth callback processes email link and user metadata
  3. Password setup for new users (auto-skip if already configured)
  4. Invitation page with auto-acceptance for matching emails
  5. Redirect to family group dashboard
- **RLS Policies**: Database-level access control for all tables
- **User Profiles**: Created automatically on signup via database trigger

### Key Patterns

#### Data Fetching

- Use TanStack Query hooks for all server state
- Server actions in `src/shared/services/supabase/server-actions.ts` for mutations
- Feature-specific services in `src/features/*/services/` directories
- RPC functions for complex queries (defined in `src/shared/services/supabase/types.ts`)

#### Component Architecture

- Feature-based component organization in `src/features/*/components/`
- Shared UI components in `src/shared/components/ui/`
- Layout components in `src/shared/components/layout/`
- Functional components with TypeScript interfaces for props
- Form components use React Hook Form + Zod validation
- Barrel exports (`index.ts`) for clean imports

#### Family Group Context

- Users can belong to multiple family groups with role-based permissions (member, admin, owner)
- Data can be private (user-owned) or shared (group-owned) via `group_id` column
- Context switching between personal and family group views in dashboard
- Email invitation system with secure tokens and expiration
- Family features organized in `src/features/family-groups/` (1,800+ lines of implementation)
- Real-time collaboration with shared accounts, budgets, and transactions

### Important Conventions

- Use 2-space indentation consistently
- TypeScript strict mode enabled with comprehensive type safety
- All user inputs validated with Zod schemas (runtime validation)
- Error boundaries and proper loading states throughout
- Responsive design with Tailwind CSS (mobile-first approach)
- Icons exclusively from Lucide React (no emojis, professional appearance)
- Toast notifications with Sonner library
- Path aliases: `@/` maps to `src/` directory

### Security Considerations

- All database operations go through RLS policies
- Server actions validate user permissions
- Environment variables for sensitive data
- Input sanitization and validation
- Secure cookie handling for authentication

### Development Guidelines

- Act as senior developer with modern practices
- Follow best security, performance, and accessibility practices
- Use modern styling with clean code and professional icons
- Prefer editing existing files over creating new ones
- Always check current project structure before making changes
- Implement TypeScript interfaces for all props and data structures
- Feature-based organization: keep feature code isolated in respective directories
- Use service layer pattern for database operations and business logic
- Follow established patterns from existing implementations (refer to family-groups feature)
- Always run `npx tsc --noEmit` for TypeScript type checking before commits

### Feature Implementation Examples

The codebase includes production-ready examples to follow:

**Family Groups Feature** (136KB implementation):
- `AddAccountDialog.tsx` (359 lines) - Complex form with validation
- `AddBudgetDialog.tsx` (410 lines) - Budget creation with period selection
- `AddTransactionDialog.tsx` (525 lines) - Transaction form with category selection
- `family-finances.ts` (529 lines) - Comprehensive service layer

**Dashboard Feature** (64KB implementation):
- `dashboard-client.tsx` (754 lines) - Main dashboard interface
- `dashboard.ts` (577 lines) - Data aggregation service

### Code Quality Standards

- **TypeScript Strict Mode**: All type checking enforced with comprehensive interfaces
- **ESLint Configuration**: React hooks rules, TypeScript recommended, import organization
- **Single Quotes**: Use single quotes for strings with trailing commas in multiline structures
- **Component Documentation**: Use JSDoc comments for complex components and functions

### Development Environment Requirements

- **Node.js ^20.0.0** with npm ^10.0.0
- **Supabase CLI** for database management and type generation
  - Install: `npm install -g supabase`
  - Generate types: `npx supabase gen types typescript --project-id [PROJECT_ID] > src/shared/services/supabase/types.ts`
  - Run migrations: `npx supabase db push` or apply manually in Supabase Dashboard
- **Environment Variables**: Copy `.env.example` to `.env.local` with proper Supabase credentials
- **Database Setup**: Ensure RLS policies are enabled and all RPC functions are deployed

### Performance Patterns

- **React.memo**: Use for expensive components to prevent unnecessary re-renders
- **useMemo/useCallback**: Implement for expensive calculations and event handlers
- **Bundle Optimization**: Dynamic imports for route-based code splitting
- **Query Optimization**: Use React Query caching with appropriate stale times
- **Database Pagination**: Implement for large datasets with RPC functions for complex operations

### Testing Framework

- **Status**: No testing framework currently configured
- **Recommended Setup**: Jest + React Testing Library for unit/integration tests
- **Future Testing Commands**:
  - `npm run test` - Run test suite (to be implemented)
  - `npm run test:watch` - Run tests in watch mode (to be implemented)
  - `npm run test:coverage` - Generate coverage report (to be implemented)

### Security Implementation Details

- **Input Validation**: All user inputs validated with Zod schemas at runtime
- **Row-Level Security**: Database-level access control with comprehensive RLS policies
- **Route Protection**: Middleware-based authentication for all `/dashboard/*` routes
- **Error Handling**: Don't expose sensitive information in error messages
- **Environment Security**: Never commit secrets, validate required variables at startup

### Accessibility Standards (WCAG 2.1)

- **Semantic HTML**: Use proper HTML elements with ARIA labels and descriptions
- **Keyboard Navigation**: Ensure all functionality is keyboard accessible
- **Focus Management**: Proper focus indicators and management throughout the application
- **Color Contrast**: Meet minimum contrast ratios with professional design system

# important-instruction-reminders

Do what has been asked; nothing more, nothing less.
NEVER create files unless they're absolutely necessary for achieving your goal.
ALWAYS prefer editing an existing file to creating a new one.
NEVER proactively create documentation files (\*.md) or README files. Only create documentation files if explicitly requested by the User.

## Recent Updates and Fixes

### 2024-12-19: Currency Consistency Implementation & Deployment Fixes (Commits: 2390769, 820fdc5)

**Major Implementation**: Comprehensive currency consistency across the entire application
**Major Fix**: Resolved all TypeScript compilation and deployment errors preventing Netlify deployment

#### Issues Resolved:

1. **Family Group Type Conflicts**

   - Unified type imports to use consistent `FamilyGroupDetails`/`FamilyGroupMember` from `@/shared/types/family`
   - Fixed optional email field handling in family group member components

2. **Dashboard Service Export Issues**

   - Added missing re-exports for `Budget`, `Goal`, `BudgetPeriod`, `GoalStatus` types
   - Fixed missing `currency` field in Goal objects

3. **TransactionType Conflicts**

   - Resolved enum vs type alias conflicts between shared types
   - Added transaction filtering to only process 'income'/'expense' types
   - Added missing `updated_at` field to `Transaction` interface
   - Replaced conflicting imports with direct string literal types

4. **Auth Callback Route**

   - Fixed Supabase SSR cookie handling for Next.js App Router

5. **Goals Page**

   - Fixed TypeScript indexing error with proper `Record<string, number>` typing

6. **Code Quality**
   - Removed unused imports to resolve ESLint warnings
   - Enhanced type safety throughout the application

#### Build Status:

- ✅ TypeScript compilation: `npx tsc --noEmit` passes
- ✅ Production build: `npm run build` successful
- ✅ ESLint checks: All warnings resolved
- 🚀 Ready for Netlify deployment

#### Files Modified:

- `src/app/auth/callback/route.ts` - Cookie handling fix
- `src/app/dashboard/family/[groupId]/*.tsx` - Type consistency fixes
- `src/app/dashboard/transactions/page.tsx` - Transaction type safety
- `src/app/dashboard/goals/page.tsx` - Indexing error fix
- `src/features/dashboard/services/*` - Export/import fixes
- `src/shared/types/index.ts` - Interface updates

#### Deployment Issues Resolved (Commit: 820fdc5):

**Root Cause**: Netlify deployment failures due to TypeScript/ESLint errors that don't surface in local development

1. **Missing useEffect Dependencies**

   - **Issue**: `fetchCategories` function used in useEffect but missing from dependency array
   - **Fix**: Wrapped `fetchCategories` with `useCallback` and added to dependencies
   - **Location**: `src/app/dashboard/family/[groupId]/categories/page.tsx`

2. **Unused Variables After Refactoring**

   - **Issue**: `defaultCurrency` and `settings` variables left unused after implementing `useCurrencyFormatter`
   - **Fix**: Removed unused variables and imports
   - **Files**: `src/app/dashboard/family/[groupId]/page.tsx`, `src/features/dashboard/components/dashboard-client.tsx`

3. **Unused Import Statements**
   - **Issue**: ESLint failing on unused `useUserSettings` imports
   - **Fix**: Cleaned up imports no longer needed after currency refactoring

#### Prevention Strategy:

**Pre-Deployment Checklist** (run locally before commits):

```bash
npm run build          # Full production build (same as Netlify)
npx tsc --noEmit      # TypeScript type checking
npm run lint          # ESLint validation
```

**Why Netlify Differs from Local Development**:

- Local development often runs with warnings-only mode
- Production builds enforce strict TypeScript compilation
- ESLint rules like `@typescript-eslint/no-unused-vars` are stricter in production
- Zero tolerance for any TypeScript errors or ESLint violations

**Common Deployment Failure Patterns**:

- Missing dependencies in useEffect hooks
- Unused variables after code refactoring
- Import statements for code that was removed
- Type mismatches that are warnings locally but errors in production

**Impact**: All TypeScript compilation and deployment errors resolved, application now builds successfully for production deployment with comprehensive currency consistency.
