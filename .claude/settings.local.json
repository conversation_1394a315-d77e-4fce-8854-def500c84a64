{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(rmdir:*)", "<PERSON><PERSON>(true)", "Bash(find:*)", "Bash(npm run type-check:*)", "Bash(grep:*)", "Bash(rg:*)", "Bash(npm run build:*)", "Bash(git add:*)", "Bash(npm run dev:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(pkill:*)", "Bash(npx supabase:*)", "Bash(ls:*)", "Bash(npm run lint)", "Bash(PGPASSWORD=\"29Ih0mK9kXpHAiup\" npx supabase db push --db-url \"postgresql://postgres.ddwkbtkbcnbsabepsfsd:<EMAIL>:6543/postgres\")", "Bash(PGPASSWORD=\"29Ih0mK9kXpHAiup\" npx supabase db pull --db-url \"postgresql://postgres.ddwkbtkbcnbsabepsfsd:<EMAIL>:6543/postgres\")", "Bash(PGPASSWORD=\"29Ih0mK9kXpHAiup\" npx supabase db push --include-all --db-url \"postgresql://postgres.ddwkbtkbcnbsabepsfsd:<EMAIL>:6543/postgres\")", "Bash(npm run lint:*)", "Bash(cp:*)", "Bash(rm:*)", "Bash(npm install:*)", "Bash(supabase migration repair:*)", "Bash(supabase db:*)", "Bash(git rm:*)", "Bash(git commit:*)", "Bash(git push:*)", "Bash(npx tsc:*)", "mcp__ide__getDiagnostics", "Bash(npx eslint:*)", "Bash(npm run:*)", "Bash(supabase:*)", "Bash(npm ls:*)", "Bash(gh pr create:*)", "WebFetch(domain:supabase.com)", "<PERSON><PERSON>(netlify:*)", "Bash(sudo rm:*)", "<PERSON><PERSON>(cat:*)", "Bash(git checkout:*)", "Bash(git merge:*)", "<PERSON><PERSON>(git stash show:*)", "Bash(git stash drop:*)"], "deny": []}}